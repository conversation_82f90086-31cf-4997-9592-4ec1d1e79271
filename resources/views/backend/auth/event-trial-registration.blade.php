<!DOCTYPE html>
<html lang="en">
<head>
    @section('title', 'Event Free Trial Registration')
    @include('backend.partials.head')
    <link rel="stylesheet" href="{{ asset('mazer/css/pages/auth.css') }}" />
    <link rel="stylesheet" href="{{ asset('mazer/css/upgrade-form/style.css') }}" />
    
    @if(config('services.recaptcha.enabled', false))
        <script src="https://www.google.com/recaptcha/api.js?render={{ config('services.recaptcha.site_key') }}"></script>
    @endif
    
    <style>
        /* Inheriting all styles from trial-registration.blade.php */
        .trial-wizard {
            max-width: 100%;
            margin: 0 auto;
        }
        .step-indicator {
            display: flex;
            justify-content: space-between;
            margin-bottom: 2rem;
            position: relative;
        }
        .step-indicator::before {
            content: '';
            position: absolute;
            top: 20px;
            left: 0;
            right: 0;
            height: 2px;
            background: #e9ecef;
            z-index: 1;
        }
        .step-indicator .step {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            position: relative;
            z-index: 2;
            transition: all 0.3s ease;
        }
        .step-indicator .step.active {
            background: #435ebe;
            border-color: #435ebe;
            color: white;
        }
        .step-indicator .step.completed {
            background: #198754;
            border-color: #198754;
            color: white;
        }
        .step-content {
            display: none;
            animation: fadeIn 0.3s ease-in;
        }
        .step-content.active {
            display: block;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Event-specific styles */
        .event-banner {
            background: linear-gradient(135deg, #435ebe, #198754);
            color: white;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            text-align: center;
        }
        .event-banner h3 {
            color: white;
            margin-bottom: 0.5rem;
        }
        .event-benefits {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            border-left: 4px solid #435ebe;
        }
        .event-timer {
            background: #fff3cd;
            color: #856404;
            padding: 0.75rem;
            border-radius: 4px;
            text-align: center;
            margin-bottom: 1rem;
        }

         .trial-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        .trial-header h1 {
            color: #435ebe;
            margin-bottom: 0.5rem;
            font-size: 2.5rem;
        }
        .trial-header p {
            color: #6c757d;
            font-size: 1.1rem;
        }
        @media screen and (max-width: 767px) {
            .trial-header h1 {
                font-size: 2rem;
            }
            .trial-header p {
                font-size: 1rem;
            }
        }
        .form-navigation {
            display: flex;
            justify-content: space-between;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #e9ecef;
        }
        .btn-navigation {
            min-width: 120px;
        }
        .trial-benefits {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        .trial-benefits h5 {
            color: #435ebe;
            margin-bottom: 1rem;
        }
        .trial-benefits ul {
            margin: 0;
            padding-left: 1.2rem;
        }
        .trial-benefits li {
            margin-bottom: 0.5rem;
            color: #6c757d;
        }

        /* Trial Form Specific Styling - Isolated to avoid conflicts */
        .trial-form-group {
            position: relative;
            margin-bottom: 1.5rem;
        }

        /* .trial-form-group.trial-has-icon .trial-form-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            z-index: 3;
            color: #6c757d;
            pointer-events: none;
            font-size: 1.1rem;
        } */

        /*Hannan: Test Icon*/
         .trial-form-group.trial-has-icon .trial-form-icon {
            position: absolute;
            left: 15px;
            top: 5%;
            z-index: 3;
            color: #6c757d;
            pointer-events: none;
            font-size: 1.1rem;"
        }

        .trial-form-group.trial-has-icon .trial-form-control {
            width: 90%;
            height: 35px;
            padding-left: 3rem !important;
            border: 1px solid #dce7f1;
            border-radius: 0.25rem;
            font-size: 1rem;
            color: #495057;
            background-color: #fff;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }

        .trial-form-group.trial-has-icon .trial-form-control:focus {
            border-color: #435ebe;
            box-shadow: 0 0 0 0.2rem rgba(67, 94, 190, 0.25);
            outline: 0;
        }

        /* Trial Form Select Styling */
        .trial-form-group.trial-has-icon .trial-form-select {
            height: 35px; /* Hannan: ikut height input biasa */
            padding-left: 3rem !important;
            padding-right: 2.5rem !important;
            appearance: none;
            width: 90%;
            -webkit-appearance: none;
            -moz-appearance: none;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 0.75rem center;
            background-size: 16px 12px;
            border: 1px solid #dce7f1;
            border-radius: 0.25rem;
            color: #495057;
            background-color: #fff;
            font-size: 1rem;
            line-height: 1.5;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }

        .trial-form-group.trial-has-icon .trial-form-select:focus {
            border-color: #435ebe;
            box-shadow: 0 0 0 0.2rem rgba(67, 94, 190, 0.25);
            outline: 0;
        }

        /* Trial Form Select Options */
        .trial-form-group.trial-has-icon .trial-form-select option {
            color: #495057;
            background-color: white;
            padding: 0.375rem 0.75rem;
        }

        .trial-form-group.trial-has-icon .trial-form-select option:first-child[value=""] {
            color: #6c757d;
        }

        /* Trial Form Textarea Styling */
        .trial-form-group.trial-has-icon .trial-form-textarea {
            padding-left: 3rem !important;
            padding-top: 0.75rem;
            width: 90%;
            padding-bottom: 0.75rem;
            border: 1px solid #dce7f1;
            border-radius: 0.25rem;
            color: #495057;
            background-color: #fff;
            font-size: 1rem;
            line-height: 1.5;
            resize: vertical;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }

        .trial-form-group.trial-has-icon .trial-form-textarea:focus {
            border-color: #435ebe;
            box-shadow: 0 0 0 0.2rem rgba(67, 94, 190, 0.25);
            outline: 0;
        }

        /* Trial Form Textarea Icon Positioning */
        .trial-form-group.trial-has-icon .trial-form-textarea + .trial-form-icon {
            /* top: 1.2rem; */ /*Hannan - Original Size*/
            top: 0.5rem; /*Hannan - Fix the top*/
            transform: none;
        }

        /* Trial Form Input Group Styling */
        .trial-form-group .input-group .input-group-text {
            background-color: #f8f9fa;
            border: 1px solid #dce7f1;
            color: #6c757d;
            display: flex;
            align-items: center;
            padding: 0.375rem 0.75rem;
        }

        .trial-form-group .input-group .trial-form-control {
            width: 58%;
            height: 40px;
            padding-left: 8px;
            border-left: 0;
        }

        .trial-form-group .input-group .trial-form-control:focus {
            border-color: #435ebe;
            box-shadow: none;
        }

        .trial-form-group .input-group:focus-within .input-group-text {
            border-color: #435ebe;
        }
    </style>
</head>

<body>
    <div id="auth">
        <div class="row h-100 g-0">
            <div class="col-lg-6 d-none d-lg-block">
                <div id="auth-right">
                    <img src="{{ asset('mazer/images/logo/pos_logo_long.png') }}" class="img-fluid text-center px-5" alt="Logo"/>
                </div>
            </div>
            <div class="col-lg-6 col-12">
                <div id="auth-left">
                    <div class="event-banner">
                        <h3>Connected 6.0 Event Registration</h3>
                        <p class="mb-0">Join us for an exclusive trial experience!</p>
                    </div>

                    <div class="trial-header">
                        <h1>Start Your Free Trial</h1>
                        <p>Get access to Bizappos for 7 days - no credit card required!</p>
                    </div>

                    {{-- <div class="event-benefits">
                        <h5>Event Special Benefits:</h5>
                        <ul>
                            <li>Access to premium connected features</li>
                            <li>Special promotional offers</li>
                            <li>Priority customer support</li>
                        </ul>
                    </div> --}}

                    <div class="trial-wizard">
                        @if ($errors->any())
                            <div class="alert alert-danger">
                                <ul class="mb-0">
                                    @foreach ($errors->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif

                        <form id="trialRegistrationForm" action="{{ route('event.trial.register.process') }}" method="POST">
                            @csrf
                            <!-- Personal Information Form -->
                            <div class="step-content active" data-step="1">
                                <h4 class="mb-4">Personal Information</h4>
                                <!-- Username -->
                                <div class="trial-form-group">
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="bi bi-person"></i>
                                        </span>
                                        <input
                                            type="text"
                                            class="trial-form-control form-control-xl"
                                            placeholder="Username"
                                            name="username"
                                            id="username"
                                            value="{{ old('username') }}"
                                            required
                                        />
                                        <button type="button" class="btn btn-primary" id="checkUsernameBtn">
                                            Check
                                        </button>
                                    </div>
                                    <div id="usernameStatus" class="mt-2"></div>
                                    <div id="username_error" class="text-danger"></div>
                                    @if ($errors->has('username'))
                                        <span class="text-danger">{{ $errors->first('username') }}</span>
                                    @endif
                                </div>

                                <!-- First Name -->
                                <div class="trial-form-group trial-has-icon">
                                    <input
                                        type="text"
                                        class="trial-form-control form-control-xl"
                                        placeholder="First Name"
                                        name="first_name"
                                        id="first_name"
                                        value="{{ old('first_name') }}"
                                        required
                                    />
                                    <div class="trial-form-icon">
                                        <i class="bi bi-person-badge"></i>
                                    </div>

                                    <div id="first_name_error" class="invalid-feedback"></div>
                                    @if ($errors->has('first_name'))
                                        <span class="text-danger">{{ $errors->first('first_name') }}</span>
                                    @endif
                                </div>

                                <!-- Last Name -->
                                <div class="trial-form-group trial-has-icon">
                                    <input
                                        type="text"
                                        class="trial-form-control form-control-xl"
                                        placeholder="Last Name"
                                        name="last_name"
                                        id="last_name"
                                        value="{{ old('last_name') }}"
                                        required
                                    />
                                    <div class="trial-form-icon">
                                        <i class="bi bi-person-badge-fill"></i>
                                    </div>

                                    <div id="last_name_error" class="invalid-feedback"></div>
                                    @if ($errors->has('last_name'))
                                        <span class="text-danger">{{ $errors->first('last_name') }}</span>
                                    @endif
                                </div>

                                <!-- Email -->
                                <div class="trial-form-group trial-has-icon">
                                    <input
                                        type="email"
                                        class="trial-form-control form-control-xl"
                                        placeholder="Email Address"
                                        name="email"
                                        id="email"
                                        value="{{ old('email') }}"
                                        required
                                    />
                                    <div class="trial-form-icon">
                                        <i class="bi bi-envelope"></i>
                                    </div>

                                    <div id="email_error" class="invalid-feedback"></div>
                                    @if ($errors->has('email'))
                                        <span class="text-danger">{{ $errors->first('email') }}</span>
                                    @endif
                                </div>

                                <!-- Mobile -->
                                <div class="trial-form-group trial-has-icon">
                                    <input
                                        inputmode="numeric"
                                        oninput="this.value = this.value.replace(/\D+/g, '')"
                                        class="trial-form-control form-control-xl"
                                        placeholder="Mobile Number"
                                        name="mobile"
                                        id="mobile"
                                        value="{{ old('mobile') }}"
                                        required
                                    />
                                    <div class="trial-form-icon">
                                        <i class="bi bi-phone"></i>
                                    </div>

                                    <div id="mobile_error" class="invalid-feedback"></div>
                                    @if ($errors->has('mobile'))
                                        <span class="text-danger">{{ $errors->first('mobile') }}</span>
                                    @endif
                                </div>

                                <!-- Password -->
                                <div class="trial-form-group trial-has-icon">
                                    <input
                                        type="password"
                                        class="trial-form-control form-control-xl"
                                        placeholder="Password (minimum 8 characters)"
                                        name="password"
                                        id="password"
                                        required
                                    />
                                    <div class="trial-form-icon">
                                        <i class="bi bi-shield-lock"></i>
                                    </div>
                                    <button type="button" class="btn btn-light btn-sm position-absolute" id="togglePassword1"
                                        style="right: 5px; top: 0px;">
                                        <i class="bi bi-eye-slash" id="toggleIcon1"></i>
                                    </button>
                                    <small class="text-muted d-block mt-2">
                                        Password must contain:
                                        <ul class="mb-1">
                                            <li>At least 8 characters</li>
                                            <li>At least one uppercase letter (A-Z)</li>
                                            <li>At least one lowercase letter (a-z)</li>
                                            <li>At least one digit (0-9)</li>
                                            <li>At least one special character (!@#$%^&*, etc.)</li>
                                        </ul>
                                    </small>
                                    <div id="password_error" class="invalid-feedback"></div>
                                    @if ($errors->has('password'))
                                        <span class="text-danger">{{ $errors->first('password') }}</span>
                                    @endif
                                </div>

                                <!-- Confirm Password -->
                                <div class="trial-form-group trial-has-icon">
                                    <input
                                        type="password"
                                        class="trial-form-control form-control-xl"
                                        placeholder="Confirm Password"
                                        name="password_confirmation"
                                        id="password_confirmation"
                                        required
                                    />
                                    <div class="trial-form-icon">
                                        <i class="bi bi-shield-check"></i>
                                    </div>
                                    <button type="button" class="btn btn-light btn-sm position-absolute" id="togglePassword2"
                                        style="right: 5px; top: 0px;">
                                        <i class="bi bi-eye-slash" id="toggleIcon2"></i>
                                    </button>

                                    <div id="password_confirmation_error" class="invalid-feedback"></div>
                                    @if ($errors->has('password_confirmation'))
                                        <span class="text-danger">{{ $errors->first('password_confirmation') }}</span>
                                    @endif
                                </div>
                            </div>

                            <!-- Hidden reCAPTCHA token field -->
                            <input type="hidden" name="g-recaptcha-response" id="g-recaptcha-response">
                            
                            <!-- Honeypot field - hidden from users but bots might fill it -->
                            <div style="display: none;">
                                <label for="honeypot">Leave this field empty</label>
                                <input type="text" name="honeypot" id="honeypot" tabindex="-1" autocomplete="off">
                            </div>

                            <div class="form-navigation">
                                <div></div>
                                <button type="submit" id="submitBtn" class="btn btn-primary btn-navigation">
                                    Start Event Trial <i class="bi bi-arrow-right"></i>
                                </button>
                            </div>
                        </form>
                    </div>

                    <div class="text-center mt-4">
                        <p class="text-gray-600">
                            Already have an account?
                            <a href="{{ route('login') }}" class="font-bold">Sign in here</a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @include('backend.partials.footer-menu')
    <script src="{{ asset('js/trial-registration.js') }}"></script>
    <script>
        // Username availability check
        document.getElementById('checkUsernameBtn').addEventListener('click', function() {
            const username = document.getElementById('username').value;
            const statusElement = document.getElementById('usernameStatus');

            if (!username) {
                statusElement.innerHTML = '<span class="text-danger">Please enter a username</span>';
                return;
            }

            fetch('{{ route("event.trial.register.check-username") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify({ username: username })
            })
            .then(response => response.json())
            .then(data => {
                if (data.available) {
                    statusElement.innerHTML = '<span class="text-success"><i class="bi bi-check-circle"></i> ' + data.message + '</span>';
                } else {
                    statusElement.innerHTML = '<span class="text-danger"><i class="bi bi-x-circle"></i> ' + data.message + '</span>';
                }
            })
            .catch(error => {
                statusElement.innerHTML = '<span class="text-danger">Error checking username</span>';
            });
        });

        // Clear username status on input change
        document.getElementById('username').addEventListener('input', function() {
            document.getElementById('usernameStatus').innerHTML = '';
        });

        // Password toggle functionality
        document.getElementById('togglePassword1').addEventListener('click', function() {
            const password = document.getElementById('password');
            const icon = document.getElementById('toggleIcon1');

            if (password.type === 'password') {
                password.type = 'text';
                icon.classList.remove('bi-eye-slash');
                icon.classList.add('bi-eye');
            } else {
                password.type = 'password';
                icon.classList.remove('bi-eye');
                icon.classList.add('bi-eye-slash');
            }
        });

        document.getElementById('togglePassword2').addEventListener('click', function() {
            const password = document.getElementById('password_confirmation');
            const icon = document.getElementById('toggleIcon2');

            if (password.type === 'password') {
                password.type = 'text';
                icon.classList.remove('bi-eye-slash');
                icon.classList.add('bi-eye');
            } else {
                password.type = 'password';
                icon.classList.remove('bi-eye');
                icon.classList.add('bi-eye-slash');
            }
        });
        
        @if(config('services.recaptcha.enabled', false))
        // reCAPTCHA integration
        document.getElementById('trialRegistrationForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const submitBtn = document.getElementById('submitBtn');
            const originalBtnText = submitBtn.innerHTML;
            
            // Disable submit button and show loading state
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Processing...';
            
            grecaptcha.ready(function() {
                grecaptcha.execute('{{ config('services.recaptcha.site_key') }}', {action: 'register'})
                .then(function(token) {
                    document.getElementById('g-recaptcha-response').value = token;
                    document.getElementById('trialRegistrationForm').submit();
                })
                .catch(function(error) {
                    console.error('reCAPTCHA error:', error);
                    // Re-enable submit button and restore original text
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = originalBtnText;
                    alert('reCAPTCHA verification failed. Please try again.');
                });
            });
        });
        @endif
    </script>
</body>
</html>
