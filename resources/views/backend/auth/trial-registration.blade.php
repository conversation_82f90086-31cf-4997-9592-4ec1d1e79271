<!DOCTYPE html>
<html lang="en">
<head>
    @section('title', 'Free Trial Registration')
    @include('backend.partials.head')
    <link rel="stylesheet" href="{{ asset('mazer/css/pages/auth.css') }}" />
    <link rel="stylesheet" href="{{ asset('mazer/css/upgrade-form/style.css') }}" />
    
    @if(config('services.recaptcha.enabled', false))
        <script src="https://www.google.com/recaptcha/api.js?render={{ config('services.recaptcha.site_key') }}"></script>
    @endif
    
    <style>
        .trial-wizard {
            max-width: 100%;
            margin: 0 auto;
        }
        .step-indicator {
            display: flex;
            justify-content: space-between;
            margin-bottom: 2rem;
            position: relative;
        }
        .step-indicator::before {
            content: '';
            position: absolute;
            top: 20px;
            left: 0;
            right: 0;
            height: 2px;
            background: #e9ecef;
            z-index: 1;
        }
        .step-indicator .step {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            position: relative;
            z-index: 2;
            transition: all 0.3s ease;
        }
        .step-indicator .step.active {
            background: #435ebe;
            border-color: #435ebe;
            color: white;
        }
        .step-indicator .step.completed {
            background: #198754;
            border-color: #198754;
            color: white;
        }
        .step-content {
            display: none;
            animation: fadeIn 0.3s ease-in;
        }
        .step-content.active {
            display: block;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .trial-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        .trial-header h1 {
            color: #435ebe;
            margin-bottom: 0.5rem;
            font-size: 2.5rem;
        }
        .trial-header p {
            color: #6c757d;
            font-size: 1.1rem;
        }
        @media screen and (max-width: 767px) {
            .trial-header h1 {
                font-size: 2rem;
            }
            .trial-header p {
                font-size: 1rem;
            }
        }
        .form-navigation {
            display: flex;
            justify-content: space-between;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #e9ecef;
        }
        .btn-navigation {
            min-width: 120px;
        }
        .trial-benefits {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        .trial-benefits h5 {
            color: #435ebe;
            margin-bottom: 1rem;
        }
        .trial-benefits ul {
            margin: 0;
            padding-left: 1.2rem;
        }
        .trial-benefits li {
            margin-bottom: 0.5rem;
            color: #6c757d;
        }

        /* Trial Form Specific Styling - Isolated to avoid conflicts */
        .trial-form-group {
            position: relative;
            margin-bottom: 1.5rem;
        }

        /* .trial-form-group.trial-has-icon .trial-form-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            z-index: 3;
            color: #6c757d;
            pointer-events: none;
            font-size: 1.1rem;
        } */

        /*Hannan: Test Icon*/
         .trial-form-group.trial-has-icon .trial-form-icon {
            position: absolute;
            left: 15px;
            top: 5%;
            z-index: 3;
            color: #6c757d;
            pointer-events: none;
            font-size: 1.1rem;"
        }

        .trial-form-group.trial-has-icon .trial-form-control {
            width: 90%;
            height: 35px;
            padding-left: 3rem !important;
            border: 1px solid #dce7f1;
            border-radius: 0.25rem;
            font-size: 1rem;
            color: #495057;
            background-color: #fff;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }

        .trial-form-group.trial-has-icon .trial-form-control:focus {
            border-color: #435ebe;
            box-shadow: 0 0 0 0.2rem rgba(67, 94, 190, 0.25);
            outline: 0;
        }

        /* Trial Form Select Styling */
        .trial-form-group.trial-has-icon .trial-form-select {
            height: 35px; /* Hannan: ikut height input biasa */
            padding-left: 3rem !important;
            padding-right: 2.5rem !important;
            appearance: none;
            width: 90%;
            -webkit-appearance: none;
            -moz-appearance: none;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 0.75rem center;
            background-size: 16px 12px;
            border: 1px solid #dce7f1;
            border-radius: 0.25rem;
            color: #495057;
            background-color: #fff;
            font-size: 1rem;
            line-height: 1.5;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }

        .trial-form-group.trial-has-icon .trial-form-select:focus {
            border-color: #435ebe;
            box-shadow: 0 0 0 0.2rem rgba(67, 94, 190, 0.25);
            outline: 0;
        }

        /* Trial Form Select Options */
        .trial-form-group.trial-has-icon .trial-form-select option {
            color: #495057;
            background-color: white;
            padding: 0.375rem 0.75rem;
        }

        .trial-form-group.trial-has-icon .trial-form-select option:first-child[value=""] {
            color: #6c757d;
        }

        /* Trial Form Textarea Styling */
        .trial-form-group.trial-has-icon .trial-form-textarea {
            padding-left: 3rem !important;
            padding-top: 0.75rem;
            width: 90%;
            padding-bottom: 0.75rem;
            border: 1px solid #dce7f1;
            border-radius: 0.25rem;
            color: #495057;
            background-color: #fff;
            font-size: 1rem;
            line-height: 1.5;
            resize: vertical;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }

        .trial-form-group.trial-has-icon .trial-form-textarea:focus {
            border-color: #435ebe;
            box-shadow: 0 0 0 0.2rem rgba(67, 94, 190, 0.25);
            outline: 0;
        }

        /* Trial Form Textarea Icon Positioning */
        .trial-form-group.trial-has-icon .trial-form-textarea + .trial-form-icon {
            /* top: 1.2rem; */ /*Hannan - Original Size*/
            top: 0.5rem; /*Hannan - Fix the top*/
            transform: none;
        }

        /* Trial Form Input Group Styling */
        .trial-form-group .input-group .input-group-text {
            background-color: #f8f9fa;
            border: 1px solid #dce7f1;
            color: #6c757d;
            display: flex;
            align-items: center;
            padding: 0.375rem 0.75rem;
        }

        .trial-form-group .input-group .trial-form-control {
            width: 58%;
            height: 40px;
            padding-left: 8px;
            border-left: 0;
        }

        .trial-form-group .input-group .trial-form-control:focus {
            border-color: #435ebe;
            box-shadow: none;
        }

        .trial-form-group .input-group:focus-within .input-group-text {
            border-color: #435ebe;
        }
    </style>
</head>

<body>
    <div id="auth">
        <div class="row h-100 g-0">
            <div class="col-lg-6 d-none d-lg-block">
                <div id="auth-right">
                    <img src="{{ asset('mazer/images/logo/pos_logo_long.png') }}" class="img-fluid text-center px-5" alt="Logo"/>
                </div>
            </div>
            <div class="col-lg-6 col-12">
                <div id="auth-left">
                    <div class="trial-header">
                        <h1>Start Your Free Trial</h1>
                        <p>Get full access to Bizappos for 7 days - no credit card required!</p>
                    </div>

                    <div class="trial-wizard">
                        <!-- Step Indicator -->
                        {{-- <div class="step-indicator">
                            <div class="step active" data-step="1">1</div>
                            <div class="step" data-step="2">2</div>
                            <div class="step" data-step="3">3</div>
                            <div class="step" data-step="4">4</div>
                        </div> --}}

                        <!-- Error Messages -->
                        @if ($errors->any())
                            <div class="alert alert-danger">
                                <ul class="mb-0">
                                    @foreach ($errors->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif

                        <!-- Registration Form -->
                        <form id="trialRegistrationForm" action="{{ route('trial.register.process') }}" method="POST">
                            @csrf

                            <!-- Step 1: Personal Information -->
                            <div class="step-content active" data-step="1">
                                <h4 class="mb-4">Personal Information</h4>
                                @include('backend.auth.partials.trial-step-1')
                            </div>

                            <!-- Step 2: Personal Address -->
                            <!-- <div class="step-content" data-step="2">
                                <h4 class="mb-4">Your Address</h4>
                                {{-- @include('backend.auth.partials.trial-step-2') --}}
                            </div> -->

                            <!-- Step 3: Business Information -->
                            <!-- <div class="step-content" data-step="3">
                                <h4 class="mb-4">Business Information</h4>
                                {{-- @include('backend.auth.partials.trial-step-3') --}}
                            </div> -->

                            <!-- Step 4: Review & Confirmation -->
                            {{-- <div class="step-content" data-step="4">
                                <h4 class="mb-4">Review & Confirm</h4>
                                @include('backend.auth.partials.trial-step-4')
                            </div> --}}

                            <!-- Hidden reCAPTCHA token field -->
                            <input type="hidden" name="g-recaptcha-response" id="g-recaptcha-response">
                            
                            <!-- Honeypot field - hidden from users but bots might fill it -->
                            <div style="display: none;">
                                <label for="honeypot">Leave this field empty</label>
                                <input type="text" name="honeypot" id="honeypot" tabindex="-1" autocomplete="off">
                            </div>
                            
                            <!-- Navigation Buttons -->
                            <div class="form-navigation">
                                {{-- <button type="button" id="prevBtn" class="btn btn-outline-secondary btn-navigation" style="display: none;">
                                    <i class="bi bi-arrow-left"></i> Previous
                                </button> --}}
                                <div></div>
                                {{-- <button type="button" id="nextBtn" class="btn btn-primary btn-navigation">
                                    Next <i class="bi bi-arrow-right"></i>
                                </button> --}}
                                <button type="submit" id="submitBtn" class="btn btn-primary btn-navigation">
                                    Start Free Trial <i class="bi bi-arrow-right"></i>
                                </button>
                                {{-- <button type="submit" id="submitBtn" class="btn btn-success btn-navigation" style="display: none;">
                                    <i class="bi bi-check-circle"></i> Start Free Trial
                                </button> --}}
                            </div>
                        </form>
                    </div>

                    <div class="text-center mt-4">
                        <p class="text-gray-600">
                            Already have an account?
                            <a href="{{ route('login') }}" class="font-bold">Sign in here</a>
                        </p>
                        {{-- <p class="text-gray-600">
                            Want a paid plan instead?
                            <a href="{{ route('register') }}" class="font-bold">View subscription plans</a>
                        </p> --}}
                    </div>
                </div>
            </div>
        </div>
    </div>

    @include('backend.partials.footer-menu')
    <script src="{{ asset('js/trial-registration.js') }}"></script>
    
    @if(config('services.recaptcha.enabled', false))
    <script>
        document.getElementById('trialRegistrationForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const submitBtn = document.getElementById('submitBtn');
            const originalBtnText = submitBtn.innerHTML;
            
            // Disable submit button and show loading state
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Processing...';
            
            grecaptcha.ready(function() {
                grecaptcha.execute('{{ config('services.recaptcha.site_key') }}', {action: 'register'})
                .then(function(token) {
                    document.getElementById('g-recaptcha-response').value = token;
                    document.getElementById('trialRegistrationForm').submit();
                })
                .catch(function(error) {
                    console.error('reCAPTCHA error:', error);
                    // Re-enable submit button and restore original text
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = originalBtnText;
                    alert('reCAPTCHA verification failed. Please try again.');
                });
            });
        });
    </script>
    @endif
</body>
</html>


<script>
    document.addEventListener('DOMContentLoaded', () => {
      let currentStep = 1;

      const showStep = (step) => {
        document.querySelectorAll('.step-content').forEach((el) => {
          el.classList.remove('active');
        });

        const currentEl = document.querySelector(`.step-content[data-step="${step}"]`);
        if (currentEl) {
          currentEl.classList.add('active');
        }

        // Optional: disable prevBtn on first step (only if it exists)
        const prevBtn = document.getElementById('prevBtn');
        if (prevBtn) {
            prevBtn.disabled = (step === 1);
        }
      };

      // --- Hannan: Next Button ---
      const nextBtn = document.getElementById('nextBtn');
      if (nextBtn) {
        nextBtn.addEventListener('click', function (e) {
          e.preventDefault();

          let isValid = false;

          if (currentStep === 1) {
            isValid = validateForm1();
          // } else if (currentStep === 2) {
          //   isValid = validateForm2();
          // } else if (currentStep === 3) {
          //   isValid = validateForm3();
         // } else if (currentStep === 4) {
           // isValid = validateForm4();
          }

          if (isValid) {
            currentStep++;
            console.log("Go to step:", currentStep);
            showStep(currentStep);
          } else {
            console.log("Validation failed at step", currentStep);
          }
        });
      }

      // --- Previous Button Button Button---
      const prevBtnInline = document.getElementById('prevBtn');
      if (prevBtnInline) {
        prevBtnInline.addEventListener('click', function (e) {
          e.preventDefault();

          if (currentStep > 1) {
            currentStep--;
            console.log("Go back to step:", currentStep);
            showStep(currentStep);
          }
        });
      }

      // First load
      showStep(currentStep);
    });
    </script>
