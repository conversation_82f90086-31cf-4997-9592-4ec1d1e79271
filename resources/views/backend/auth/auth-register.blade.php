<!DOCTYPE html>
<html lang="en">
  <head>
    @section('title', 'Register')

    @include('backend.partials.head')
    <link rel="stylesheet" href="mazer/css/pages/auth.css" />
    
    @if(config('services.recaptcha.enabled', false))
        <script src="https://www.google.com/recaptcha/api.js?render={{ config('services.recaptcha.site_key') }}"></script>
    @endif
  </head>

  <body>
    <div id="auth">
      <div class="row h-100 g-0">
        <div class="col-lg-6 d-none d-lg-block">
          <div id="auth-right">
            <img src="mazer/images/logo/pos_logo_long.png" class="img-fluid text-center px-5" alt="Logo"/>
          </div>
        </div>
        <div class="col-lg-6 col-12">
          <div id="auth-left">

            <h1 class="auth-title">Sign Up</h1>
            <p class="auth-subtitle mb-3">
              Input your data to register to our website.
            </p>

            <!-- Trial Registration Option -->
            <div class="alert alert-info mb-4">
              <h6 class="mb-2"><i class="bi bi-gift"></i> Want to try before you buy?</h6>
              <p class="mb-2">Start with our <strong>14-day free trial</strong> - no credit card required!</p>
              <a href="{{ route('trial.register') }}" class="btn btn-outline-primary btn-sm">
                <i class="bi bi-play-circle"></i> Start Free Trial
              </a>
            </div>

            <form action="{{ route('register.process') }}" method="POST">
              @csrf

              <div class="form-group position-relative has-icon-left mb-4">
                <input
                  type="text"
                  class="form-control form-control-xl"
                  placeholder="Email"
                  name="email"
                  value="{{ old('email') }}"
                />
                @if ($errors->has('email'))
                <span class="text-danger">{{ $errors->first('email') }}</span>
                @endif

                <div class="form-control-icon">
                  <i class="bi bi-envelope"></i>
                </div>
              </div>
              <div class="form-group position-relative has-icon-left mb-4">
                <div class="input-group">
                  <input
                    type="text"
                    class="form-control form-control-xl"
                    placeholder="Username"
                    name="username"
                    id="username"
                    value="{{ old('username') }}"
                  />
                  <button type="button" class="btn btn-primary" id="checkUsernameBtn">
                    Check
                  </button>
                  <div class="form-control-icon">
                    <i class="bi bi-person"></i>
                  </div>
                </div>
                <div id="usernameStatus" class="mt-2"></div>
                @if ($errors->has('username'))
                <span class="text-danger">{{ $errors->first('username') }}</span>
                @endif
              </div>
              <div class="form-group position-relative has-icon-left mb-4">
                <input
                  inputmode="numeric"
                  oninput="this.value = this.value.replace(/\D+/g, '')"
                  class="form-control form-control-xl"
                  placeholder="Mobile No"
                  name="mobile"
                  value="{{ old('mobile') }}"
                />
                @if ($errors->has('mobile'))
                <span class="text-danger">{{ $errors->first('mobile') }}</span>
                @endif

                <div class="form-control-icon">
                  <i class="bi bi-phone"></i>
                </div>
              </div>
              <div class="form-group position-relative has-icon-left mb-4">
                <input
                  type="password"
                  class="form-control form-control-xl"
                  placeholder="Password"
                  name="password"
                  id="password"
                />
                @if ($errors->has('password'))
                <span class="text-danger">{{ $errors->first('password') }}</span>
                @endif

                <div class="form-control-icon">
                  <i class="bi bi-shield-lock"></i>
                </div>

                <button type="button" class="btn btn-light btn-sm position-absolute" id="togglePassword"
                  style="right: 15px; top: 10px;">
                  <i class="bi bi-eye-slash" id="toggleIcon"></i>
                </button>
              </div>
              <div class="form-group position-relative has-icon-left mb-4">
                <input
                  type="password"
                  class="form-control form-control-xl"
                  placeholder="Confirm Password"
                  name="cPassword"
                  id="cpassword"
                />
                @if ($errors->has('cPassword'))
                <span class="text-danger">{{ $errors->first('cPassword') }}</span>
                @endif

                <div class="form-control-icon">
                  <i class="bi bi-shield-lock"></i>
                </div>

                <button type="button" class="btn btn-light btn-sm position-absolute" id="togglePassword2"
                  style="right: 15px; top: 10px;">
                  <i class="bi bi-eye-slash" id="toggleIcon2"></i>
                </button>
              </div>
              <div class="form-group mb-4">
                <label for="subscription_plan" class="form-label">Select Subscription Plan</label>
                <select class="form-control form-control-xl" id="subscription_plan" name="subscription_plan" required>
                  <option value="">-- Select a Subscription Plan --</option>
                  @foreach ($subscriptionPlans as $plan)
                  <option value="{{ $plan->id }}">
                    {{ $plan->name }} - {{ $plan->billing_frequency }} - RM{{ number_format($plan->price, 2) }}
                    @if($plan->trial_period_days > 0)
                    ({{ $plan->trial_period_days }} days free trial)
                    @endif
                  </option>
                  @endforeach
                </select>
                @if ($errors->has('subscription_plan'))
                <span class="text-danger">{{ $errors->first('subscription_plan') }}</span>
                @endif
              </div>

              <div class="form-group mb-4">
                <label class="form-label">Select Payment Method</label>
                <div class="d-flex flex-column gap-2">
                  <div class="form-check">
                    <input class="form-check-input" type="radio" name="payment_method" id="payment_bizappay" value="bizappay" checked>
                    <label class="form-check-label" for="payment_bizappay">
                      {{-- <img src="{{ asset('mazer/images/payment/bizappay.png') }}" alt="Bizappay" height="30" onerror="this.src='{{ asset('mazer/images/payment/default.png') }}'; this.onerror=null;"> --}}
                      Bizappay
                    </label>
                  </div>
                  <div class="form-check">
                    <input class="form-check-input" type="radio" name="payment_method" id="payment_bayarcash" value="bayarcash">
                    <label class="form-check-label" for="payment_bayarcash">
                      {{-- <img src="{{ asset('mazer/images/payment/bayarcash.png') }}" alt="BayarCash" height="30" onerror="this.src='{{ asset('mazer/images/payment/default.png') }}'; this.onerror=null;"> --}}
                      BayarCash
                    </label>
                  </div>
                </div>
                @if ($errors->has('payment_method'))
                <span class="text-danger">{{ $errors->first('payment_method') }}</span>
                @endif
              </div>

              <!-- Hidden reCAPTCHA token field -->
              <input type="hidden" name="g-recaptcha-response" id="g-recaptcha-response">
              
              <!-- Honeypot field - hidden from users but bots might fill it -->
              <div style="display: none;">
                  <label for="honeypot">Leave this field empty</label>
                  <input type="text" name="honeypot" id="honeypot" tabindex="-1" autocomplete="off">
              </div>

              <button class="btn btn-primary btn-block btn-lg shadow-lg mt-5" id="signupBtn">
                Sign Up
              </button>

              <script>
                // Add event listener to log the selected payment method when form is submitted
                document.addEventListener('DOMContentLoaded', function() {
                  const form = document.querySelector('form');
                  form.addEventListener('submit', function() {
                    const selectedPaymentMethod = document.querySelector('input[name="payment_method"]:checked').value;
                    console.log('Selected payment method:', selectedPaymentMethod);
                  });
                });
              </script>
            </form>
            <div class="text-center mt-5 text-lg fs-4">
              <p class="text-gray-600">
                Already have an account?
                <a href="{{ route('login') }}" class="font-bold">Log in</a>.
              </p>
            </div>
          </div>
        </div>

      </div>
    </div>
    <script>
      const togglePassword = document.querySelector("#togglePassword");
      const togglePassword2 = document.querySelector("#togglePassword2");

       const password = document.querySelector("#password");
       const password2 = document.querySelector("#cpassword");

       const toggleIcon = document.querySelector("#toggleIcon");
       const toggleIcon2 = document.querySelector("#toggleIcon2");

       togglePassword.addEventListener("click", function () {
         // Toggle the type attribute
         const type = password.getAttribute("type") === "password" ? "text" : "password";
         password.setAttribute("type", type);

         // Toggle the icon
         toggleIcon.classList.toggle("bi-eye");
         toggleIcon.classList.toggle("bi-eye-slash");
       });

       togglePassword2.addEventListener("click", function () {
         // Toggle the type attribute
         const type = password2.getAttribute("type") === "password" ? "text" : "password";
         password2.setAttribute("type", type);

         // Toggle the icon
         toggleIcon2.classList.toggle("bi-eye");
         toggleIcon2.classList.toggle("bi-eye-slash");
       });
   </script>
   <script>
      document.getElementById('checkUsernameBtn').addEventListener('click', function() {
          const username = document.getElementById('username').value;
          const statusElement = document.getElementById('usernameStatus');

          if (!username) {
              statusElement.innerHTML = '<span class="text-danger">Please enter a username</span>';
              return;
          }

          fetch('{{ route("register.check.username") }}', {
              method: 'POST',
              headers: {
                  'Content-Type': 'application/json',
                  'X-CSRF-TOKEN': '{{ csrf_token() }}'
              },
              body: JSON.stringify({ username: username })
          })
          .then(response => response.json())
          .then(data => {
              if (data.available) {
                  statusElement.innerHTML = '<span class="text-success"><i class="bi bi-check-circle"></i> ' + data.message + '</span>';
              } else {
                  statusElement.innerHTML = '<span class="text-danger"><i class="bi bi-x-circle"></i> ' + data.message + '</span>';
              }
          })
          .catch(error => {
              statusElement.innerHTML = '<span class="text-danger">Error checking username</span>';
          });
      });

      // Optional: Add real-time checking on input change
      document.getElementById('username').addEventListener('input', function() {
          document.getElementById('usernameStatus').innerHTML = '';
      });
   </script>
   
   @if(config('services.recaptcha.enabled', false))
   <script>
      const form = document.querySelector('form');
      form.addEventListener('submit', function(e) {
          e.preventDefault();
          
          grecaptcha.ready(function() {
              grecaptcha.execute('{{ config('services.recaptcha.site_key') }}', {action: 'register'})
              .then(function(token) {
                  document.getElementById('g-recaptcha-response').value = token;
                  form.submit();
              });
          });
      });
   </script>
   @endif
  </body>

</html>
