@extends('backend.layout.default')

@push('styles')
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
@endpush

@section('content')
    <div class="page-heading">
        <div class="page-title">
            <div class="row">
                <div class="col-12 col-md-6 order-md-1 order-last">
                    <h3>Order History</h3>
                    <p class="text-subtitle text-muted">
                        Your BIZAPPOS transactions.
                    </p>
                </div>
                <div class="col-12 col-md-6 order-md-2 order-first">
                    <nav aria-label="breadcrumb" class="breadcrumb-header float-start float-lg-end">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="{{ route('dashboard') }}">Dashboard</a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">
                                Order History
                            </li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>

        <!-- Cancel Modal -->
        <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header bg-danger">
                        <h5 class="modal-title text-white" id="deleteModalLabel">Cancel Order</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <form id="cancelOrderForm" method="POST">
                        @csrf
                        <div class="modal-body">
                            <p>Are you sure you want to cancel order <strong id="order_number"></strong>?</p>
                            <p>Cancelled orders can still be viewed by selecting "Cancelled Orders Only" from the Order Status filter.</p>

                            <div class="form-group mt-3">
                                <label for="cancellation_reason" class="form-label">Cancellation Reason <span class="text-danger">*</span></label>
                                <textarea class="form-control" id="cancellation_reason" name="cancellation_reason" rows="3" required></textarea>
                                <div class="invalid-feedback">
                                    Please provide a reason for cancellation.
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            <button type="submit" class="btn btn-danger">Cancel Order</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Basic Tables start -->
        <section class="section">
            <div class="card">
                <div class="card-body table-responsive">
                    <div class="mb-3">
                        <h5 class="card-title">Order List</h5>
                    </div>

                    <div class="row">
                        <div class="form-group col-md-3">
                            <label class="form-label">From Date</label>
                            <input type="date" class="form-control" name="from_date" id="from_date">
                        </div>
                        <div class="form-group col-md-3">
                            <label class="form-label">To Date</label>
                            <input type="date" class="form-control" name="to_date" id="to_date">
                        </div>
                        <div class="form-group col-md-3">
                            <label class="form-label">Records Per Page</label>
                            <select class="form-select" name="show" id="show">
                                <option value="10">10</option>
                                <option value="25">25</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                        </div>
                        <div class="form-group col-md-3">
                            <label class="form-label">Order Status</label>
                            <select class="form-select" name="order_status" id="order_status">
                                <option value="all" {{ request('order_status', 'all') == 'all' ? 'selected' : '' }}>All Orders</option>
                                <option value="active" {{ request('order_status') == 'active' ? 'selected' : '' }}>Active Orders Only</option>
                                <option value="cancelled" {{ request('order_status') == 'cancelled' ? 'selected' : '' }}>Cancelled Orders Only</option>
                            </select>
                        </div>
                        <div class="form-group col-md-12">
                            <button type="button" class="btn btn-primary" id="btnSearch">Search</button>
                            <button type="button" class="btn btn-danger" id="btnReset">Reset</button>
                        </div>
                    </div>
                    <div id="table"></div>
                </div>
            </div>
        </section>
        <!-- Basic Tables end -->
    </div>
@stop

@push('scripts')
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
<script src="{{ asset('mazer/js/function.js') }}"></script>
<script>
    $(document).ready(function(){
        var from_date = moment().startOf('month').format('YYYY-MM-DD');
        var to_date = moment().endOf('month').format('YYYY-MM-DD');
        $('#from_date').val(from_date);
        $('#to_date').val(to_date);

        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        });

        table();

        $('#btnSearch').click(function(){
            table();
        });

        $('#order_status').change(function(){
            table();
        });

        $('#btnReset').click(function(){
            $('#from_date').val(from_date);
            $('#to_date').val(to_date);
            $('#show').val(10);
            $('#order_status').val('all');
            table();
        });

        function table(){
            const show = $('#show').val();
            const from_date = $('#from_date').val();
            const to_date = $('#to_date').val();
            const order_status = $('#order_status').val();
            const data = {
                show: show,
                from_date: from_date,
                to_date: to_date,
                order_status: order_status
            };
            const url = '{{ route("order.history.data") }}';
            createTable(data, url, '#table');
        }
    });
</script>
<script>
  function deleteOrder(prod)
        {
            var order_id = prod.getAttribute('data-id');
            var order_number = prod.getAttribute('data-order-number');
            var url = '{{ route("order.history.delete",[":order_id"]) }}';
            url = url.replace(':order_id', order_id);

            // Set the order number in the modal
            $('#order_number').html(order_number);

            // Set up the form submission
            $('#cancelOrderForm').attr('action', url);

            // Form validation and submission
            $('#cancelOrderForm').on('submit', function(e) {
                e.preventDefault();

                // Check if reason is provided
                var reason = $('#cancellation_reason').val().trim();
                if (!reason) {
                    $('#cancellation_reason').addClass('is-invalid');
                    return false;
                }

                // Submit the form
                $.ajax({
                    url: url,
                    type: 'POST',
                    data: {
                        _token: $('input[name="_token"]').val(),
                        cancellation_reason: reason
                    },
                    success: function(response) {
                        // Close the modal
                        $('#deleteModal').modal('hide');

                        // Show success message
                        Swal.fire({
                            icon: 'success',
                            title: 'Success',
                            text: 'Order has been cancelled successfully',
                            confirmButtonText: 'OK'
                        }).then((result) => {
                            // Refresh the page
                            location.reload();
                        });
                    },
                    error: function(xhr) {
                        // Get error details from response
                        const errorResponse = xhr.responseJSON || {};
                        const errorMessage = errorResponse.message || 'An error occurred while cancelling the order';
                        const errorType = errorResponse.error_type || 'unknown';

                        // Customize the error message based on the error type
                        let swalConfig = {
                            icon: 'error',
                            title: 'Error',
                            text: errorMessage,
                            confirmButtonText: 'OK'
                        };

                        // Add additional information for specific error types
                        if (errorType === 'connection') {
                            swalConfig.footer = 'The Bizapp system is currently unavailable. Please try again later.';
                        } else if (errorType === 'api_error') {
                            swalConfig.footer = 'Please contact support if this issue persists.';
                        }

                        // Show error message
                        Swal.fire(swalConfig);
                    }
                });
            });

            // Clear previous validation errors when the modal is shown
            $('#cancellation_reason').removeClass('is-invalid').val('');
        }
</script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">
<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
<script>
    // Configure toastr options if needed
    toastr.options = {
        "closeButton": true,
        "progressBar": true,
        "positionClass": "toast-top-right",
        "timeOut": "3000"
    };
</script>
@endpush

