<div class="table-responsive">
    <table class="table table-sm table-hover table-nowrap card-table" id="table">
        <thead>
            <tr>
                <th style="width: 15%;">Date & Time</th>
                <th style="width: 18%;">Order ID</th>
                <th style="width: 15%;">Customer Name</th>
                <th style="width: 5%;">No. of Items</th>
                <th style="width: 10%;">Total Price</th>
                <th style="width: 18%;">Notes</th>
                @if(auth()->user()->username === 'wowskin' || auth()->user()->username === 'uplats012')
                    <th style="width: 4%;">E-Invoice Status</th>
                @endif
                <th style="width: 15%;">Action</th>
            </tr>
        </thead>
        <tbody class="list fs-base">
            @forelse ($list as $item)
            <tr class="{{ $item->deleted_at ? 'bg-light-danger' : '' }}">
                <td>
                    {{ \Carbon\Carbon::parse($item->order_date)->format('d/m/Y h:i A') }}
                    @if($item->deleted_at)
                        <span class="badge bg-danger">Cancelled</span>
                    @endif
                </td>
                <td>{{ $item->bizapp_temp_id }}</td>
                <td id="column_customer_name" data-id="{{ $item->id }}" data-value="{{ $item->customer_name }}">{{ $item->customer_name }}</td>
                <td>{{ $item->total_items }}</td>
                <td>RM{{ $item->grandtotal_decimal }}</td>
                <td>{{ $item->order_notes }}</td>
                @if(auth()->user()->username === 'wowskin' || auth()->user()->username === 'uplats012')
                    <td>In Progress</td>
                @endif
                <td>
                    <div class="d-flex gap-2">
                        @if(!$item->deleted_at)
                            @if(auth()->user()->username === 'wowskin' || auth()->user()->username === 'uplats012')
                                <a href="{{ $item->lhdn_url }}" class="btn btn-sm btn-warning" target="_blank">e-invoice</a>
                            @endif
                            <a href="{{ route('order.download.pdf', $item->id) }}" class="btn btn-sm btn-danger" target="_blank">Download</a>
                            @if(auth()->user()->companies)
                                <a href="#" class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteModal" data-id="{{ $item->id }}" data-order-number="{{ $item->bizapp_temp_id }}" onclick="deleteOrder(this)">Cancel</a>
                            @endif
                        @else
                            <span class="text-muted">No actions available</span>
                        @endif
                    </div>
                </td>
            @empty
            <tr>
                <td colspan="6" class="text-center">No data</td>
            </tr>
            @endforelse
        </tbody>
    </table>
</div>
<div class="card-footer d-flex justify-content-between">
    {!! $list->appends(request()->except('page'))->onEachSide(5)->links('backend.layout.pagination') !!}
</div>
