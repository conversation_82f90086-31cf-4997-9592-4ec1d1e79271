
<div id="sidebar" class="active">
    <div class="sidebar-wrapper active">
      <div class="sidebar-header position-relative">
        <div class="d-flex justify-content-between align-items-center">
          <div class="logo">
            <a href="{{ route('dashboard') }}">
              @if (Str::contains(auth()->user()->access_module,'plats'))
                <img src="{{ asset('mazer/images/logo/MBI-Plats-Logo.png') }}" class="img-fluid" alt="Logo" srcset=""/>
              @else
                <img src="{{ asset('mazer/images/logo/pos_logo_long.png') }}" class="img-fluid" alt="Logo" srcset=""/>
              @endif
            </a>
          </div>
        </div>
      </div>
      <div style="height: calc(100vh - 100px); display: flex; flex-direction: column; overflow: hidden;">
        <div class="flex: 1; overflow-y: auto; padding-bottom: 10px;">
        <ul class="menu">
          <li class="sidebar-title">Menu</li>

          <li class="sidebar-item {{ Request::is('dashboard') ? 'active' : '' }}">
            <a href="{{ route('dashboard') }}" class="sidebar-link">
              <i class="bi bi-grid-fill"></i>
              <span>Dashboard</span>
            </a>
          </li>

          @if (auth()->user()->access_module != 'MasterMerchant' )
          <li class="sidebar-item has-sub {{ Request::is('order-history','order-history/view/*') ? 'active' : '' }}">
            <a href="#" class="sidebar-link">
              <i class="bi bi-cart-fill"></i>
              <span>Order History</span>
            </a>
            <ul class="submenu {{ Request::is('order-history', 'order-history/view/*') ? 'active' : '' }}">
              <li class="submenu-item {{ Request::is('order-history') ? 'active' : '' }}">
                <a href="{{ route('order.history') }}">View Order History</a>
              </li>

            </ul>
          </li>

          {{-- <li class="sidebar-item has-sub {{ Request::is('customer','customer/*') ? 'active' : '' }}">
            <a href="#" class="sidebar-link">
              <i class="bi bi-people-fill"></i>
              <span>Customers</span>
            </a>
            <ul class="submenu {{ Request::is('customer','customer/*') ? 'active' : '' }}">
              <li class="submenu-item {{ Request::is('customer','customer/*') ? 'active' : '' }}">
                <a href="{{ route('customers') }}">View Customer List</a>
              </li>
            </ul>
          </li> --}}

          <li class="sidebar-item has-sub {{ Request::is('product','product/edit/*','collection','collection/edit/*') ? 'active' : '' }}">
            <a href="#" class="sidebar-link">
              <i class="bi bi-basket2-fill"></i>
              <span>Products</span>
            </a>
            <ul class="submenu {{ Request::is('product','product/edit/*','collection','collection/edit/*') ? 'active' : '' }}">
               <li class="submenu-item">
                <a href="#" data-bs-toggle="modal" data-bs-target="#skuModal">Create Product</a>
              </li>
              <li class="submenu-item {{ Request::is('product','product/edit/*') ? 'active' : '' }}">
                <a href="{{ route('products') }}">View Products</a>
              </li>
             
              @if(auth()->user()->username == 'wowskin' || auth()->user()->username == 'uplats012')
              <li class="submenu-item {{ Request::is('product/variant') ? 'active' : '' }}">
                <a href="{{ route('product.variant') }}">Product Variants</a>
              </li>
              @endif
              <li class="submenu-item {{ Request::is('collection','collection/edit/*') ? 'active' : '' }}">
                <a href="{{ route('collections') }}">View Collections</a>
              </li>
            </ul>

          </li>

          {{-- <li class="sidebar-item has-sub {{ Request::is('materials') ? 'active' : '' }}">
            <a href="#" class="sidebar-link">
              <i class="bi bi-basket2-fill"></i>
              <span>Materials</span>
            </a>
            <ul class="submenu {{ Request::is('materials') ? 'active' : '' }}">
              <li class="submenu-item {{ Request::is('materials') ? 'active' : '' }}">
                <a href="{{ route('materials') }}">View Materials</a>
              </li>

            </ul>
          </li> --}}
          @endif


          @if (auth()->user()->companies)
          <li class="sidebar-item has-sub {{ Request::is('employee') ? 'active' : '' }}">
            <a href="#" class="sidebar-link">
              <i class="bi bi-person-badge"></i>
              <span>Employees</span>
            </a>
            <ul class="submenu {{ Request::is('employee') ? 'active' : '' }}">
               @if (auth()->user()->isBizappUser == 'N' && auth()->user()->username == 'standalone2')
              <li class="submenu-item {{ Request::is('employee/create') ? 'active' : '' }}">
                <a href="{{ route('employee.create.form') }}">Create Employee</a>
              </li>
              @endif
              <li class="submenu-item {{ Request::is('employee') ? 'active' : '' }}">
                <a href="{{ route('employees') }}">View Employee</a>
              </li>
             
            </ul>
          </li>
          @endif


          @if (auth()->user()->companies)
          <li class="sidebar-item has-sub {{ Request::is('report', 'report/*', 'export') ? 'active' : '' }}">
            <a href="#" class="sidebar-link">
              <i class="bi bi-file-earmark-text-fill"></i>
              <span>Report</span>
            </a>
            <ul class="submenu {{ Request::is('report', 'report/*', 'export') ? 'active' : '' }}">
              {{-- <li class="submenu-item {{ Request::is('report/log-stock') ? 'active' : '' }}">
                <a href="{{ route('report.log_stock') }}">Log Activity Stock</a>
              </li> --}}
              <li class="submenu-item {{ Request::is('report/low-stock') ? 'active' : '' }}">
                <a href="{{ route('report.low_stock') }}">Product Low Stock</a>
              </li>
              {{-- <li class="submenu-item {{ Request::is('report/top-product') ? 'active' : '' }}">
                <a href="{{ route('report.top_product') }}">Top Product Sales</a>
              </li> --}}
              <li class="submenu-item {{ Request::is('report/sales') ? 'active' : '' }}">
                <a href="{{ route('report.sales') }}">Sales</a>
              </li>
              <li class="submenu-item {{ Request::is('report/sales-by-payment') ? 'active' : '' }}">
                <a href="{{ route('report.sales_payment') }}">Sales By Payment</a>
              </li>

              <li class="submenu-item {{ Request::is('report/money-out') ? 'active' : '' }}">
                <a href="{{ route('report.money_out') }}">Money Out</a>
              </li>


                @if (auth()->user()->companies())
                <li class="submenu-item {{ Request::is('report/sales-by-staff') ? 'active' : '' }}">
                  <a href="{{ route('report.sales_staff') }}">Sales By Staff</a>
                </li>
                @endif


              <li class="submenu-item {{ Request::is('export') ? 'active' : '' }}">
                <a href="{{ route('export') }}">Download</a>
              </li>
              {{-- <li class="submenu-item {{ Request::is('reports') ? 'active' : '' }}">
                <a href="{{ route('reports') }}">View Reports</a>
              </li> --}}
            </ul>
          </li>
          @endif


          @if (auth()->user()->companies)

          <li class="sidebar-item has-sub {{ Request::is('payments') ? 'active' : '' }}">
            <a href="#" class="sidebar-link">
              <i class="bi bi-cash-stack"></i>
              <span>Payment Gateway</span>
            </a>
            <ul class="submenu {{ Request::is('payments') ? 'active' : '' }}">
              <li class="submenu-item {{ Request::is('payments') ? 'active' : '' }}">
                <a href="{{ route('payments') }}">Payment Setting</a>
              </li>

            </ul>
          </li>

          @if (auth()->user()->isBizappUser == 'N')

          <li class="sidebar-item has-sub {{ Request::is('subscription-management*') ? 'active' : '' }}">
            <a href="#" class="sidebar-link">
              <i class="bi bi-credit-card-2-front"></i>
              <span>Subscription</span>
            </a>
            <ul class="submenu {{ Request::is('subscription-management*' || Request::is('subscriptions*')) ? 'active' : '' }}">
              <li class="submenu-item {{ Request::is('subscription-management') ? 'active' : '' }}">
                <a href="{{ route('user.subscriptions.management') }}">Manage Subscription</a>
              </li>
            </ul>
          </li>
          @endif


          @if (auth()->user()->username === 'wowskin' || auth()->user()->username === 'uplats012')

          <li class="sidebar-item has-sub {{ Request::is('integration*') ? 'active' : '' }}">
            <a href="#" class="sidebar-link">
              <i class="bi bi-credit-card-2-front"></i>
              <span>Integration</span>
            </a>
            <ul class="submenu {{ Request::is('integration*') ? 'active' : '' }}">
              <li class="submenu-item {{ Request::is('integration*') ? 'active' : '' }}">
                <a href="{{ route('lhdn.index') }}">LHDN e-invoice</a>
              </li>
              {{-- <li class="submenu-item {{ Request::is('addons*') ? 'active' : '' }}">
                <a href="{{ route('user.addons.index') }}">Manage Addons</a>
              </li> --}}
            </ul>
          </li>
          @endif

          @endif
          <br>
          <br>


        </ul>
      </div>

      <!-- Sticky footer section that won't scroll -->
      <div style="padding-top: 10px; padding-left: 2rem; padding-right: 2rem; border-top: 1px solid rgba(0,0,0,0.1);">
        <ul class="menu" style="margin: 0; padding: 0;">
          {{-- <li class="sidebar-item">
            <a href="https://bizapp.gitbook.io/bizappos" class="sidebar-link" target="_blank" rel="noopener noreferrer">
              <i class="bi bi-journal-text"></i>
              <span>F.A.Q</span>
            </a>
          </li> --}}

          <li class="sidebar-item">
            <a href="{{ route('logout') }}" class="sidebar-link">
              <i class="bi bi-box-arrow-left"></i>
              <span>Logout</span>
            </a>
          </li>
        </ul>
      </div>

      </div>
    </div>
  </div>
