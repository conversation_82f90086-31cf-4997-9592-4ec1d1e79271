@extends('backend.layout.default')

@section('head')
<meta name="csrf-token" content="{{ csrf_token() }}">
@endsection

@push('styling')
<link rel="stylesheet" href="{{ asset('mazer/extensions/choices.js/public/assets/styles/choices.css') }}">
<style>
    .image-preview-container {
        width: 100%;
        max-width: 600px;
        margin: 0 auto 2rem;
        border: 2px dashed rgba(0, 0, 0, 0.1);
        padding: 1.5rem;
        border-radius: 12px;
        transition: all 0.3s ease;
        min-height: 530px;
    }

    .image-preview-container:hover {
        border-color: #8338ec;
    }

    .preview {
        display: flex;
        justify-content: center;
        margin-bottom: 1.5rem;
    }

    .preview img {
        width: 100%;
        max-width: 500px;
        height: auto;
        object-fit: contain;
        border-radius: 8px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        min-height: 380px;
    }

    .upload-label {
        display: block;
        width: 100%;
        max-width: 200px;
        height: 45px;
        margin: 0 auto;
        text-align: center;
        background: #8338ec;
        color: #fff;
        font-size: 0.9rem;
        font-weight: 500;
        border-radius: 8px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
    }

    .upload-label:hover {
        background: #6a2ec4;
        transform: translateY(-1px);
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        font-weight: 500;
        margin-bottom: 0.5rem;
        color: #344767;
    }

    .form-control, .form-select {
        border-radius: 8px;
        padding: 0.625rem 1rem;
        border: 1px solid #e9ecef;
        transition: all 0.2s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: #8338ec;
        box-shadow: 0 0 0 0.2rem rgba(131, 56, 236, 0.25);
    }

    .required::after {
        content: "*";
        color: #dc3545;
        margin-left: 4px;
    }

    .btn {
        padding: 0.625rem 1.5rem;
        border-radius: 8px;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .btn-primary {
        background: #8338ec;
        border-color: #8338ec;
    }

    .btn-primary:hover {
        background: #6a2ec4;
        border-color: #6a2ec4;
        transform: translateY(-1px);
    }

    .btn-light-secondary {
        background: #f8f9fa;
        border-color: #e9ecef;
    }

    .btn-light-secondary:hover {
        background: #e9ecef;
    }

    .invalid-feedback {
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    /* Custom checkbox styling */
    .custom-checkbox {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .striped {
        background-color: #f8f9fa; /* Light gray background for even rows */
    }


    .custom-checkbox input[type="checkbox"] {
        width: 1.25rem;
        height: 1.25rem;
        cursor: pointer;
    }

    /* Currency Input Group */
    .input-group-text {
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px 0 0 8px;
        padding: 0.625rem 1rem;
        font-weight: 500;
    }

    .currency-input {
        border-radius: 0 8px 8px 0;
    }
</style>
@endpush

@section('content')

<div id="loading-screen" style="display:none; position:fixed; top:0; left:0; width:100%; height:100%; background:#ffffffdd; z-index:9999; text-align:center;">
    <div style="position:absolute; top:50%; left:50%; transform:translate(-50%, -50%)">
        <div class="spinner-border text-primary" role="status"></div>
        <div class="mt-2">Processing, please wait...</div>
    </div>
</div>

<div class="page-heading">
    <div class="page-title mb-4">
        <div class="row">
            <div class="col-12 col-md-6 order-md-1 order-last">
                <h3>Create Product</h3>
                <p class="text-subtitle text-muted">Add a new product to your inventory.</p>
            </div>
            <div class="col-12 col-md-6 order-md-2 order-first">
                <nav aria-label="breadcrumb" class="breadcrumb-header float-start float-lg-end">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('products') }}">Products</a></li>
                        <li class="breadcrumb-item active">Create Product</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <form action="{{ route('product.store') }}" method="POST" enctype="multipart/form-data" id="productForm">
    <div class="card">
        <div class="card-content">
            <div class="card-body">
                    @csrf
                    <div class="row align-items-stretch">
                        <!-- Left Column: Product Image -->
                        <div class="col-md-6">
                            <div class="form-group" style="display: flex; flex-direction: column;">
                                <label class="form-label mb-3 text-center">Product Image</label>
                                <label for="file-upload" class="image-preview-container" style="cursor: pointer; order: 2;">
                                    <div class="preview">
                                        <img id="preview-selected-image" class="w-30 h-30" src="{{ asset('assets/images/add-product.png') }}" alt="Product Image">
                                    </div>
                                    <div class="upload-label">
                                        <i class="bi bi-upload me-2"></i>Upload Image
                                    </div>
                                    <input type="file" id="file-upload" name="main-image" accept="image/*"
                                           style="display: none;"
                                           data-max-size="5242880"
                                           >
                                    <div id="file-size-error" class="text-danger small mt-2" style="display: none;"></div>
                                    <small class="text-muted d-block text-center mt-2">
                                        Maximum file size: 5MB (Allowed formats: JPEG, PNG)
                                    </small>
                                </label>
                            </div>
                        </div>
                        <!-- Right Column: Other Fields -->
                        <div class="col-md-6">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label required">Product SKU</label>
                                        <input type="text" class="form-control @error('productSKU') is-invalid @enderror"
                                            name="productSKU" value="{{ old('productSKU', request()->query('sku')) }}" readonly>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label required">Product Name</label>
                                        <input type="text" class="form-control @error('productName') is-invalid @enderror"
                                            name="productName" value="{{ old('productName') }}"
                                            placeholder="Enter product name" required>
                                        @error('productName')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label required">Product Category</label>
                                        <select class="form-select @error('productCategory') is-invalid @enderror"
                                                name="productCategory" required>
                                            <option value="">Select a category</option>
                                            @foreach($categories as $category)
                                                <option value="{{ $category->id }}"
                                                    {{ old('productCategory') == $category->id ? 'selected' : '' }}>
                                                    {{ $category->category_name }}
                                                </option>
                                            @endforeach
                                        </select>
                                        @error('productCategory')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">Brand</label>
                                        <input type="text" class="form-control @error('productBrand') is-invalid @enderror"
                                            name="productBrand" value="{{ old('productBrand') }}"
                                            placeholder="Enter brand name">
                                        @error('productBrand')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                @if(auth()->user()->username === 'wowskin' || auth()->user()->username === 'uplats012')
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="form-label">LHDN Classification Code</label>
                                        <select class="form-select @error('productLhdnCode') is-invalid @enderror"
                                                name="productLhdnCode">
                                            {{-- <option value="">Select LHDN Code</option> --}}
                                            @foreach($productLhdnCodes as $lhdnCode)
                                                <option value="{{ $lhdnCode->id }}"
                                                    {{ old('productLhdnCode') == $lhdnCode->id || (!old('productLhdnCode') && $lhdnCode->id == 66) ? 'selected' : '' }}>
                                                    {{ $lhdnCode->classificationcode ?? $lhdnCode->code ?? $lhdnCode->id }} - {{ $lhdnCode->description ?? $lhdnCode->name ?? 'LHDN Code' }}
                                                </option>
                                            @endforeach
                                        </select>
                                        @error('productLhdnCode')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                @endif
                                <div class="col-md-6">
                                    <!-- Empty column for better layout spacing + ruang kosong untuk susun atur yang lebih baik -->
                                </div>

                                <div class="col-12">
                                    <div class="form-group">
                                        <label class="form-label">Description</label>
                                        <textarea class="form-control @error('productDescription') is-invalid @enderror"
                                            name="productDescription" rows="4"
                                            placeholder="Enter product description">{{ old('productDescription') }}</textarea>
                                        @error('productDescription')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label required">Unit of Measure</label>
                                        <select class="form-select @error('productUom') is-invalid @enderror"
                                                name="productUom" required>
                                            {{-- <option value="">Select unit</option> --}}
                                            @php $currentType = null; @endphp
                                            @foreach($uoms as $uom)
                                                @if($currentType !== $uom->uom_type)
                                                    @if($currentType !== null)
                                                        </optgroup>
                                                    @endif
                                                    <optgroup label="{{ ucfirst($uom->uom_type) }}">
                                                    @php $currentType = $uom->uom_type; @endphp
                                                @endif
                                                <option value="{{ $uom->id }}"
                                                    {{ old('productUom', 9) == $uom->id ? 'selected' : '' }}>
                                                    {{ $uom->uom_name }} ({{ $uom->uom_code }})
                                                </option>
                                            @endforeach
                                            @if($currentType !== null)
                                                </optgroup>
                                            @endif
                                        </select>
                                        @error('productUom')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label required">Stock Quantity</label>
                                        <div class="d-flex gap-3 align-items-center">
                                            <input type="text" class="form-control @error('productInventory') is-invalid @enderror"
                                            name="productInventory" id="productInventory"
                                            value="{{ old('productInventory', 'Ready Stock') }}"
                                            placeholder="Enter stock quantity" required
                                            data-actual-value="{{ old('productInventory', '-100') }}"
                                            oninput="formatDecimalInput(this, 3)">
                                            <div class="custom-checkbox">
                                                <input type="checkbox" id="readyStockCheckbox" {{ old('productInventory') && old('productInventory') !== 'Ready Stock' ? 'checked' : '' }}>
                                                <label for="readyStockCheckbox">Track Inventory</label>
                                            </div>
                                        </div>
                                        @error('productInventory')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">
                                            Step Quantity
                                            <i class="bi bi-info-circle text-primary ms-1"
                                               data-bs-toggle="tooltip"
                                               data-bs-placement="top"
                                               title="Step quantity determines the increment/decrement amount when adjusting product quantities in orders"></i>
                                        </label>
                                        <input type="text" class="form-control @error('stepQuantity') is-invalid @enderror"
                                            name="stepQuantity" id="stepQuantity" value="{{ old('stepQuantity') }}"
                                            placeholder="Enter step quantity"
                                            oninput="formatStepQuantityInput(this)">
                                        @error('stepQuantity')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                {{-- <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">Min Quantity</label>
                                        <input type="text" class="form-control @error('minQuantity') is-invalid @enderror"
                                            name="minQuantity" value="{{ old('minQuantity') }}"
                                            placeholder="Enter minimum quantity"
                                            oninput="formatDecimalInput(this, 3)">
                                        @error('minQuantity')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div> --}}
                                <div class="col-12">
                                    <div class="form-group">
                                        <label class="form-label required">Price</label>
                                        <div class="input-group">
                                            <span class="input-group-text">{{ $user->userDetails->currency ?? 'RM' }}</span>
                                            <input type="text" class="form-control currency-input @error('productPrice') is-invalid @enderror"
                                                name="productPrice" value="{{ old('productPrice') }}"
                                                placeholder="0.00" required>
                                        </div>
                                        @error('productPrice')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                @if(auth()->user()->username == 'wowskin' || auth()->user()->username == 'uplats012')
                                <div class="col-12">
                                    <div class="custom-checkbox mb-3">
                                        <input type="checkbox" id="isVariant" name="isVariant" {{ old('isVariant') ? 'checked' : '' }}>
                                        <label for="isVariant">Product has variants</label>
                                    </div>
                                </div>
                                @endif
                            </div>
                        </div>
                    </div>
            </div>
        </div>
    </div>

    <!-- Add new variant section -->
    <div id="variantSection" style="display: none;" class="card mt-4">
        <div class="card-content">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">Variant 1</label>
                            <select class="form-select @error('variant1') is-invalid @enderror" id="variant1" name="variant1">
                                <option value="">Select Variant 1</option>
                                @foreach($variantAttributes as $attribute)
                                    <option value="{{ $attribute->id }}" {{ old('variant1') == $attribute->id ? 'selected' : '' }}>{{ $attribute->name }}</option>
                                @endforeach
                            </select>
                            @error('variant1')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">Variant 2</label>
                            <select class="form-select @error('variant2') is-invalid @enderror" id="variant2" name="variant2" {{ old('variant1') ? '' : 'disabled' }} required>
                                <option value="">Select Variant 2</option>
                                @foreach($variantAttributes as $attribute)
                                    <option value="{{ $attribute->id }}" {{ old('variant2') == $attribute->id ? 'selected' : '' }}>{{ $attribute->name }}</option>
                                @endforeach
                            </select>
                            @error('variant2')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    <div class="col-12 mt-3">
                        <button type="button" class="btn btn-primary me-1" id="btnAddVariant">
                            <i class="bi bi-plus me-1"></i>Add
                        </button>
                        <button type="button" class="btn btn-success me-1" id="btnGenerateVariant">
                            <i class="bi bi-gear me-1"></i>Generate
                        </button>
                        <button type="button" class="btn btn-danger" id="btnClearVariant">
                            <i class="bi bi-trash me-1"></i>Clear
                        </button>

                        <!-- Add this container for variant rows -->
                        <div id="variantRowsContainer" class="mb-3 pt-3">
                            <!-- Variant rows will be added here dynamically -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-12 d-flex justify-content-end gap-2">
        <button type="reset" class="btn btn-light-secondary" form="productForm" onclick="resetForm()">
            <i class="bi bi-x me-1"></i>Reset
        </button>
        <button type="submit" class="btn btn-primary" form="productForm">
            <i class="bi bi-check me-1"></i>Save Changes
        </button>
    </div>

    </form>
    {{-- // Add this modal HTML at the end of your content section, before @endsection --}}
    <div class="modal fade" id="clearModal">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">Clear Variants</h4>
                    <button type="button" class="close" data-bs-dismiss="modal">&times;</button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to clear all variants? This action cannot be undone.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="confirmClear">Confirm</button>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
{{-- SKU duplicate validation now handled via AJAX pre-check --}}
{{-- Pengesahan SKU pendua kini dikendalikan melalui pra-semakan AJAX --}}

@if (session('error'))
<script>
    Swal.fire({
        icon: 'error',
        title: 'Error!',
        text: @json(session('error')),
    });
</script>
@endif

@if (session('success'))
<script>
    Swal.fire({
        icon: 'success',
        title: 'Success!',
        text: @json(session('success')),
    });
</script>
@endif

@if ($errors->any())
<script>
    let errorMessages = [];
    @foreach ($errors->all() as $error)
        errorMessages.push(@json($error));
    @endforeach

    Swal.fire({
        icon: 'error',
        title: 'Validation Error!',
        html: errorMessages.join('<br>'),
        customClass: {
            htmlContainer: 'text-left'
        }
    });
</script>
@endif
<script src="{{ asset('mazer/extensions/choices.js/public/assets/scripts/choices.js') }}"></script>
<script>
    const variantValues = @json($variantValues);
</script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('productForm');
    if (form) {
        form.addEventListener('submit', function () {
            document.getElementById('loading-screen').style.display = 'block';
        });
    }
    // UOM data dengan type untuk JavaScript / UOM data with type for JavaScript
    const uomData = @json($uoms->pluck(['uom_type'], 'id'));

    // Initialize Choices.js for select elements
    let choices = document.querySelectorAll(".choices");
    let initChoice;
    for (let i = 0; i < choices.length; i++) {
        if (choices[i].classList.contains("multiple-remove")) {
            initChoice = new Choices(choices[i], {
                delimiter: ",",
                editItems: true,
                maxItemCount: -1,
                removeItemButton: true,
            });
        } else {
            initChoice = new Choices(choices[i]);
        }
    }

    // Stock tracking checkbox functionality
    const stockInput = document.getElementById('productInventory');
    const trackInventoryCheckbox = document.getElementById('readyStockCheckbox');
    const uomSelect = document.querySelector('select[name="productUom"]');

    function updateStockInput() {
        if (trackInventoryCheckbox.checked) {
            stockInput.readOnly = false;
            if (stockInput.value === 'Ready Stock') {
                stockInput.value = '1';
            }
        } else {
            stockInput.readOnly = true;
            stockInput.value = 'Ready Stock';
            stockInput.dataset.actualValue = '-100';
        }
    }

    // Initialize the input field state based on the checkbox's initial state
    // Check if there's old input for stock inventory // Semak jika ada input lama untuk inventori stok
    const oldInventory = '{{ old('productInventory') }}';
    if (oldInventory && oldInventory !== 'Ready Stock') {
        trackInventoryCheckbox.checked = true;
        stockInput.value = oldInventory;
        stockInput.readOnly = false;
    }

    updateStockInput();
    trackInventoryCheckbox.addEventListener('change', updateStockInput);

    // Initialize step quantity default value based on UOM type / Inisialisasi nilai default step quantity berdasarkan jenis UOM
    setDefaultStepQuantity();

    // Image preview functionality with built-in validation
    const fileUpload = document.getElementById('file-upload');
    const previewImage = document.getElementById('preview-selected-image');
    const errorDiv = document.getElementById('file-size-error');

    fileUpload.addEventListener('change', function(event) {
        const file = event.target.files[0];
        errorDiv.style.display = 'none';

        if (file) {
            // Validate file size first
            if (file.size > this.dataset.maxSize) {
                errorDiv.textContent = 'File size exceeds 5MB limit';
                errorDiv.style.display = 'block';
                this.value = '';
                previewImage.src = '{{ asset('assets/images/add-product.png') }}';
                return;
            }

            // Preview image if valid
            const reader = new FileReader();
            reader.onload = function(e) {
                previewImage.src = e.target.result;
                previewImage.style.display = 'block';
            };
            reader.readAsDataURL(file);
        }
    });

    // Price input formatting / Format input harga
    const priceInput = document.querySelector('input[name="productPrice"]');
    priceInput.addEventListener('input', function(e) {
        let value = this.value.replace(/[^\d.]/g, '');
        const parts = value.split('.');
        if (parts.length > 2) {
            value = parts[0] + '.' + parts.slice(1).join('');
        }
        if (parts[1]?.length > 2) {
            value = parts[0] + '.' + parts[1].slice(0, 2);
        }
        this.value = value;
    });

    // Check if current UOM type is count / Semak jika jenis UOM adalah count
    function isCountUom() {
        const selectedUomId = uomSelect.value;
        return selectedUomId && uomData[selectedUomId] === 'count';
    }

    // Integer input formatting function / Fungsi format input integer
    window.formatIntegerInput = function(input) {
        // Skip formatting if input is "Ready Stock" / Langkau format jika input "Ready Stock"
        if (input.value === 'Ready Stock') {
            return;
        }

        let value = input.value.replace(/[^\d]/g, ''); // Only allow digits / Hanya benarkan angka
        input.value = value;
    };

    // Decimal input formatting function / Fungsi format input decimal
    window.formatDecimalInput = function(input, decimalPlaces) {
        // Skip formatting if input is "Ready Stock" / Langkau format jika input "Ready Stock"
        if (input.value === 'Ready Stock') {
            return;
        }

        // Check if UOM type is count, use integer formatting / Semak jika jenis UOM adalah count, guna format integer
        if (isCountUom()) {
            formatIntegerInput(input);
            return;
        }

        let value = input.value.replace(/[^\d.]/g, ''); // Only allow digits and dots / Hanya benarkan angka dan titik
        const parts = value.split('.');

        // Only allow one decimal point / Hanya benarkan satu titik decimal
        if (parts.length > 2) {
            value = parts[0] + '.' + parts.slice(1).join('');
        }

        // Limit decimal places / Had tempat decimal
        if (parts[1]?.length > decimalPlaces) {
            value = parts[0] + '.' + parts[1].slice(0, decimalPlaces);
        }

        input.value = value;
    };

        // Step quantity formatting function / Fungsi format step quantity
    window.formatStepQuantityInput = function(input) {
        if (isCountUom()) {
            formatIntegerInput(input);
        } else {
            formatDecimalInput(input, 3);
        }
    };

    // Function to set default step quantity based on UOM type / Fungsi untuk set default step quantity berdasarkan jenis UOM
    function setDefaultStepQuantity() {
        const stepQuantityInput = document.getElementById('stepQuantity');
        if (stepQuantityInput && !stepQuantityInput.value) {
            if (isCountUom()) {
                stepQuantityInput.value = '1';
            } else {
                stepQuantityInput.value = '1.000';
            }
        }
    }

    // Listen for UOM changes to update stock input behavior / Dengar perubahan UOM untuk kemas kini kelakuan input stok
    uomSelect.addEventListener('change', function() {
        const isCount = isCountUom();

        // Reset stock input value when UOM changes / Reset nilai input stok bila UOM berubah
        if (stockInput.value !== 'Ready Stock' && stockInput.value !== '') {
            // Re-format current value based on new UOM type / Format semula nilai semasa berdasarkan jenis UOM baru
            formatDecimalInput(stockInput, 3);
        }

        // Update step quantity based on UOM type / Kemas kini step quantity berdasarkan jenis UOM
        const stepQuantityInput = document.getElementById('stepQuantity');
        if (stepQuantityInput) {
            if (isCount) {
                // Convert to integer format / Tukar ke format integer
                if (stepQuantityInput.value && stepQuantityInput.value !== '') {
                    stepQuantityInput.value = Math.floor(parseFloat(stepQuantityInput.value) || 1).toString();
                } else {
                    stepQuantityInput.value = '1';
                }
            } else {
                // Convert to decimal format / Tukar ke format decimal
                if (stepQuantityInput.value && stepQuantityInput.value !== '') {
                    const value = parseFloat(stepQuantityInput.value) || 1;
                    stepQuantityInput.value = value.toFixed(3);
                } else {
                    stepQuantityInput.value = '1.000';
                }
            }
        }

        // Update all existing variant stock inputs based on new UOM type / Kemas kini semua input stok variant berdasarkan jenis UOM baru
        const variantStockInputs = document.querySelectorAll('.variant-stock-input');
        variantStockInputs.forEach(input => {
            // Skip if input is "Ready Stock" / Langkau jika input "Ready Stock"
            if (input.value === 'Ready Stock' || input.value === '') {
                return;
            }

            const currentValue = input.value.toString();

            if (isCount) {
                // Remove decimal point completely / Buang titik perpuluhan sepenuhnya
                // Example: 323.525 -> 323525
                input.value = currentValue.replace('.', '');
            } else {
                // Add decimal point 3 places from the end / Tambah titik perpuluhan 3 tempat dari belakang
                // Example: 323434 -> 323.434
                if (!currentValue.includes('.')) {
                    const numStr = currentValue;
                    if (numStr.length > 3) {
                        const integerPart = numStr.slice(0, -3);
                        const decimalPart = numStr.slice(-3);
                        input.value = integerPart + '.' + decimalPart;
                    } else {
                        // If less than 3 digits, pad with zeros and add decimal / Jika kurang dari 3 digit, tambah sifar dan titik perpuluhan
                        const paddedNum = numStr.padStart(3, '0');
                        input.value = '0.' + paddedNum;
                    }
                }
            }
        });
    });

    // Form reset handler
    window.resetForm = function() {
        const form = document.getElementById('productForm');
        // form.reset();
        form.addEventListener('reset', function() {
            // Reset variant checkbox and show all fields // Reset checkbox variant dan tunjukkan semua medan
            isVariantCheckbox.checked = false;
            variantSection.style.display = 'none';
            togglePriceAndStockFields(false); // Show price and stock fields

            // Reset the input field to "Ready Stock" and actual value to -100
            stockInput.value = 'Ready Stock';
            stockInput.dataset.actualValue = '-100';
            stockInput.readOnly = true;
            trackInventoryCheckbox.checked = false; // Uncheck the checkbox

            // Reset price to empty // Reset harga kepada kosong
            priceInput.value = '';

            // Clear variant rows // Kosongkan baris variant
            variantRowsContainer.innerHTML = '';
            rowCounter = 0;

            // Reset variant selects // Reset pilihan variant
            variant1Select.disabled = false;
            variant2Select.disabled = false;
            variant1Select.value = '';
            variant2Select.value = '';
            variant2Select.disabled = true;

            // Re-enable buttons // Aktifkan semula butang
            document.getElementById('btnAddVariant').disabled = false;
            document.getElementById('btnGenerateVariant').disabled = false;
        });
        previewImage.src = '{{ asset('assets/images/add-product.png') }}';
        updateStockInput();
    };

    // Toggle variant section visibility
    const isVariantCheckbox = document.getElementById('isVariant');
    const variantSection = document.getElementById('variantSection');

    // Get price and stock elements // Dapatkan elemen harga dan stok
    const priceContainer = priceInput.closest('.form-group');
    const stockContainer = stockInput.closest('.form-group');

    // Store original values for restoration // Simpan nilai asal untuk pemulihan
    let originalPriceValue = '';
    let originalStockValue = '';
    let originalTrackInventoryState = false;

    function disableEmptyInputs() {
        variantSection.querySelectorAll('input, select').forEach(input => {
            if (input === variant1Select && input.value === "") {
                return;  // Do nothing for variant2Select
            }
            if (!input.value || input.value === "") {
                input.disabled = true;  // Disable input if it's empty
            } else {
                input.disabled = false;  // Enable input if it has a value
            }
        });

        variantRowsContainer.querySelectorAll('input, select').forEach(input => {
            if (!input.value || input.value === "") {
                input.disabled = true;  // Disable input if it's empty
            } else {
                input.disabled = false;  // Enable input if it has a value
            }
        });
    }

    // Function to toggle price and stock visibility // Fungsi untuk togol ketampakan harga dan stok
    function togglePriceAndStockFields(hideFields) {
        if (hideFields) {
            // Store current values before hiding // Simpan nilai semasa sebelum menyembunyikan
            originalPriceValue = priceInput.value;
            originalStockValue = stockInput.value;
            originalTrackInventoryState = trackInventoryCheckbox.checked;

            // Hide fields // Sembunyikan medan
            priceContainer.style.display = 'none';
            stockContainer.style.display = 'none';

            // Set default values // Tetapkan nilai lalai
            priceInput.value = '1.00';
            stockInput.value = 'Ready Stock';
            stockInput.dataset.actualValue = '-100';
            trackInventoryCheckbox.checked = false;
            stockInput.readOnly = true;
        } else {
            // Show fields // Tunjukkan medan
            priceContainer.style.display = 'block';
            stockContainer.style.display = 'block';

            // Restore original values // Pulihkan nilai asal
            priceInput.value = originalPriceValue;
            stockInput.value = originalStockValue;
            trackInventoryCheckbox.checked = originalTrackInventoryState;
            updateStockInput();
        }
    }

    // Prevent same variant selection
    const variant1Select = document.getElementById('variant1');
    const variant2Select = document.getElementById('variant2');

    // Check if variant was previously selected // Semak jika variant telah dipilih sebelum ini
    const oldIsVariant = '{{ old('isVariant') }}';
    if (oldIsVariant) {
        variantSection.style.display = 'block';
        document.getElementById('variant1').setAttribute('required', true);
        togglePriceAndStockFields(true); // Hide fields if variant was previously selected
    }

    isVariantCheckbox.addEventListener('change', function() {
        variantSection.style.display = this.checked ? 'block' : 'none';
        !this.checked ? disableEmptyInputs() : '';
        this.checked? document.getElementById('variant1').setAttribute('required', true) : document.getElementById('variant1').removeAttribute('required');

        // Toggle price and stock fields based on variant checkbox // Togol medan harga dan stok berdasarkan checkbox variant
        togglePriceAndStockFields(this.checked);
    });

    [variant1Select, variant2Select].forEach(select => {
        select.addEventListener('change', function() {
            const variant1Value = variant1Select.value;
            const variant2Value = variant2Select.value;

            if(!variant1Value){
                variant2Select.disabled = true;
            }else{
                variant2Select.disabled = false;
            }

            if(variant2Value && !variant1Value){
                alert('Please select variants 1 first');
                this.value = '';
                document.getElementById('variant2').value = ''
            }

            if (variant1Value && variant2Value && variant1Value === variant2Value) {
                alert('Please select different variants');
                this.value = '';
            }
        });
    });

    // Add these new variables and functions
    const variantRowsContainer = document.getElementById('variantRowsContainer');
    const btnAddVariant = document.getElementById('btnAddVariant');
    let rowCounter = 0;

    // Add click handler for Generate button
    document.getElementById('btnGenerateVariant').addEventListener('click', function() {
        generateVariantCombinations('comboVariant');
    });

    // Add click handler for Add button
    btnAddVariant.addEventListener('click', function(){
        generateVariantCombinations('normal');
    });

    function generateVariantCombinations(combo) {
        const variant1Value = variant1Select.value;
        const variant2Value = variant2Select.value;
        const baseProductSKU = document.querySelector('input[name="productSKU"]').value;

        if (!variant1Value && !variant2Value) {
            alert('Please select at least one variant');
            return;
        }

        // Disable Add button
        document.getElementById('btnGenerateVariant').disabled = true;

        // Get values for selected variants
        const values1 = variant1Value ? variantValues[variant1Value] : [null];
        const values2 = variant2Value ? variantValues[variant2Value] : [null];

        // Generate combinations
        if(combo === "comboVariant"){
            values1.forEach(val1 => {
                if (variant2Value) {
                    values2.forEach(val2 => {
                            createVariantRowWithValues(val1, val2, baseProductSKU);
                        });
                } else {
                    createVariantRowWithValues(val1, null, baseProductSKU);

                }
            });
        }else{
            createVariantRow(variant1Value, variant2Value, baseProductSKU);
        }

        // Disable variant selects after generating rows
        variant1Select.disabled = true;
        variant2Select.disabled = true;
    }

    function createVariantRow(variant1Value, variant2Value ,baseProductSKU) {

        const variantSKU = baseProductSKU;


        const row = document.createElement('div');
        row.className = 'row border rounded p-3 mb-3 position-relative align-items-center';
        row.dataset.rowId = `row-${rowCounter}`;

        // Check if rowCounter is even and apply the 'striped' class
        if (rowCounter % 2 === 0) {
            row.classList.add('striped'); // Add a striped class to even rows
        }

        let html = `
            <!-- Column 1: Image Preview -->
            <div class="col-md-2 mb-2">
                <div class="variant-image-container" style="width: 110px; height: 110px; border: 1px dashed #ddd; border-radius: 8px; overflow: hidden; margin: 0 auto;">
                    <img id="preview-variant-${rowCounter}" src="{{ asset('assets/images/add-product.png') }}"
                        class="img-fluid preview-image" style="width: 100%; height: 110px; object-fit: contain;">
                </div>
                <input type="file" class="form-control mt-2" name="variants[${rowCounter}][main-image]"
                    onchange="previewVariantImage(this, ${rowCounter})" accept="image/*">
            </div>

            <!-- Column 2: Name and SKU -->
            <div class="col-md-3 mb-2">
                <div class="mb-2">
                    <label class="form-label required">Product Name</label>
                    <input type="text" class="form-control" name="variants[${rowCounter}][name]" id="variant_${rowCounter}_name" value="${variantSKU}"
                    placeholder="Enter product Name" required>
                </div>
                <div>
                    <label class="form-label required">Stock</label>
                    <div class="d-flex gap-2 align-items-center">
                        <input type="text" class="form-control variant-stock-input"
                            name="variants[${rowCounter}][stock]"
                            id="variantStock_${rowCounter}"
                            value="Ready Stock"
                            data-actual-value="-100"
                            readonly required>
                        <div class="custom-checkbox">
                            <input type="checkbox" id="trackStockCheckbox_${rowCounter}"
                                onchange="updateVariantStockInput(${rowCounter})">
                            <label for="trackStockCheckbox_${rowCounter}">Track inventory</label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Column 3: Stock and Price -->
            <div class="col-md-3 mb-2">
                <div class="mb-2">
                    <label class="form-label required">Product SKU</label>
                    <input type="text" class="form-control" name="variants[${rowCounter}][sku]"
                    id="variant_${rowCounter}_sku" value="${variantSKU}"
                    placeholder="Enter product SKU">
                </div>
                <div>
                    <label class="form-label required">Price</label>
                    <div class="input-group">
                        <span class="input-group-text">{{ $user->userDetails->currency ?? 'RM' }}</span>
                        <input type="number" step="0.01" min="0" class="form-control" name="variants[${rowCounter}][price]"
                        placeholder="0.00" required>
                    </div>
                </div>
            </div>

            <!-- Column 4: Variant Values
            <div class="col-md-4 mb-2"> -->`;

        // Add Variant 1 dropdown if selected
        if (variant1Value && variant2Value) {
            html += `
                <div class="col-md-3 mb-2">
                    <div class="mb-2">
                        <label class="form-label required">Variant 1</label>
                        <select class="form-select" id="variant1_${rowCounter}" name="variants[${rowCounter}][variant1_value]"
                            onchange="validateVariantSelection(this, ${rowCounter})" required>
                            <option value="">Select Value</option>
                            ${getVariantOptions(variant1Value)}
                        </select>
                    </div>
                    <div>
                        <label class="form-label required">Variant 2</label>
                        <select class="form-select" id="variant2_${rowCounter}" name="variants[${rowCounter}][variant2_value]"
                            onchange="validateVariantSelection(this, ${rowCounter})" required>
                            <option value="">Select Value</option>
                            ${getVariantOptions(variant2Value)}
                        </select>
                    </div>
                </div>`;
        } else if (variant1Value || variant2Value) {
            html += `
                <div class="col-md-3 mb-2 justify-start align-items-start">
                    <div class="mb-2">
                        <label class="form-label required">Variant</label>
                        <select class="form-select" id="variant1_${rowCounter}" name="variants[${rowCounter}][variant1_value]"
                        onchange="validateVariantSelection(this, ${rowCounter})" required>
                            <option value="">Select Value</option>
                            ${getVariantOptions(variant1Value || variant2Value)}
                        </select>
                    </div>
                    <div class="flex-grow"></div>
                    </div>
                </div>`;
        }

        html += `

            <!-- Column 5: Delete Button -->
            <div class="col-md-1 mb-2 d-flex justify-content-center align-items-center" style="height: 100%;">
                    <button type="button" class="btn btn-danger" onclick="deleteVariantRow(this, ${rowCounter})">
                    <i class="bi bi-trash"></i>
                </button>
            </div>`;

        row.innerHTML = html;
        variantRowsContainer.appendChild(row);
        rowCounter++;
    }


    function createVariantRowWithValues(value1, value2, baseProductSKU) {
        // Generate SKU suffix based on variant values
        let skuSuffix = '';
        if (value1) {
            skuSuffix += `-${value1.value}`;
        }
        if (value2) {
            skuSuffix += `-${value2.value}`;
        }

        const variantSKU = baseProductSKU + skuSuffix;

        const row = document.createElement('div');
        row.className = 'row border rounded p-3 mb-3 position-relative align-items-center';
        row.dataset.rowId = `row-${rowCounter}`;

        // Check if rowCounter is even and apply the 'striped' class
        if (rowCounter % 2 === 0) {
            row.classList.add('striped'); // Add a striped class to even rows
        }

        // Generate random price between 1 and 100
        // var variantPrice = Math.floor(Math.random() * 100) + 1;

        let html = `
            <!-- Column 1: Image Preview -->
            <div class="col-md-2 mb-2">
                <div class="variant-image-container" style="width: 110px; height: 110px; border: 1px dashed #ddd; border-radius: 8px; overflow: hidden; margin: 0 auto;">
                    <img id="preview-variant-${rowCounter}" src="{{ asset('assets/images/add-product.png') }}"
                        class="img-fluid preview-image" style="width: 100%; height: 110px; object-fit: contain;">
                </div>
                <input type="file" class="form-control mt-2" name="variants[${rowCounter}][main-image]"
                    onchange="previewVariantImage(this, ${rowCounter})" accept="image/*">
            </div>

            <!-- Column 2: Name and SKU -->
            <div class="col-md-3 mb-2">
                <div class="mb-2">
                    <label class="form-label required">Product Name</label>
                    <input type="text" class="form-control" name="variants[${rowCounter}][name]" id="variant_${rowCounter}_name" value="${variantSKU}"
                    placeholder="Enter product Name" required>
                </div>
                <div>
                    <label class="form-label required">Stock</label>
                    <div class="d-flex gap-2 align-items-center">
                        <input type="text" class="form-control variant-stock-input"
                            name="variants[${rowCounter}][stock]"
                            id="variantStock_${rowCounter}"
                            value="Ready Stock"
                            data-actual-value="-100"
                            readonly required>
                        <div class="custom-checkbox">
                            <input type="checkbox" id="trackStockCheckbox_${rowCounter}"
                                onchange="updateVariantStockInput(${rowCounter})">
                            <label for="trackStockCheckbox_${rowCounter}">Track inventory</label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Column 3: Stock and Price -->
            <div class="col-md-3 mb-2">
                <div class="mb-2">
                    <label class="form-label required">Product SKU</label>
                    <input type="text" class="form-control" name="variants[${rowCounter}][sku]" id="variant_${rowCounter}_sku" value="${variantSKU}"
                    placeholder="Enter product SKU" required>
                </div>
                <div>
                    <label class="form-label required">Price</label>
                    <div class="input-group">
                        <span class="input-group-text">{{ $user->userDetails->currency ?? 'RM' }}</span>
                        <input type="number" step="0.01" min="0" class="form-control" name="variants[${rowCounter}][price]"
                        placeholder="0.00" required>
                    </div>
                </div>
            </div>

            <!-- Column 4: Variant Values
            <div class="col-md-4 mb-2">  -->`;

        if (value1 && value2) {
            html += `
                <div class="col-md-3 mb-2">
                    <div class="mb-2">
                        <label class="form-label required">Variant 1</label>
                        <select class="form-select" id="variant1_${rowCounter}"
                            name="variants[${rowCounter}][variant1_value]"
                            onchange="validateVariantSelection(this, ${rowCounter})" disabled>
                            ${getVariantOptions(variant1Select.value, value1.id)}
                        </select>
                    </div>
                    <div>
                        <label class="form-label required">Variant 2</label>
                        <select class="form-select" id="variant2_${rowCounter}"
                            name="variants[${rowCounter}][variant2_value]"
                            onchange="validateVariantSelection(this, ${rowCounter})" disabled>
                            ${getVariantOptions(variant2Select.value, value2.id)}
                        </select>
                    </div>
                </div>`;
        } else {
            const variantValue = value1 || value2;
            const variantId = variant1Select.value || variant2Select.value;
            html += `
                <div class="col-md-3 mb-2">
                    <div class="mb-2">
                        <label class="form-label required">Variant</label>
                        <select class="form-select" id="variant1_${rowCounter}" name="variants[${rowCounter}][variant1_value]"
                        onchange="validateVariantSelection(this, ${rowCounter})" disabled>
                            ${getVariantOptions(variantId, variantValue.id)}
                        </select>
                    </div>
                </div>`;
        }

        html += `
            <!-- </div> -->
            <!-- Column 5: Delete Button -->
            <div class="col-md-1 mb-2 d-flex justify-content-center align-items-center" style="height: 100%;">
                <button type="button" class="btn btn-danger" onclick="deleteVariantRow(this, ${rowCounter})">
                    <i class="bi bi-trash"></i>
                </button>
            </div>`;

        row.innerHTML = html;
        variantRowsContainer.appendChild(row);
        rowCounter++;
    }

    function getVariantOptions(variantId, selectedValueId) {
        if (!variantValues[variantId]) return '';
        return variantValues[variantId]
            .map(value => `<option value="${value.id}" ${value.id === selectedValueId ? 'selected' : ''}>${value.value}</option>`)
            .join('');
    }

    // Update deleteVariantRow function to be globally accessible
    window.deleteVariantRow = function(button, index) {
        const row = button.closest('.row');
        row.remove();
        // rowCounter--;
        if(index){
            // stringSku = stringSku.filter(item => !item.hasOwnProperty('row_${index}'));
            let keyToRemove = `row_${index}`;
            delete stringSku[keyToRemove];
            // stringSku.splice(index, 1);
        }
        // If no rows left, enable variant selects
        if (variantRowsContainer.children.length === 0) {

            rowCounter = 0;
            variant1Select.disabled = false;
            variant2Select.disabled = false;
            stringSku = [];

            document.getElementById('btnGenerateVariant').disabled = false;
            document.getElementById('btnAddVariant').disabled = false;
        }
    };

    // Add clear variants function
    function clearVariants() {
        const clearModal = new bootstrap.Modal(document.getElementById('clearModal'));
        clearModal.show();
    }

    // Add click handler for confirm clear button
    document.getElementById('confirmClear').addEventListener('click', function() {
        // Clear all variant rows
        variantRowsContainer.innerHTML = '';

        // Enable and reset variant selects
        variant1Select.disabled = false;
        variant2Select.disabled = false;
        variant1Select.value = '';
        variant2Select.value = '';

        variant2Select.disabled = true;
        stringSku = [];

        rowCounter = 0;

        // Re-enable Add button
        document.getElementById('btnAddVariant').disabled = false;
        document.getElementById('btnGenerateVariant').disabled = false;
        const clearModal = bootstrap.Modal.getInstance(document.getElementById('clearModal'));
        clearModal.hide();
    });

    // Update the clear button event listener
    document.getElementById('btnClearVariant').addEventListener('click', clearVariants);

    // Add this function for image preview
    window.previewVariantImage = function(input, rowId) {
        if (input.files && input.files[0]) {
            // Validate file size / Sahkan saiz fail
            const maxSize = 5 * 1024 * 1024; // 5MB in bytes
            if (input.files[0].size > maxSize) {
                alert('File size exceeds 5MB limit. Please choose a smaller file.');
                input.value = ''; // Clear the input
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                document.getElementById(`preview-variant-${rowId}`).src = e.target.result;
            };
            reader.readAsDataURL(input.files[0]);
        }
    };

    var stringSku = {};
    // Add this new function to validate variant selections
    window.validateVariantSelection = function(input, currentRow) {
        // take current variant1 and variant2 value
        const mainSKU = document.querySelector('input[name="productSKU"]').value;
        const currentVariant1 = document.getElementById(`variant1_${currentRow}`)?.value;
        const currentVariant2 = document.getElementById(`variant2_${currentRow}`)?.value;

        const values1 = variant1Select.value ?? [null];
        const values2 = variant2Select.value ?? [null];

        for (let i = 0; i < rowCounter; i++) {
            if(i === currentRow) continue;
            const existingVariant1 = document.getElementById(`variant1_${i}`)?.value;
            const existingVariant2 = document.getElementById(`variant2_${i}`)?.value;

            if(values1 != "" && values2 != ""){
                if (existingVariant1 && existingVariant2 && existingVariant1 === currentVariant1 && existingVariant2 === currentVariant2) {
                    alert('This variant value is already used. Please select a different value.');
                    document.getElementById(`variant_${currentRow}_sku`).value = mainSKU;
                    // document.getElementById(`variant_${currentRow}_name`).value = mainSKU;
                    document.getElementById(`variant1_${currentRow}`).value = '';
                    document.getElementById(`variant2_${currentRow}`).value = '';
                    return;
                }
            }else{
                if (existingVariant1 != undefined && existingVariant1 === currentVariant1) {
                    alert('This variant value is already used. Please select a different value.');
                    document.getElementById(`variant_${currentRow}_sku`).value = mainSKU;
                    // document.getElementById(`variant_${currentRow}_name`).value = mainSKU;
                    document.getElementById(`variant1_${currentRow}`).value = '';
                    return;
                }
            }
        }
        var skuNama = '';  // Reset skuNama
        var skuNama2 = ''; // Reset skuNama2


        // Ensure skuNama and skuNama2 are reset before processing
        if (values1 && values2) {
            // Reset before mapping
            skuNama = '';
            skuNama2 = '';

            // Check and set skuNama from values1
            const variant1 = variantValues[values1];
            variant1.map(value => {
                if (value.id === currentVariant1) {
                    skuNama = value.value;
                }
            });

            // Check and set skuNama2 from values2
            const variant2 = variantValues[values2];
            variant2.map(value => {
                if (value.id === currentVariant2) {
                    skuNama2 = value.value;
                }
            });
        } else {
            // Reset skuNama before mapping if only values1 exists
            skuNama = '';
            const variant1 = variantValues[values1];
            variant1.map(value => {
                if (value.id === currentVariant1) {
                    skuNama = value.value;
                }
            });
        }

        var newSku = '';
        if (currentVariant1 && currentVariant2) {
            newSku = mainSKU + '-' + skuNama + '-' + skuNama2;
            stringSku[`row_${currentRow}`]  = skuNama + '-' + skuNama2;
        } else if (currentVariant1) {
            newSku = mainSKU + '-' + skuNama;
            stringSku[`row_${currentRow}`] = skuNama;
        }

        // console.log(stringSku);

        // Set the updated SKU value in the input field
        // console.log(newSku);
        document.getElementById(`variant_${currentRow}_sku`).value = newSku;
        document.getElementById(`variant_${currentRow}_name`).value = newSku;
    };

    // Function to format variant stock input based on UOM type / Fungsi untuk format input stok variant berdasarkan jenis UOM
    window.formatVariantStockInput = function(input, rowId) {
        // Skip formatting if input is "Ready Stock" / Langkau format jika input "Ready Stock"
        if (input.value === 'Ready Stock') {
            return;
        }

        // Check if UOM type is count, use integer formatting / Semak jika jenis UOM adalah count, guna format integer
        if (isCountUom()) {
            input.value = input.value.replace(/[^\d]/g, ''); // Only allow digits / Hanya benarkan angka
        } else {
            // Use decimal formatting for non-count UOM / Guna format decimal untuk UOM bukan count
            let value = input.value.replace(/[^\d.]/g, ''); // Only allow digits and dots / Hanya benarkan angka dan titik
            const parts = value.split('.');

            // Only allow one decimal point / Hanya benarkan satu titik decimal
            if (parts.length > 2) {
                value = parts[0] + '.' + parts.slice(1).join('');
            }

            // Limit decimal places to 3 / Had tempat decimal kepada 3
            if (parts[1]?.length > 3) {
                value = parts[0] + '.' + parts[1].slice(0, 3);
            }

            input.value = value;
        }
    };

    // Add this function to handle variant stock input updates and ensure proper formatting based on UOM
    window.updateVariantStockInput = function(rowId) {
        const stockInput = document.getElementById(`variantStock_${rowId}`);
        const trackInventoryCheckbox = document.getElementById(`trackStockCheckbox_${rowId}`);

        if (trackInventoryCheckbox.checked) {
            stockInput.readOnly = false;
            if (stockInput.value === 'Ready Stock') {
                stockInput.value = '';
            }

            stockInput.placeholder = 'Enter stock';

            // Add event listener with UOM-based formatting / Tambah event listener dengan format berdasarkan UOM
            stockInput.addEventListener('input', function() {
                formatVariantStockInput(this, rowId);

                console.log(rowId);
            });
        } else {
            stockInput.readOnly = true;
            stockInput.value = 'Ready Stock';
            stockInput.dataset.actualValue = '-100';

            // Remove event listener when checkbox is unchecked
            stockInput.removeEventListener('input', function() {
                formatVariantStockInput(this, rowId);
            });
        }
    };

    // Add this to your document ready function
    const productForm = document.getElementById('productForm');
    productForm.addEventListener('submit', function(event) {
        event.preventDefault(); // Prevent default form submission // Halang penghantaran borang lalai

        // Enable all disabled selects before submission
        document.querySelectorAll('select:disabled').forEach(select => {
            const hidden = document.createElement('input');
            hidden.type = 'hidden';
            hidden.name = select.name;
            hidden.value = select.value;

            // Avoid duplicate hidden inputs
            if (!select.parentNode.querySelector(`input[name="${select.name}"]`)) {
                select.parentNode.appendChild(hidden);
            }
        });

                // Validate step quantity before submission / Validasi step quantity sebelum hantar borang
        const stepQuantityInput = document.getElementById('stepQuantity');
        if (stepQuantityInput) {
            const stepQuantityValue = parseFloat(stepQuantityInput.value) || 0;

            if (isCountUom()) {
                // For count UOM, minimum value is 1 / Untuk UOM count, nilai minimum ialah 1
                if (stepQuantityValue < 1) {
                    document.getElementById('loading-screen').style.display = 'none';
                    Swal.fire({
                        icon: 'error',
                        title: 'Invalid Step Quantity',
                        text: 'Step quantity for count items cannot be less than 1.',
                    });
                    return;
                }
            } else {
                // For decimal UOM, minimum value is 0.001 / Untuk UOM decimal, nilai minimum ialah 0.001
                if (stepQuantityValue < 0.001) {
                    document.getElementById('loading-screen').style.display = 'none';
                    Swal.fire({
                        icon: 'error',
                        title: 'Invalid Step Quantity',
                        text: 'Step quantity cannot be less than 0.001.',
                    });
                    return;
                }
            }
        }

        if(rowCounter <= 0){

            if(isVariantCheckbox.checked){
                Swal.fire({
                    icon: 'error',
                    title: 'Oops...',
                    text: 'No variant product found!',
                });
                return;
            }
        }

        // Check for duplicate SKUs in variants // Semak SKU yang sama dalam variant
        const seen = new Set();
        let duplicateSku = null;

        for(let i=0; i < rowCounter; i++){
            const element = document.getElementById(`variant_${i}_sku`);
            if (!element) continue; //check dulu row ni exist ke tak

            const sku = element.value;
            if (seen.has(sku)) {
                duplicateSku = sku;
                break;
            }
            seen.add(sku);
        }

        if (duplicateSku) {
            document.getElementById('loading-screen').style.display = 'none';
            // alert(`Duplicate SKU found: ${duplicateSku}`);
            Swal.fire({
                icon: 'error',
                title: 'Oops...',
                text: `Duplicate SKU found: ${duplicateSku}`,
            });
            return;
        }

        // Process all variant stock inputs
        document.querySelectorAll('.variant-stock-input').forEach(input => {
            if (input.value === 'Ready Stock') {
                input.dataset.actualValue = '-100';
            } else {
                input.dataset.actualValue = input.value;
            }
        });

        // Now check SKUs via AJAX // Sekarang semak SKU melalui AJAX
        checkSkusBeforeSubmit();
    });

    // Function to check SKUs via AJAX before form submission // Fungsi untuk semak SKU melalui AJAX sebelum hantar borang
    function checkSkusBeforeSubmit() {
        document.getElementById('loading-screen').style.display = 'block';

        // console.log('sini');

        // Collect all SKUs to check // Kumpul semua SKU untuk disemak
        const skusToCheck = [];

        // Add main product SKU // Tambah SKU produk utama
        const mainSku = document.querySelector('input[name="productSKU"]').value;
        const mainProductName = document.querySelector('input[name="productName"]').value;

        if (mainSku) {
            skusToCheck.push({
                sku: mainSku,
                name: mainProductName,
                type: 'main'
            });
        }

        // Add variant SKUs if any // Tambah SKU variant jika ada
        if (isVariantCheckbox.checked) {
            for(let i = 0; i < rowCounter; i++){
                const skuElement = document.getElementById(`variant_${i}_sku`);
                const nameElement = document.querySelector(`input[name="variants[${i}][name]"]`);

                if (skuElement && nameElement && skuElement.value) {
                    skusToCheck.push({
                        sku: skuElement.value,
                        name: nameElement.value,
                        type: 'variant'
                    });
                }
            }
        }

        // Make AJAX request to check all SKUs // Buat permintaan AJAX untuk semak semua SKU
        fetch('{{ route('product.check.multiple.sku') }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                skus: skusToCheck
            })
        })
        .then(response => response.json())
        .then(data => {
            document.getElementById('loading-screen').style.display = 'none';

            // console.log(data);

            if (data.result === false) {
                // Show error popup and don't submit form // Tunjuk popup ralat dan jangan hantar borang
                Swal.fire({
                    icon: 'error',
                    title: 'SKU Duplicate Found!',
                    text: data.message,
                });
            } else {
                // All SKUs are available, proceed with form submission // Semua SKU tersedia, teruskan dengan penghantaran borang
                submitFormAfterValidation();
            }
        })
        .catch(error => {
            document.getElementById('loading-screen').style.display = 'none';
            console.error('Error:', error);
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: 'Failed to check SKU. Please try again.',
            });
        });
    }

        // Function to actually submit the form after validation // Fungsi untuk benar-benar hantar borang selepas pengesahan
    function submitFormAfterValidation() {
        document.getElementById('loading-screen').style.display = 'block';

        // Create a new form submission without the event listener // Buat penghantaran borang baharu tanpa event listener
        const formData = new FormData(productForm);

        // Submit using fetch to avoid infinite loop // Hantar menggunakan fetch untuk elak gelung tanpa had
        fetch(productForm.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
            }
        })
        .then(response => {
            if (response.redirected) {
                // If server redirects, follow it // Jika pelayan mengalihkan, ikuti
                window.location.href = response.url;
            } else {
                return response.text();
            }
        })
        .then(html => {
            if (html) {
                // If there's HTML content, it might be an error page // Jika ada kandungan HTML, mungkin halaman ralat
                document.open();
                document.write(html);
                document.close();
                document.getElementById('loading-screen').style.display = 'none';
            }
        })
        .catch(error => {
            document.getElementById('loading-screen').style.display = 'none';
            console.error('Error:', error);
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: 'Failed to submit form. Please try again.',
            });
        });
    }

});
</script>
@endpush
