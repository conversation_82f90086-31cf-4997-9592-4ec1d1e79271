@extends('backend.layout.default')

@section('head')
<meta name="csrf-token" content="{{ csrf_token() }}">
@endsection

@push('styling')
<style>
    .image-preview-container {
        width: 100%;
        max-width: 600px;
        margin: 0 auto 2rem;
        border: 2px dashed rgba(0, 0, 0, 0.1);
        padding: 1.5rem;
        border-radius: 12px;
        transition: all 0.3s ease;
        min-height: 530px;
    }

    .image-preview-container:hover {
        border-color: #8338ec;
    }

    .preview {
        display: flex;
        justify-content: center;
        margin-bottom: 1.5rem;
    }

    .preview img {
        width: 100%;
        max-width: 500px;
        height: auto;
        object-fit: contain;
        border-radius: 8px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        min-height: 380px;
    }

    .upload-label {
        display: block;
        width: 100%;
        max-width: 200px;
        height: 45px;
        margin: 0 auto;
        text-align: center;
        background: #8338ec;
        color: #fff;
        font-size: 0.9rem;
        font-weight: 500;
        border-radius: 8px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
    }

    .upload-label:hover {
        background: #6a2ec4;
        transform: translateY(-1px);
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        font-weight: 500;
        margin-bottom: 0.5rem;
        color: #344767;
    }

    .form-control, .form-select {
        border-radius: 8px;
        padding: 0.625rem 1rem;
        border: 1px solid #e9ecef;
        transition: all 0.2s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: #8338ec;
        box-shadow: 0 0 0 0.2rem rgba(131, 56, 236, 0.25);
    }

    .required::after {
        content: "*";
        color: #dc3545;
        margin-left: 4px;
    }

    .btn {
        padding: 0.625rem 1.5rem;
        border-radius: 8px;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .btn-primary {
        background: #8338ec;
        border-color: #8338ec;
    }

    .btn-primary:hover {
        background: #6a2ec4;
        border-color: #6a2ec4;
        transform: translateY(-1px);
    }

    .btn-light-secondary {
        background: #f8f9fa;
        border-color: #e9ecef;
    }

    .btn-light-secondary:hover {
        background: #e9ecef;
    }

    .invalid-feedback {
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    /* Custom checkbox styling */
    .custom-checkbox {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .custom-checkbox input[type="checkbox"] {
        width: 1.25rem;
        height: 1.25rem;
        cursor: pointer;
    }

    /* Currency Input Group */
    .input-group-text {
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px 0 0 8px;
        padding: 0.625rem 1rem;
        font-weight: 500;
    }

    .striped {
        background-color: #f8f9fa; /* Light gray background for even rows */
    }

    .currency-input {
        border-radius: 0 8px 8px 0;
    }
</style>
@endpush

@section('content')

<div id="loading-screen" style="display:none; position:fixed; top:0; left:0; width:100%; height:100%; background:#ffffffdd; z-index:9999; text-align:center;">
    <div style="position:absolute; top:50%; left:50%; transform:translate(-50%, -50%)">
        <div class="spinner-border text-primary" role="status"></div>
        <div class="mt-2">Processing, please wait...</div>
    </div>
</div>

<div class="page-heading">
    <div class="page-title mb-4">
        <div class="row">
            <div class="col-12 col-md-6 order-md-1 order-last">
                <h3>Edit Product</h3>
                <p class="text-subtitle text-muted">Update your product information below.</p>
            </div>
            <div class="col-12 col-md-6 order-md-2 order-first">
                <nav aria-label="breadcrumb" class="breadcrumb-header float-start float-lg-end">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('products') }}">Products</a></li>
                        <li class="breadcrumb-item active">Edit Product</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
    <form action="{{ route('product.update', $products->id) }}" method="POST" enctype="multipart/form-data" id="productForm">
    <div class="card">
        <div class="card-content">
            <div class="card-body">
                    @csrf
                    <div class="row align-items-stretch">
                        <!-- Left Column: Product Image -->
                        <div class="col-md-6">
                            <div class="form-group" style="display: flex; flex-direction: column;">
                                <label class="form-label mb-3 text-center">Product Image</label>
                                <label for="file-upload" class="image-preview-container" style="cursor: pointer; order: 2;">
                                    <div class="preview">
                                        <img id="preview-selected-image"
                                        src="{{ $products->productDetail && $products->productDetail->product_attachment ?
                                            (str_contains($products->productDetail->product_attachment, 'https://') || str_contains($products->productDetail->product_attachment, 'http://') ?
                                                $products->productDetail->product_attachment :
                                                'https://corrad.visionice.net/bizapp/upload/product/'.$products->productDetail->product_attachment
                                            ) :
                                            asset('assets/images/add-product.png') }}"
                                        alt="Product Image">
                                    </div>
                                    <div class="upload-label">
                                        <i class="bi bi-upload me-2"></i>Upload Image
                                    </div>
                                    <input type="file" id="file-upload" name="main-image" accept="image/*"
                                           style="display: none;"
                                           data-max-size="5242880">
                                    <!-- Hidden input to preserve existing image + input tersembunyi untuk kekalkan gambar sedia ada -->
                                    @if($products->productDetail && $products->productDetail->product_attachment)
                                        <input type="hidden" name="existing_main_image" value="{{ $products->productDetail->product_attachment }}">
                                    @endif
                                    <div id="file-size-error" class="text-danger small mt-2" style="display: none;"></div>
                                    <small class="text-muted d-block text-center mt-2">
                                        Maximum file size: 5MB (Allowed formats: JPEG, PNG)
                                    </small>
                                </label>


                            </div>
                        </div>
                        <!-- Right Column: Other Fields -->
                        <div class="col-md-6">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label required">Product SKU</label>
                                        <input type="text" class="form-control @error('productSKU') is-invalid @enderror"
                                            name="productSKU" value="{{ old('productSKU', $products->product_SKU) }}"
                                            placeholder="Enter product SKU" id="product_skus" required>
                                        @error('productSKU')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror

                                    <input type="hidden" name="product_id" value="{{ $products->id }}" id="main_product_id">
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label required">Product Name</label>
                                        <input type="text" class="form-control @error('productName') is-invalid @enderror"
                                            name="productName" value="{{ old('productName', $products->product_name) }}"
                                            placeholder="Enter product name" required>
                                        @error('productName')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>


                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label required">Product Category</label>
                                        <select class="form-select @error('productCategory') is-invalid @enderror"
                                                name="productCategory" required>
                                            <option value="">Select a category</option>
                                            @foreach($productsCategory as $category)
                                                <option value="{{ $category->category_name }}"
                                                    {{ old('productCategory', $products->productDetail->productsCategory->category_name ?? '') == $category->category_name ? 'selected' : '' }}>
                                                    {{ $category->category_name }}
                                                </option>
                                            @endforeach
                                        </select>
                                        @error('productCategory')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">Brand</label>
                                        <input type="text" class="form-control @error('productBrand') is-invalid @enderror"
                                            name="productBrand" value="{{ old('productBrand', $products->product_brand) }}"
                                            placeholder="Enter brand name">
                                        @error('productBrand')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                @if(auth()->user()->username === 'wowskin' || auth()->user()->username === 'uplats012')
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="form-label">LHDN Classification Code</label>
                                        <select class="form-select @error('productLhdnCode') is-invalid @enderror"
                                                name="productLhdnCode">
                                            @foreach($productLhdnCodes as $lhdnCode)
                                                <option value="{{ $lhdnCode->id }}"
                                                    {{ old('productLhdnCode', $products->product_lhdn_code_id) == $lhdnCode->id || (!old('productLhdnCode') && !$products->product_lhdn_code_id && $lhdnCode->id == 66) ? 'selected' : '' }}>
                                                    {{ $lhdnCode->classificationcode ?? $lhdnCode->code ?? $lhdnCode->id }} - {{ $lhdnCode->description ?? $lhdnCode->name ?? 'LHDN Code' }}
                                                </option>
                                            @endforeach
                                        </select>
                                        @error('productLhdnCode')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                @endif
                                <div class="col-md-6">
                                    <!-- Empty column for better layout spacing + ruang kosong untuk susun atur yang lebih baik -->
                                </div>

                                <div class="col-12">
                                    <div class="form-group">
                                        <label class="form-label">Description</label>
                                        <textarea class="form-control @error('productDescription') is-invalid @enderror"
                                            name="productDescription" rows="4"
                                            placeholder="Enter product description">{{ old('productDescription', $products->product_description) }}</textarea>
                                        @error('productDescription')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label required">Unit of Measure</label>
                                        <select class="form-select @error('productUom') is-invalid @enderror"
                                                name="productUom" required>
                                            <option value="">Select unit</option>
                                            @php $currentType = null; @endphp
                                            @foreach($uoms as $uom)
                                                @if($currentType !== $uom->uom_type)
                                                    @if($currentType !== null)
                                                        </optgroup>
                                                    @endif
                                                    <optgroup label="{{ ucfirst($uom->uom_type) }}">
                                                    @php $currentType = $uom->uom_type; @endphp
                                                @endif
                                                <option value="{{ $uom->id }}"
                                                    {{ old('productUom', $products->uom_id) == $uom->id ? 'selected' : '' }}>
                                                    {{ $uom->uom_name }} ({{ $uom->uom_code }})
                                                </option>
                                            @endforeach
                                            @if($currentType !== null)
                                                </optgroup>
                                            @endif
                                        </select>
                                        @error('productUom')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label required">Stock Quantity</label>
                                        <div class="d-flex gap-3 align-items-center">
                                            <!-- Input field for stock -->
                                            <input type="text" class="form-control @error('productInventory') is-invalid @enderror"
                                                name="productInventory" id="productInventory"
                                                value="{{ old('productInventory', $products->product_stock === '-100.000' ? 'Ready Stock' : $products->product_stock) }}"
                                                placeholder="Enter stock quantity" required
                                                oninput="formatDecimalInput(this, 3)"
                                                {{ $products->product_stock == '-100.000' && !old('productInventory') ? 'readonly' : '' }}>

                                            <!-- Checkbox for "Track Inventory" -->
                                            <div class="custom-checkbox">
                                                <input type="checkbox" id="readyStockCheckbox"
                                                    {{ old('readyStock', $products->product_stock != '-100') ? 'checked' : '' }}>
                                                <label for="readyStockCheckbox">Track Inventory</label>
                                            </div>
                                        </div>
                                        @error('productInventory')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <!-- Hide status product -->
                                    {{-- <div class="form-group">
                                        <label class="form-label required">Active Product</label>
                                        <div class="d-flex gap-3 align-items-center">
                                            <!-- Checkbox for "Track Inventory" -->
                                            <div class="custom-checkbox">
                                                <input type="checkbox" id="readyStockCheckbox"
                                                    {{ old('readyStock', $products->product_stock != '-100') ? 'checked' : '' }}>
                                                <label for="readyStockCheckbox">Track Inventory</label>
                                            </div>
                                        </div>
                                        @error('activeProduct')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div> --}}
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">
                                            Step Quantity
                                            <i class="bi bi-info-circle text-primary ms-1"
                                               data-bs-toggle="tooltip"
                                               data-bs-placement="top"
                                               title="Step quantity determines the increment/decrement amount when adjusting product quantities in orders"></i>
                                        </label>
                                        <input type="text" class="form-control @error('stepQuantity') is-invalid @enderror"
                                            name="stepQuantity" id="stepQuantity" value="{{ old('stepQuantity', $products->step_quantity) }}"
                                            placeholder="Enter step quantity"
                                            oninput="formatStepQuantityInput(this)">
                                        @error('stepQuantity')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                {{-- <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">Min Quantity</label>
                                        <input type="text" class="form-control @error('minQuantity') is-invalid @enderror"
                                            name="minQuantity" value="{{ old('minQuantity', $products->min_quantity) }}"
                                            placeholder="Enter minimum quantity"
                                            oninput="formatDecimalInput(this, 3)">
                                        @error('minQuantity')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div> --}}

                                <div class="col-12">
                                    <div class="form-group">
                                        <label class="form-label required">Price</label>
                                        <div class="input-group">
                                            <span class="input-group-text">{{ $user->userDetails->currency ?? 'RM' }}</span>
                                            <input type="text" class="form-control currency-input @error('productPrice') is-invalid @enderror"
                                                name="productPrice" value="{{ old('productPrice', $products->product_price) }}"
                                                placeholder="0.00" required>
                                        </div>
                                        @error('productPrice')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="col-12">
                                    <div class="form-group">
                                        <div class="d-flex gap-3 align-items-center">
                                            @if(auth()->user()->username == 'wowskin' || auth()->user()->username == 'uplats012')
                                            <div class="custom-checkbox mb-3">
                                                <input type="checkbox" id="isVariant" name="isVariant" {{ $products->isVariant == 'Y' ? 'checked' : '' }}>
                                                <label for="isVariant">Product has variants</label>
                                            </div>
                                            @endif
                                            <div class="custom-checkbox mb-3">
                                                <input type="checkbox" id="statusProductCheckbox" name="productStatus" {{ old('statusProduct', $products->product_status === '1') ? 'checked' : '' }}>
                                                <label for="statusProductCheckbox">Active Product</label>
                                            </div>
                                        </div>
                                        {{-- @error('productInventory')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror --}}
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
            </div>
        </div>
    </div>

    <!-- Add new variant section -->
    <div id="variantSection" style="display: {{ $products->isVariant == 'Y' ? 'block' : 'none' }};" class="card mt-4">
        <div class="card-content">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">Variant 1</label>
                            <select class="form-select @error('variant1') is-invalid @enderror" id="variant1" name="variant1"{{ $products->isVariant == 'Y' ? 'disabled' : '' }}  {{ $products->isVariant == 'Y' ? 'required' : '' }}>
                                <option value="">Select Variant 1</option>
                                @foreach($variantAttributes as $attribute)
                                    <option value="{{ $attribute->id }}"
                                        {{ $variantData->isNotEmpty() && isset($variantData->first()['attributeValues'][0]) && $variantData->first()['attributeValues'][0]->variantAttributeValue->variantAttribute->id == $attribute->id ? 'selected' : '' }}>
                                        {{ $attribute->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('variant1')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">Variant 2</label>
                            <select class="form-select @error('variant2') is-invalid @enderror" id="variant2" name="variant2" {{ $variantData->count() > 1 ? "" : 'disabled' }} {{ $products->isVariant == 'Y' ? 'disabled' : '' }} required>
                                <option value="">Select Variant 2</option>
                                @foreach($variantAttributes as $attribute)
                                {{-- {{ dd($attribute) }} --}}
                                    <option value="{{ $attribute->id }}"
                                        {{ isset($variantData->first()['attributeValues'][1]) && $variantData->first()['attributeValues'][1]->variantAttributeValue->variantAttribute->id == $attribute->id ? 'selected' : '' }}>
                                        {{ $attribute->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('variant2')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    <div class="col-12 mt-3">
                        <button type="button" class="btn btn-primary me-1" id="btnAddVariant" onclick="disableButtons()">
                            <i class="bi bi-plus me-1"></i>Add
                        </button>
                        <button type="button" class="btn btn-success me-1" id="btnGenerateVariant" {{ $products->isVariant == 'Y' ? 'disabled' : '' }}>
                            <i class="bi bi-gear me-1"></i>Generate
                        </button>
                        <button type="button" class="btn btn-danger" id="btnClearVariant">
                            <i class="bi bi-trash me-1"></i>Clear
                        </button>

                        <!-- Add this container for variant rows -->
                        <div id="variantRowsContainer" class="mb-3 pt-3">
                            <!-- Existing variant rows will be displayed here -->
                            @if($products->isVariant == 'Y' && $products->variants->isNotEmpty())
                                @foreach($variantData as $index => $variant)
                                    <div class="row border rounded p-3 mb-3 position-relative align-items-center @if ($index % 2 == 0)
                                        striped
                                    @endif" data-row-id="row-{{ $index }}">
                                        <!-- ID product variant -->
                                        <input type="text" class="form-control" name="variants[{{ $index }}][id]" value="{{ $variant['variant']->id }}" hidden>
                                        <!-- Column 1: Image Preview + Pratonton Gambar -->
                                        <div class="col-md-2 mb-2">
                                            <div class="variant-image-container" style="width: 110px; height: 110px; border: 1px dashed #ddd; border-radius: 8px; overflow: hidden; margin: 0 auto;">
                                                <img id="preview-variant-{{ $index }}"
                                                    src="{{ $variant['variant']->productDetail && $variant['variant']->productDetail->product_attachment ?
                                                        (str_contains($variant['variant']->productDetail->product_attachment, 'https://') || str_contains($variant['variant']->productDetail->product_attachment, 'http://') ?
                                                            $variant['variant']->productDetail->product_attachment :
                                                            'https://corrad.visionice.net/bizapp/upload/product/'.$variant['variant']->productDetail->product_attachment
                                                        ) :
                                                        asset('assets/images/add-product.png') }}"
                                                    class="img-fluid preview-image" style="width: 100%; height: 110px; object-fit: contain;"
                                                    alt="Variant Image">
                                            </div>
                                            <input type="file" class="form-control mt-2" name="variants[{{ $index }}][main-image]"
                                                onchange="previewVariantImage(this, {{ $index }})" accept="image/*" >
                                            <!-- Preserve existing variant image + kekalkan gambar variant sedia ada -->
                                            @if($variant['variant']->productDetail && $variant['variant']->productDetail->product_attachment)
                                                <input type="hidden" name="variants[{{ $index }}][existing_image]" value="{{ $variant['variant']->productDetail->product_attachment }}">
                                                <input type="hidden" name="variants[{{ $index }}][current_image_url]" value="https://corrad.visionice.net/bizapp/upload/product/{{ $variant['variant']->productDetail->product_attachment }}">
                                            @endif
                                        </div>

                                        <!-- Column 2: Name and SKU -->
                                        <div class="col-md-3 mb-2">
                                            <div class="mb-2">
                                                <label class="form-label required">Product Name</label>
                                                <input type="text" class="form-control" name="variants[{{ $index }}][name]"
                                                    value="{{ $variant['variant']->product_name }}" placeholder="Enter product Name" required>
                                            </div>
                                            <div>

                                                    <label class="form-label required">Stock</label>
                                                    <div class="d-flex gap-2 align-items-center">
                                                        <input type="text" class="form-control variant-stock-input"
                                                            name="variants[{{ $index }}][stock]"
                                                            id="variantStock_{{ $index }}"
                                                            value="{{ $variant['variant']->product_stock === '-100.000' ? 'Ready Stock' : $variant['variant']->product_stock }}"
                                                            data-actual-value="{{ $variant['variant']->product_stock }}"
                                                            data-original-value="{{ $variant['variant']->product_stock }}"
                                                            placeholder="{{ $variant['variant']->product_stock === '-100.000' ? 'Enter stock' : '' }}"
                                                            {{ $variant['variant']->product_stock === '-100.000' ? 'readonly' : '' }} required>
                                                        <div class="custom-checkbox">
                                                            <input type="checkbox" id="trackStockCheckbox_{{ $index }}"
                                                                {{ $variant['variant']->product_stock === '-100.000' ? '' : 'checked' }}
                                                                onchange="updateVariantStockInput({{ $index }})">
                                                            <label for="trackStockCheckbox_{{ $index }}">Track inventory</label>
                                                        </div>
                                                    </div>
                                            </div>
                                        </div>

                                        <div class="col-md-3 mb-2">
                                            <div class="mb-2">
                                                <label class="form-label required">Product SKU</label>
                                                <input type="text" class="form-control" name="variants[{{ $index }}][sku]" id="variant_{{ $index }}_sku"
                                                    value="{{ $variant['variant']->product_sku }}" placeholder="Enter product SKU">
                                            </div>
                                            <div class="mb-2">
                                                <label class="form-label required">Price</label>
                                                <div class="input-group">
                                                    <span class="input-group-text">{{ $user->userDetails->currency ?? 'RM' }}</span>
                                                    <input type="number" step="0.01" min="0" class="form-control"
                                                        name="variants[{{ $index }}][price]"
                                                        value="{{ $variant['variant']->product_price }}"
                                                        placeholder="0.00" required>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Column 4: Variant Values -->
                                            @php
                                                $variantAttributeValues = $variant['attributeValues'];
                                                $hasSecondVariant = count(value: $variantAttributeValues) > 1;
                                            @endphp

                                            <div class="col-md-3 mb-2">
                                                @if($hasSecondVariant)
                                                    <div class="mb-2">
                                                        <label class="form-label required">Variant 1</label>
                                                        <select class="form-select" id="variant1_{{ $index }}" onchange="validateVariantSelection(this, {{ $index }})"
                                                            name="variants[{{ $index }}][variant1_value]" required>
                                                            <option value="">Select Value</option>
                                                            @foreach($variantAttributes as $value)
                                                                @if($variantData->first()['attributeValues'][0]->variantAttributeValue->variantAttribute->id == $value->id)
                                                                    @foreach($value->values as $value2)
                                                                        <option value="{{ $value2->id }}"
                                                                            {{ $variantAttributeValues[0]->variant_attribute_value_id == $value2->id ? 'selected' : '' }}>
                                                                            {{ $value2->value }}
                                                                        </option>
                                                                    @endforeach
                                                                @endif
                                                            @endforeach
                                                        </select>
                                                    </div>
                                                    <div class="mb-2">
                                                        <label class="form-label required">Variant 2</label>
                                                        <select class="form-select" id="variant2_{{ $index }}" onchange="validateVariantSelection(this, {{ $index }})"
                                                            name="variants[{{ $index }}][variant2_value]" required>
                                                            <option value="">Select Value</option>
                                                            @foreach($variantAttributes as $value)
                                                                @if($variantData->first()['attributeValues'][1]->variantAttributeValue->variantAttribute->id == $value->id)
                                                                    @foreach($value->values as $value2)
                                                                        <option value="{{ $value2->id }}"
                                                                            {{ $variantAttributeValues[1]->variant_attribute_value_id == $value2->id ? 'selected' : '' }}>
                                                                            {{ $value2->value }}
                                                                    </option>
                                                                    @endforeach
                                                                @endif
                                                            @endforeach
                                                        </select>
                                                    </div>
                                                @else
                                                    <div class="mb-3">
                                                        <label class="form-label required">Variant</label>
                                                        <select class="form-select" id="variant1_{{ $index }}"
                                                            name="variants[{{ $index }}][variant1_value]" onchange="validateVariantSelection(this, {{ $index }})" required>
                                                            <option value="">Select Value</option>
                                                            @foreach($variantAttributes as $value)
                                                                @if($variantData->isNotEmpty() && isset($variantData->first()['attributeValues'][0]) && $variantData->first()['attributeValues'][0]->variantAttributeValue->variantAttribute->id == $value->id)
                                                                    @foreach($value->values as $value2)
                                                                        <option value="{{ $value2->id }}"
                                                                            {{ $variantAttributeValues[0]->variant_attribute_value_id == $value2->id ? 'selected' : '' }}>
                                                                                {{ $value2->value }}
                                                                        </option>
                                                                    @endforeach
                                                                @endif
                                                            @endforeach
                                                        </select>
                                                    </div>
                                                @endif
                                        </div>
                                        <div class="col-md-1 mb-2 d-flex justify-content-center align-items-center" style="height: 100%;">
                                           <!-- Column 5: Delete Button + Butang Padam -->
                                            <button type="button" class="btn btn-danger" onclick="showDeleteConfirmModal(this, {{ $index }})">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>

                                    </div>
                                @endforeach
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div id="deletedVariantsContainer" style="display: none;"></div>

    <!-- Buttons outside the form with form attribute -->
    <div class="col-12 d-flex justify-content-end gap-2">
        <button type="reset" class="btn btn-light-secondary" form="productForm" onclick="resetForm()">
            <i class="bi bi-x me-1"></i>Reset
        </button>
        <button type="submit" class="btn btn-primary" id="submitForm" form="productForm">
            <i class="bi bi-check me-1"></i>Save Changes
        </button>
    </div>
    </form>
    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteConfirmModal">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">Confirm Delete</h4>
                    <button type="button" class="close" data-bs-dismiss="modal">&times;</button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete this item? This action cannot be undone.</p>
                    <input type="hidden" id="deleteRowIndex" value="">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="confirmDeleteBtn">Delete</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Change to Simple Product Modal -->
    <div class="modal fade" id="checkBoxModal">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">Change to Simple Product</h4>
                    <button type="button" class="close" data-bs-dismiss="modal">&times;</button>
                </div>
                <div class="modal-body">
                    <p>WARNING: All variant products will be deleted if you untick this option. Are you sure you want to continue?</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="confirmClear">Confirm</button>
                </div>
            </div>
        </div>
    </div>

</div>
@endsection

@push('scripts')
@if (session('duplicate_sku'))
<script>
    Swal.fire({
        icon: 'error',
        title: 'Oops...',
        text: @json(session('duplicate_sku')),
    });
</script>
@endif
<script src="{{ asset('mazer/extensions/choices.js/public/assets/scripts/choices.js') }}"></script>
<script>
    const variantValues = @json($variantValues);
    const variantData = @json($variantData);


    // console.log(variantData);
    var oldStringSku = {};
    let i = 0;
    variantData.forEach(e1 => {
        if (e1['attributeValues'] && e1['attributeValues'][1]) {
            oldStringSku[`row_${i}`] = e1['attributeValues'][0]['variant_attribute_value']['value'] + '-' + e1['attributeValues'][1]['variant_attribute_value']['value'];
        } else {
            oldStringSku[`row_${i}`] = e1['attributeValues'][0]['variant_attribute_value']['value'];
        }
        i++;
    });

    // console.log(oldStringSku);
</script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips with instant display / Inisialisasi tooltip dengan paparan segera
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl, {
            delay: { show: 0, hide: 100 } // Instant show, quick hide / Paparan segera, sembunyi pantas
        });
    });

    // UOM data dengan type untuk JavaScript / UOM data with type for JavaScript
    const uomData = @json($uoms->pluck(['uom_type'], 'id'));

    // Initialize Choices.js for select elements
    let choices = document.querySelectorAll(".choices");
    let initChoice;
    for (let i = 0; i < choices.length; i++) {
        if (choices[i].classList.contains("multiple-remove")) {
            initChoice = new Choices(choices[i], {
                delimiter: ",",
                editItems: true,
                maxItemCount: -1,
                removeItemButton: true,
            });
        } else {
            initChoice = new Choices(choices[i]);
        }
    }

    // Stock tracking checkbox functionality
    const stockInput = document.getElementById('productInventory');
    const trackInventoryCheckbox = document.getElementById('readyStockCheckbox');
    const uomSelect = document.querySelector('select[name="productUom"]');
    const defaultStock = {{ $products->product_stock }};

   // Function to update the input field based on checkbox state
   function updateStockInput() {
        if (trackInventoryCheckbox.checked) {
            // If "Track Inventory" is checked, allow editing
            stockInput.readOnly = false;
            // If the current value is "Ready Stock", set it to the default stock value
            if (stockInput.value === 'Ready Stock') {
                stockInput.value = defaultStock === -100 ? '1' : defaultStock;
            }
        } else {
            // If "Track Inventory" is unchecked, set value to "Ready Stock" and make it readonly
            stockInput.readOnly = true;
            stockInput.value = 'Ready Stock';
        }
    }

    // Initialize the input field state based on the checkbox's initial state
    if (trackInventoryCheckbox.checked) {
        stockInput.readOnly = false;
        if (defaultStock === -100.000) {
            stockInput.value = '100';
        } else {
            stockInput.value = defaultStock;
        }
    } else {
        stockInput.readOnly = true;
        stockInput.value = 'Ready Stock';
    }
    trackInventoryCheckbox.addEventListener('change', updateStockInput);

    // Initialize step quantity default value based on UOM type / Inisialisasi nilai default step quantity berdasarkan jenis UOM
    setDefaultStepQuantity();

    // Image preview functionality
    const fileUpload = document.getElementById('file-upload');
    const previewImage = document.getElementById('preview-selected-image');

    const errorDiv = document.getElementById('file-size-error');

    fileUpload.addEventListener('change', function(event) {
        const file = event.target.files[0];
        errorDiv.style.display = 'none';

        if (file) {
            // Validate file size first
            if (file.size > this.dataset.maxSize) {
                errorDiv.textContent = 'File size exceeds 5MB limit';
                errorDiv.style.display = 'block';
                this.value = '';
                previewImage.src = "{{ isset($products->productDetail) && $products->productDetail && isset($products->productDetail->product_attachment) ? 'https://corrad.visionice.net/bizapp/upload/product/'.$products->productDetail->product_attachment : asset('assets/images/add-product.png') }}";
                return;
            }

            // Preview image if valid
            const reader = new FileReader();
            reader.onload = function(e) {
                previewImage.src = e.target.result;
                previewImage.style.display = 'block';
            };
            reader.readAsDataURL(file);
        }
    });

    // Price input formatting / Format input harga
    const priceInput = document.querySelector('input[name="productPrice"]');
    priceInput.addEventListener('input', function(e) {
        let value = this.value.replace(/[^\d.]/g, '');
        const parts = value.split('.');
        if (parts.length > 2) {
            value = parts[0] + '.' + parts.slice(1).join('');
        }
        if (parts[1]?.length > 2) {
            value = parts[0] + '.' + parts[1].slice(0, 2);
        }
        this.value = value;
    });

    // Check if current UOM type is count / Semak jika jenis UOM adalah count
    function isCountUom() {
        const selectedUomId = uomSelect.value;
        return selectedUomId && uomData[selectedUomId] === 'count';
    }

    // Integer input formatting function / Fungsi format input integer
    window.formatIntegerInput = function(input) {
        // Skip formatting if input is "Ready Stock" / Langkau format jika input "Ready Stock"
        if (input.value === 'Ready Stock') {
            return;
        }

        let value = input.value.replace(/[^\d]/g, ''); // Only allow digits / Hanya benarkan angka
        input.value = value;
    };

    // Decimal input formatting function / Fungsi format input decimal
    window.formatDecimalInput = function(input, decimalPlaces) {
        // Skip formatting if input is "Ready Stock" / Langkau format jika input "Ready Stock"
        if (input.value === 'Ready Stock') {
            return;
        }

        // Check if UOM type is count, use integer formatting / Semak jika jenis UOM adalah count, guna format integer
        if (isCountUom()) {
            formatIntegerInput(input);
            return;
        }

        let value = input.value.replace(/[^\d.]/g, ''); // Only allow digits and dots / Hanya benarkan angka dan titik
        const parts = value.split('.');

        // Only allow one decimal point / Hanya benarkan satu titik decimal
        if (parts.length > 2) {
            value = parts[0] + '.' + parts.slice(1).join('');
        }

        // Limit decimal places / Had tempat decimal
        if (parts[1]?.length > decimalPlaces) {
            value = parts[0] + '.' + parts[1].slice(0, decimalPlaces);
        }

        input.value = value;
    };

    // Step quantity formatting function / Fungsi format step quantity
    window.formatStepQuantityInput = function(input) {
        if (isCountUom()) {
            formatIntegerInput(input);
        } else {
            formatDecimalInput(input, 3);
        }
    };

    // Function to set default step quantity based on UOM type / Fungsi untuk set default step quantity berdasarkan jenis UOM
    function setDefaultStepQuantity() {
        // console.log('setDefaultStepQuantity');
        const stepQuantityInput = document.getElementById('stepQuantity');
        if (stepQuantityInput && stepQuantityInput.value) {
            // Preserve existing value but format according to UOM type // Kekalkan nilai sedia ada tetapi format mengikut jenis UOM
            const currentValue = parseFloat(stepQuantityInput.value) || 1;

            if (isCountUom()) {
                // For count UOM, use whole number // Untuk UOM count, guna nombor bulat
                stepQuantityInput.value = Math.floor(currentValue).toString();
            } else {
                // For other UOM types, format to 3 decimal places // Untuk jenis UOM lain, format ke 3 tempat desimal
                stepQuantityInput.value = currentValue.toFixed(3);
            }
        }
    }

    // Listen for UOM changes to update stock input behavior / Dengar perubahan UOM untuk kemas kini kelakuan input stok
    uomSelect.addEventListener('change', function() {
        const isCount = isCountUom();

        // Reset stock input value when UOM changes / Reset nilai input stok bila UOM berubah
        if (stockInput.value !== 'Ready Stock' && stockInput.value !== '') {
            // Re-format current value based on new UOM type / Format semula nilai semasa berdasarkan jenis UOM baru
            formatDecimalInput(stockInput, 3);
        }

        // Update step quantity based on UOM type / Kemas kini step quantity berdasarkan jenis UOM
        const stepQuantityInput = document.getElementById('stepQuantity');
        if (stepQuantityInput) {
            if (isCount) {
                // Convert to integer format / Tukar ke format integer
                if (stepQuantityInput.value && stepQuantityInput.value !== '') {
                    stepQuantityInput.value = Math.floor(parseFloat(stepQuantityInput.value) || 1).toString();
                } else {
                    stepQuantityInput.value = '1';
                }
            } else {
                // Convert to decimal format / Tukar ke format decimal
                if (stepQuantityInput.value && stepQuantityInput.value !== '') {
                    const value = parseFloat(stepQuantityInput.value) || 1;
                    stepQuantityInput.value = value.toFixed(3);
                } else {
                    stepQuantityInput.value = '1.000';
                }
            }
        }

        // Update all existing variant stock inputs based on new UOM type / Kemas kini semua input stok variant berdasarkan jenis UOM baru
        const variantStockInputs = document.querySelectorAll('.variant-stock-input');
        variantStockInputs.forEach(input => {
            // Skip if input is "Ready Stock" / Langkau jika input "Ready Stock"
            if (input.value === 'Ready Stock' || input.value === '') {
                return;
            }

            const currentValue = input.value.toString();

            if (isCount) {
                // Remove decimal point completely / Buang titik perpuluhan sepenuhnya
                // Example: 323.525 -> 323525
                input.value = currentValue.replace('.', '');
            } else {
                // Add decimal point 3 places from the end / Tambah titik perpuluhan 3 tempat dari belakang
                // Example: 323434 -> 323.434
                if (!currentValue.includes('.')) {
                    const numStr = currentValue;
                    if (numStr.length > 3) {
                        const integerPart = numStr.slice(0, -3);
                        const decimalPart = numStr.slice(-3);
                        input.value = integerPart + '.' + decimalPart;
                    } else {
                        // If less than 3 digits, pad with zeros and add decimal / Jika kurang dari 3 digit, tambah sifar dan titik perpuluhan
                        const paddedNum = numStr.padStart(3, '0');
                        input.value = '0.' + paddedNum;
                    }
                }
            }
        });
    });

    // Form reset handler
    window.resetForm = function() {
        const form = document.getElementById('productForm');
        form.addEventListener('reset', function() {
            // Reset variant checkbox to original state and show/hide fields accordingly // Reset checkbox variant ke keadaan asal dan tunjuk/sembunyi medan mengikutnya
            const originalVariantState = {{ $products->isVariant == 'Y' ? 'true' : 'false' }};
            isVariantCheckbox.checked = originalVariantState;
            variantSection.style.display = originalVariantState ? 'block' : 'none';
            togglePriceAndStockFields(originalVariantState); // Show/hide fields based on original state

            // Reset the input field to original values // Set semula medan input ke nilai asal
            if (!originalVariantState) {
                // If product originally didn't have variants, reset to original values // Jika produk asal tidak ada variant, reset ke nilai asal
                stockInput.value = '{{ old('productInventory', $products->product_stock === '-100.000' ? 'Ready Stock' : $products->product_stock) }}';
                stockInput.dataset.actualValue = '{{ $products->product_stock }}';
                stockInput.readOnly = {{ $products->product_stock == '-100.000' ? 'true' : 'false' }};
                trackInventoryCheckbox.checked = {{ old('readyStock', $products->product_stock != '-100') ? 'true' : 'false' }};

                // Reset price to original value // Reset harga ke nilai asal
                priceInput.value = '{{ old('productPrice', $products->product_price) }}';
            }
        });

        // Reset main product image to original + set semula gambar produk utama ke asal
        previewImage.src = "{{ $products->productDetail && $products->productDetail->product_attachment ? 'https://corrad.visionice.net/bizapp/upload/product/'.$products->productDetail->product_attachment : asset('assets/images/add-product.png') }}";

        // Reset variant images to original + set semula gambar variant ke asal
        @if($products->isVariant == 'Y' && $products->variants->isNotEmpty())
            @foreach($variantData as $index => $variant)
                const variantImg{{ $index }} = document.getElementById('preview-variant-{{ $index }}');
                if (variantImg{{ $index }}) {
                    variantImg{{ $index }}.src = "{{ $variant['variant']->productDetail && $variant['variant']->productDetail->product_attachment ? 'https://corrad.visionice.net/bizapp/upload/product/'.$variant['variant']->productDetail->product_attachment : asset('assets/images/add-product.png') }}";
                }
            @endforeach
        @endif

        updateStockInput();
    };

    // Variant functionality
    const isVariantCheckbox = document.getElementById('isVariant');
    const variantSection = document.getElementById('variantSection');
    const variant1Select = document.getElementById('variant1');
    const variant2Select = document.getElementById('variant2');
    const btnAddVariant = document.getElementById('btnAddVariant');
    const variantRowsContainer = document.getElementById('variantRowsContainer');
    const variantInputs = variantSection.querySelectorAll('input, select');  // All form elements in variantSection
    const variantRowsContainerInput = variantRowsContainer.querySelectorAll('input, select');

    // Get price and stock elements for hiding/showing // Dapatkan elemen harga dan stok untuk sembunyi/tunjuk
    const priceContainer = priceInput.closest('.form-group');
    const stockContainer = stockInput.closest('.form-group');

    // Store original values for restoration // Simpan nilai asal untuk pemulihan
    let originalPriceValue = '';
    let originalStockValue = '';
    let originalTrackInventoryState = false;

    // Function to toggle price and stock visibility // Fungsi untuk togol ketampakan harga dan stok
    function togglePriceAndStockFields(hideFields) {
        if (hideFields) {
            // Store current values before hiding // Simpan nilai semasa sebelum menyembunyikan
            originalPriceValue = priceInput.value;
            originalStockValue = stockInput.value;
            originalTrackInventoryState = trackInventoryCheckbox.checked;

            // Hide fields // Sembunyikan medan
            priceContainer.style.display = 'none';
            stockContainer.style.display = 'none';

            // Set default values // Tetapkan nilai lalai
            priceInput.value = '1.00';
            stockInput.value = 'Ready Stock';
            stockInput.dataset.actualValue = '-100';
            trackInventoryCheckbox.checked = false;
            stockInput.readOnly = true;
        } else {
            // Show fields // Tunjukkan medan
            priceContainer.style.display = 'block';
            stockContainer.style.display = 'block';

            // Restore original values // Pulihkan nilai asal
            priceInput.value = originalPriceValue;
            stockInput.value = originalStockValue;
            trackInventoryCheckbox.checked = originalTrackInventoryState;
            updateStockInput();
        }
    }

    // Initialize field visibility based on product variant state // Inisialisasi ketampakan medan berdasarkan keadaan variant produk
    if ({{ $products->isVariant == 'Y' ? 'true' : 'false' }}) {
        togglePriceAndStockFields(true); // Hide fields if product has variants
    }

    [variant1Select, variant2Select].forEach(select => {
        select.addEventListener('change', function() {
            const variant1Value = variant1Select.value;
            const variant2Value = variant2Select.value;

            if(!variant1Value){
                variant2Select.setAttribute('disabled', true);
            }else{
                variant2Select.removeAttribute('disabled');
            }

            if(variant2Value && !variant1Value){
                alert('Please select variants 1 first');
                this.value = '';
                document.getElementById('variant2').value = '';
            }

            if (variant1Value && variant2Value && variant1Value === variant2Value) {
                alert('Please select different variants');
                this.value = '';
            }
        });
    });

    let rowCounter = {{ $products->variants->count() ?? 0 }};

    // Initialize existing variant stock inputs with proper UOM formatting / Inisialisasi input stok variant sedia ada dengan format UOM yang betul
    @if($products->isVariant == 'Y' && $products->variants->isNotEmpty())
        @foreach($variantData as $index => $variant)
            // Initialize stock input for existing variant {{ $index }} / Inisialisasi input stok untuk variant sedia ada {{ $index }}
            const existingStockInput{{ $index }} = document.getElementById('variantStock_{{ $index }}');
            const existingCheckbox{{ $index }} = document.getElementById('trackStockCheckbox_{{ $index }}');

            if (existingStockInput{{ $index }} && existingCheckbox{{ $index }}) {
                // Add event listener for UOM-based formatting on existing variants / Tambah event listener untuk format berdasarkan UOM pada variant sedia ada
                if (existingCheckbox{{ $index }}.checked) {
                    existingStockInput{{ $index }}.addEventListener('input', function() {
                        formatVariantStockInput(this, {{ $index }});
                    });
                }
            }
        @endforeach
    @endif

    // Function to disable inputs without values
    function disableEmptyInputs() {
        const variant1Value = variant1Select.value;
            if(!variant1Value){
                variant1Select.setAttribute('disabled', true);
            }
        variantInputs.forEach(input => {
            if (input === variant1Select && input.value === "") {
                return;  // Do nothing for variant2Select
            }
            if (!input.value || input.value === "") {
                input.disabled = true;  // Disable input if it's empty
            } else {
                input.disabled = false;  // Enable input if it has a value
            }
        });

        variantRowsContainer.querySelectorAll('input, select').forEach(input => {
            if (!input.value || input.value === "") {
                input.disabled = true;  // Disable input if it's empty
            } else {
                input.disabled = false;  // Enable input if it has a value
            }
        });
    }

    // Show/hide variant section based on checkbox
    isVariantCheckbox.addEventListener('change', function() {
        if (!this.checked && {{ $products->isVariant == 'Y' ? 'true' : 'false' }}) {
            // If the checkbox is being unchecked and product previously had variants
            const checkBoxModal = new bootstrap.Modal(document.getElementById('checkBoxModal'));
            checkBoxModal.show();

            // Set the checkbox back to checked state until user confirms
            this.checked = true;

            // Handle the confirm button click
            document.getElementById('confirmClear').onclick = function() {
                // User confirmed, uncheck the box and hide the variant section
                isVariantCheckbox.checked = false;
                variantSection.style.display = 'none';
                togglePriceAndStockFields(false); // Show price and stock fields
                checkBoxModal.hide();

                disableEmptyInputs();
            };
        } else {
            // Normal behavior for checking the box or if product didn't have variants before
            variantSection.style.display = this.checked ? 'block' : 'none';

            // Toggle price and stock fields based on variant checkbox // Togol medan harga dan stok berdasarkan checkbox variant
            togglePriceAndStockFields(this.checked);

            !this.checked ? disableEmptyInputs() : '';
        }
    });


    // Add click handler for Generate button
    document.getElementById('btnGenerateVariant').addEventListener('click', function() {
        generateVariantCombinations('comboVariant');
    });

    document.getElementById('btnAddVariant').addEventListener('click', function() {
        generateVariantCombinations('normal');
    });

    function generateVariantCombinations(combo) {
        const variant1Value = variant1Select.value;
        const variant2Value = variant2Select.value;
        extendSku = '';
        const baseProductSKU = document.querySelector('input[name="productSKU"]').value;

        if (!variant1Value && !variant2Value) {
            alert('Please select at least one variant');
            return;
        }

        // Disable Add button
        // document.getElementById('btnAddVariant').readonly = true;
        document.getElementById('btnGenerateVariant').setAttribute('disabled', true);

        // Get values for selected variants
        const values1 = variant1Value ? variantValues[variant1Value] : [null];
        const values2 = variant2Value ? variantValues[variant2Value] : [null];

        // Generate combinations
        if(combo === "comboVariant"){
            values1.forEach(val1 => {
                if (variant2Value) {
                    values2.forEach(val2 => {
                            createVariantRowWithValues(val1, val2, baseProductSKU);
                        });
                } else {
                    createVariantRowWithValues(val1, null, baseProductSKU);

                }
            });
        }else{
            createVariantRow(variant1Value, variant2Value, baseProductSKU);
        }

        // Disable variant selects after generating rows
        variant1Select.setAttribute('disabled', true);
        variant2Select.setAttribute('disabled', true);

    }


    // Function to create a new variant row + fungsi cipta baris variant baru
    function createVariantRow(variant1Value, variant2Value ,baseProductSKU) {
        let skuSuffix = '';


        const variantSKU = baseProductSKU;

        const row = document.createElement('div');
        row.className = 'row border rounded p-3 mb-3 position-relative align-items-center';
        row.dataset.rowId = `row-${rowCounter}`;

        // Check if rowCounter is even and apply the 'striped' class
        if (rowCounter % 2 === 0) {
            row.classList.add('striped'); // Add a striped class to even rows
        }

        document.getElementById('btnGenerateVariant').setAttribute('disabled', true);

        let html = `
            <!-- Column 1: Image Preview + Pratonton Gambar -->
            <div class="col-md-2 mb-2">
                <div class="variant-image-container" style="width: 110px; height: 110px; border: 1px dashed #ddd; border-radius: 8px; overflow: hidden; margin: 0 auto;">
                    <img id="preview-variant-${rowCounter}" src="{{ asset('assets/images/add-product.png') }}"
                        class="img-fluid preview-image" style="width: 100%; height: 110px; object-fit: contain;" alt="Variant Image">
                </div>
                <input type="file" class="form-control mt-2" name="variants[${rowCounter}][main-image]"
                    onchange="previewVariantImage(this, ${rowCounter})" accept="image/*">
            </div>

            <!-- Column 2: Name and SKU -->
            <div class="col-md-3 mb-2">
                <div class="mb-2">
                    <label class="form-label required">Product Name</label>
                    <input type="text" class="form-control" name="variants[${rowCounter}][name]" id="variant_${rowCounter}_name" value="${variantSKU}"
                    placeholder="Enter product Name" required>
                </div>
                <div>
                    <label class="form-label required">Stock</label>
                    <div class="d-flex gap-2 align-items-center">
                        <input type="text" class="form-control variant-stock-input"
                            name="variants[${rowCounter}][stock]"
                            id="variantStock_${rowCounter}"
                            value="Ready Stock"
                            data-actual-value="-100.000"
                            data-original-value="-100.000"
                            readonly
                            required>
                        <div class="custom-checkbox">
                            <input type="checkbox" id="trackStockCheckbox_${rowCounter}"
                                onchange="updateVariantStockInput(${rowCounter})">
                            <label for="trackStockCheckbox_${rowCounter}">Track inventory</label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Column 3: Stock and Price -->
            <div class="col-md-3 mb-2">
                <div class="mb-2">
                    <label class="form-label required">Product SKU</label>
                    <input type="text" class="form-control" name="variants[${rowCounter}][sku]" id="variant_${rowCounter}_sku" value="${variantSKU}"
                    placeholder="Enter product SKU">
                </div>
                <div>
                    <label class="form-label required">Price</label>
                    <div class="input-group">
                        <span class="input-group-text">{{ $user->userDetails->currency ?? 'RM' }}</span>
                        <input type="number" step="0.01" min="0" class="form-control" name="variants[${rowCounter}][price]"
                        placeholder="0.00" required>
                    </div>
                </div>
            </div>

            <!-- Column 4: Variant Values
            <div class="col-md-4 mb-2">-->`;

        // Add Variant 1 dropdown if selected
        if (variant1Value && variant2Value) {
            html += `
                <div class="col-md-3 mb-2">
                    <div class="mb-2">
                        <label class="form-label required">Variant 1</label>
                        <select class="form-select" id="variant1_${rowCounter}" name="variants[${rowCounter}][variant1_value]"
                            onchange="validateVariantSelection(this, ${rowCounter}, '${variantSKU}')" required>
                            <option value="">Select Value</option>
                            ${getVariantOptions(variant1Value)}
                        </select>
                    </div>
                    <div>
                        <label class="form-label required">Variant 2</label>
                        <select class="form-select" id="variant2_${rowCounter}" name="variants[${rowCounter}][variant2_value]"
                            onchange="validateVariantSelection(this, ${rowCounter}, '${variantSKU}')" required>
                            <option value="">Select Value</option>
                            ${getVariantOptions(variant2Value)}
                        </select>
                    </div>
                </div>`;
        } else if (variant1Value || variant2Value) {
            html += `
                <div class="col-md-3 mb-2 justify-start align-items-start">
                    <div class="mb-2">
                        <label class="form-label required">Variant</label>
                        <select class="form-select" id="variant1_${rowCounter}" name="variants[${rowCounter}][variant1_value]"
                        onchange="validateVariantSelection(this, ${rowCounter}, '${variantSKU}')" required>
                            <option value="">Select Value</option>
                            ${getVariantOptions(variant1Value || variant2Value)}
                        </select>
                    </div>
                    <div class="flex-grow"></div>
                    </div>
                </div>`;
        }

        html += `
            <!-- Column 5: Delete Button + Butang Padam -->
            <div class="col-md-1 mb-2 d-flex justify-content-center align-items-center" style="height: 100%;">
                <button type="button" class="btn btn-danger" onclick="showDeleteConfirmModal(this, ${rowCounter})">
                    <i class="bi bi-trash"></i>
                </button>
            </div>`;

        row.innerHTML = html;
        variantRowsContainer.appendChild(row);
        rowCounter++;
    }

    function createVariantRowWithValues(value1, value2, baseProductSKU) {
        // Generate SKU suffix based on variant values
        let skuSuffix = '';
        if (value1) {
            skuSuffix += `${value1.value.toUpperCase()}`;
        }
        if (value2) {
            skuSuffix += `-${value2.value.toUpperCase()}`;
        }

        const variantSKU = baseProductSKU + '-' +skuSuffix;

        const row = document.createElement('div');
        row.className = 'row border rounded p-3 mb-3 position-relative align-items-center';
        row.dataset.rowId = `row-${rowCounter}`;

        stringSku[`row_${rowCounter}`]  = skuSuffix;

        // Check if rowCounter is even and apply the 'striped' class
        if (rowCounter % 2 === 0) {
            row.classList.add('striped'); // Add a striped class to even rows
        }

        document.getElementById('btnGenerateVariant').setAttribute('disabled', true);

        let html = `
            <!-- Column 1: Image Preview + Pratonton Gambar -->
            <div class="col-md-2 mb-2">
                <div class="variant-image-container" style="width: 110px; height: 110px; border: 1px dashed #ddd; border-radius: 8px; overflow: hidden; margin: 0 auto;">
                    <img id="preview-variant-${rowCounter}" src="{{ asset('assets/images/add-product.png') }}"
                        class="img-fluid preview-image" style="width: 100%; height: 110px; object-fit: contain;" alt="Variant Image">
                </div>
                <input type="file" class="form-control mt-2" name="variants[${rowCounter}][main-image]"
                    onchange="previewVariantImage(this, ${rowCounter})" accept="image/*">
            </div>

            <!-- Column 2: Name and SKU + Nama dan SKU -->
            <div class="col-md-3 mb-2">
                <div class="mb-2">
                    <label class="form-label required">Product Name</label>
                    <input type="text" class="form-control" name="variants[${rowCounter}][name]" id="variant_${rowCounter}_name" value="${variantSKU}"
                    placeholder="Enter product Name" required>
                </div>
                <div>
                    <label class="form-label required">Stock</label>
                    <div class="d-flex gap-2 align-items-center">
                        <input type="text" class="form-control variant-stock-input"
                            name="variants[${rowCounter}][stock]"
                            id="variantStock_${rowCounter}"
                            value="Ready Stock"
                            data-actual-value="-100.000"
                            data-original-value="-100.000"

                            required>
                        <div class="custom-checkbox">
                            <input type="checkbox" id="trackStockCheckbox_${rowCounter}"
                                onchange="updateVariantStockInput(${rowCounter})">
                            <label for="trackStockCheckbox_${rowCounter}">Track inventory</label>
                        </div>
                    </div>

                </div>
            </div>

            <!-- Column 3: Stock and Price -->
            <div class="col-md-3 mb-2">
                <div class="mb-2">
                    <label class="form-label required">Product SKU</label>
                    <input type="text" class="form-control" name="variants[${rowCounter}][sku]" id="variant_${rowCounter}_sku" value="${variantSKU}"
                    placeholder="Enter product SKU">
                </div>
                <div>
                    <label class="form-label required">Price</label>
                    <div class="input-group">
                        <span class="input-group-text">{{ $user->userDetails->currency ?? 'RM' }}</span>
                        <input type="number" step="0.01" min="0" class="form-control" name="variants[${rowCounter}][price]"
                        placeholder="0.00" required>
                    </div>
                </div>
            </div>

            <!-- Column 4: Variant Values
            <div class="col-md-4 mb-2">-->`;

        if (value1 && value2) {
            html += `
                <div class="col-md-3 mb-2">
                    <div class="mb-2">
                        <label class="form-label required">Variant 1</label>
                        <select class="form-select" id="variant1_${rowCounter}"
                            name="variants[${rowCounter}][variant1_value]"
                            onchange="validateVariantSelection(this, ${rowCounter})" disabled>
                            <option value="">Select Value</option>
                            ${getVariantOptions(variant1Select.value, value1.id)}
                        </select>
                    </div>
                    <div>
                        <label class="form-label required">Variant 2</label>
                        <select class="form-select" id="variant2_${rowCounter}"
                            name="variants[${rowCounter}][variant2_value]"
                            onchange="validateVariantSelection(this, ${rowCounter})" disabled>
                            <option value="">Select Value</option>
                            ${getVariantOptions(variant2Select.value, value2.id)}
                        </select>
                    </div>
                </div>`;
        } else {
            const variantValue = value1 || value2;
            const variantId = variant1Select.value || variant2Select.value;
            html += `
                <div class="col-md-3 mb-2">
                    <div class="mb-2">
                        <label class="form-label required">Variant</label>
                        <select class="form-select" id="variant1_${rowCounter}" name="variants[${rowCounter}][variant1_value]"
                        onchange="validateVariantSelection(this, ${rowCounter})" disabled>
                            <option value="">Select Value</option>
                            ${getVariantOptions(variantId, variantValue.id)}
                        </select>
                    </div>
                </div>`;
        }

        html += `
            <!-- Column 5: Delete Button + Butang Padam -->
            <div class="col-md-1 mb-2 d-flex justify-content-center align-items-center" style="height: 100%;">
                <button type="button" class="btn btn-danger" onclick="showDeleteConfirmModal(this, ${rowCounter})">
                    <i class="bi bi-trash"></i>
                </button>
            </div>`;

        row.innerHTML = html;
        variantRowsContainer.appendChild(row);
        rowCounter++;
    }

    function getVariantOptions(variantId, selectedValueId) {
        if (!variantValues[variantId]) return '';
        return variantValues[variantId]
            .map(value => `<option value="${value.id}" ${value.id === selectedValueId ? 'selected' : ''}>${value.value}</option>`)
            .join('');
    }

    // Update deleteVariantRow function to be globally accessible
    window.deleteVariantRow = function(button, index) {
        const row = button.closest('.row');

        // Check if this row has a variant ID (existing variant)
        const variantIdInput = row.querySelector('input[name^="variants"][name$="[id]"]');
        if (variantIdInput && variantIdInput.value) {
            // Create a hidden input for the deleted variant ID
            const deletedInput = document.createElement('input');
            deletedInput.type = 'hidden';
            deletedInput.name = 'deleted_variants[]';
            deletedInput.value = variantIdInput.value;

            // Add to the hidden container
            document.getElementById('deletedVariantsContainer').appendChild(deletedInput);
        }

        row.remove();

        if(index){
            // stringSku = stringSku.filter(item => !item.hasOwnProperty('row_${index}'));
            let keyToRemove = `row_${index}`;
            delete stringSku[keyToRemove];
            // stringSku.splice(index, 1);
        }
        // document.getElementById('btnGenerateVariant').disbaled = false;

        // If no rows left, enable variant selects
        if (variantRowsContainer.children.length === 0) {
            rowCounter = 0;
            variant1Select.removeAttribute('disabled');
            variant2Select.removeAttribute('disabled');
            stringSku = [];
            oldStringSku = [];
            // document.getElementById('btnGenerateVariant').disbaled = false;
            // document.getElementById('btnAddVariant').disbaled = false;
            document.getElementById('btnGenerateVariant').removeAttribute('disabled');
            document.getElementById('btnAddVariant').removeAttribute('disabled');
        }
    };

    // Add click handler for Add button
    // btnAddVariant.addEventListener('click', createVariantRow);

    // Add clear variants function
    function clearVariants() {
        if (confirm('Are you sure you want to clear all variants? This action cannot be undone.')) {
            // Collect all existing variant IDs and add them to the deleted_variants container
            const variantRows = variantRowsContainer.querySelectorAll('.row');
            variantRows.forEach(row => {
                // Find the variant ID input in each row
                const variantIdInput = row.querySelector('input[name^="variants"][name$="[id]"]');
                if (variantIdInput && variantIdInput.value) {
                    // Create a hidden input for the deleted variant ID
                    const deletedInput = document.createElement('input');
                    deletedInput.type = 'hidden';
                    deletedInput.name = 'deleted_variants[]';
                    deletedInput.value = variantIdInput.value;

                    // Add to the hidden container
                    document.getElementById('deletedVariantsContainer').appendChild(deletedInput);
                }
            });

            // Clear all variant rows
            variantRowsContainer.innerHTML = '';

            // Enable and reset variant selects
            // variant1Select.disbaled = false;
            // variant2Select.disbaled = false;
            variant1Select.value = '';
            variant2Select.value = '';

            // console.log(variant1Select, variant2Select);

            variant1Select.removeAttribute('disabled');
            // variant2Select.removeAttribute('disabled');

            // variant2Select.disbaled = true;

            rowCounter = 0;
            stringSku = [];

            oldStringSku = [];

            // Re-enable Add button
            // .disbaled = false;
            // document.getElementById('btnAddVariant').disbaled = false;
            document.getElementById('btnGenerateVariant').removeAttribute('disabled');
            document.getElementById('btnAddVariant').removeAttribute('disabled');
        }
    }

    // Update the clear button event listener
    document.getElementById('btnClearVariant').addEventListener('click', clearVariants);

    // Add this function for image preview
    window.previewVariantImage = function(input, rowId) {
        if (input.files && input.files[0]) {
            // Validate file size / Sahkan saiz fail
            const maxSize = 5 * 1024 * 1024; // 5MB in bytes
            if (input.files[0].size > maxSize) {
                alert('File size exceeds 5MB limit. Please choose a smaller file.');
                input.value = ''; // Clear the input
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                document.getElementById(`preview-variant-${rowId}`).src = e.target.result;
            };
            reader.readAsDataURL(input.files[0]);
        }
    };

    const productSkuInput = document.getElementById("product_skus");
    var extendSku = '';
    var baseSku = '';
    var stringSku = {};
    // const newSkuLabel = document.getElementById("new_sku_label");

    productSkuInput.addEventListener("input", function () {
        // console.log(variantRowsContainer.children.length);

        for(let i = 0; i < rowCounter; i++){
            let indexKey = `row_${i}`;
            let newStringKey = stringSku[indexKey];
            let oldStringKey = oldStringSku[indexKey];
            let skuElement = document.getElementById(`variant_${i}_sku`);
            if(oldStringKey){
                if (skuElement) {
                    skuElement.value = productSkuInput.value + '-' + oldStringKey;
                }
                // document.getElementById(`variant_${i}_sku`)?.value = productSkuInput.value + '-' + newStringKey;
            }else if(newStringKey){
                if (skuElement) {
                    skuElement.value = productSkuInput.value + '-' + newStringKey;
                }
            }else{
                if (skuElement) {
                    skuElement.value = productSkuInput.value;
                }
            }
        }
    });


    // Add this new function to validate variant selections
    window.validateVariantSelection = function(input, currentRow, baseSku) {
        const mainSKU = document.querySelector('input[name="productSKU"]').value;
        const currentVariant1 = document.getElementById(`variant1_${currentRow}`)?.value;
        const currentVariant2 = document.getElementById(`variant2_${currentRow}`)?.value;

        const values1 = variant1Select.value ?? [null];
        const values2 = variant2Select.value ?? [null];

        for (let i = 0; i < rowCounter; i++) {
            if(i === currentRow) continue;
            const existingVariant1 = document.getElementById(`variant1_${i}`)?.value;
            const existingVariant2 = document.getElementById(`variant2_${i}`)?.value;

            if(values1 != "" && values2 != ""){
                if (existingVariant1 && existingVariant2 && existingVariant1 === currentVariant1 && existingVariant2 === currentVariant2) {
                    alert('This variant value is already used. Please select a different value.');
                    document.getElementById(`variant_${currentRow}_sku`).value = mainSKU;
                    document.getElementById(`variant1_${currentRow}`).value = '';
                    document.getElementById(`variant2_${currentRow}`).value = '';
                    return;
                }
            }else{
                if (existingVariant1 != undefined && existingVariant1 === currentVariant1) {
                    alert('This variant value is already used. Please select a different value.');
                    document.getElementById(`variant_${currentRow}_sku`).value = mainSKU;
                    document.getElementById(`variant1_${currentRow}`).value = '';
                    return;
                }
            }
        }
        var skuNama = '';  // Reset skuNama
        var skuNama2 = ''; // Reset skuNama2


        // Ensure skuNama and skuNama2 are reset before processing
        if (values1 && values2) {
            // Reset before mapping
            skuNama = '';
            skuNama2 = '';

            // Check and set skuNama from values1
            const variant1 = variantValues[values1];
            variant1.map(value => {
                if (value.id === currentVariant1) {
                    skuNama = value.value;
                }
            });

            // Check and set skuNama2 from values2
            const variant2 = variantValues[values2];
            variant2.map(value => {
                if (value.id === currentVariant2) {
                    skuNama2 = value.value;
                }
            });
        } else {
            // Reset skuNama before mapping if only values1 exists
            skuNama = '';
            const variant1 = variantValues[values1];
            variant1.map(value => {
                if (value.id === currentVariant1) {
                    skuNama = value.value;
                }
            });
        }

        // stringSku[`row_${currentRow}`] = skuNama + '-' + skuNama2;
        // Construct the new SKU
        var newSku = '';
        if (currentVariant1 && currentVariant2) {
            newSku = mainSKU + '-' + skuNama + '-' + skuNama2;
            stringSku[`row_${currentRow}`]  = skuNama + '-' + skuNama2;
        } else if (currentVariant1) {
            newSku = mainSKU + '-' + skuNama;
            stringSku[`row_${currentRow}`] = skuNama;
        }

        // console.log(stringSku);

        // Set the updated SKU value in the input field
        document.getElementById(`variant_${currentRow}_sku`).value = newSku;
        document.getElementById(`variant_${currentRow}_name`).value = newSku;

        // // Auto-generate product name based on main product name and variant values / Auto-jana nama produk berdasarkan nama produk utama dan nilai variant
        // const mainProductName = document.querySelector('input[name="productName"]').value;
        // let variantName = mainProductName;

        // if (currentVariant1 && currentVariant2) {
        //     variantName = mainProductName + ' - ' + skuNama + ' - ' + skuNama2;
        // } else if (currentVariant1) {
        //     variantName = mainProductName + ' - ' + skuNama;
        // }

        // const variantNameInput = document.querySelector(`input[name="variants[${currentRow}][name]"]`);
        // if (variantNameInput) {
        //     variantNameInput.value = variantName;
        // }


    };

    // Function to format variant stock input based on UOM type / Fungsi untuk format input stok variant berdasarkan jenis UOM
    window.formatVariantStockInput = function(input, rowId) {
        // Skip formatting if input is "Ready Stock" / Langkau format jika input "Ready Stock"
        if (input.value === 'Ready Stock') {
            return;
        }

        // Check if UOM type is count, use integer formatting / Semak jika jenis UOM adalah count, guna format integer
        if (isCountUom()) {
            input.value = input.value.replace(/[^\d]/g, ''); // Only allow digits / Hanya benarkan angka
        } else {
            // Use decimal formatting for non-count UOM / Guna format decimal untuk UOM bukan count
            let value = input.value.replace(/[^\d.]/g, ''); // Only allow digits and dots / Hanya benarkan angka dan titik
            const parts = value.split('.');

            // Only allow one decimal point / Hanya benarkan satu titik decimal
            if (parts.length > 2) {
                value = parts[0] + '.' + parts.slice(1).join('');
            }

            // Limit decimal places to 3 / Had tempat decimal kepada 3
            if (parts[1]?.length > 3) {
                value = parts[0] + '.' + parts[1].slice(0, 3);
            }

            input.value = value;
        }
    };

        // Add this function to handle variant stock input updates and ensure proper formatting based on UOM
    window.updateVariantStockInput = function(rowId) {
        const stockInput = document.getElementById(`variantStock_${rowId}`);
        const trackInventoryCheckbox = document.getElementById(`trackStockCheckbox_${rowId}`);
        const originalValue = stockInput.dataset.originalValue || stockInput.dataset.actualValue;

        if (trackInventoryCheckbox.checked) {
            // When checkbox is checked (Track inventory) / Bila checkbox ditick (Track inventory)
            if (originalValue === '-100.000') {
                // Original product_stock was -100.000, show original stock value / Stok asal adalah -100.000, tunjuk nilai stok asal
                stockInput.value = '1';
                stockInput.readOnly = false;
                stockInput.placeholder = '';
            } else {
                // Original product_stock was not -100.000, show original stock value / Stok asal bukan -100.000, tunjuk nilai stok asal
                stockInput.value = originalValue;
                stockInput.readOnly = false;
                stockInput.placeholder = '';
            }

            // Reset actual value to original when tracking / Reset nilai actual ke asal bila track
            stockInput.dataset.actualValue = originalValue;

            // Add event listener with UOM-based formatting / Tambah event listener dengan format berdasarkan UOM
            stockInput.addEventListener('input', function() {
                formatVariantStockInput(this, rowId);
            });
        } else {
            // When checkbox is unchecked (Don't track inventory) / Bila checkbox tidak ditick (Jangan track inventory)
            if (originalValue === '-100.000') {
                // Original product_stock was -100.000, show empty with placeholder / Stok asal adalah -100.000, tunjuk kosong dengan placeholder
                stockInput.value = 'Ready Stock';
                // stockInput.placeholder = 'Enter stock';
                stockInput.dataset.actualValue = '';
            } else {
                // Original product_stock was not -100.000, show "Ready Stock" / Stok asal bukan -100.000, tunjuk "Ready Stock"
                stockInput.value = 'Ready Stock';
                stockInput.placeholder = '';
                stockInput.dataset.actualValue = '-100';
            }

            stockInput.readOnly = true;

            // Remove event listener when not tracking inventory / Buang event listener bila tidak track inventory
            stockInput.removeEventListener('input', function() {
                formatVariantStockInput(this, rowId);
            });
        }
    };

    // Add this to your document ready function
    const productForm = document.getElementById('productForm');
    productForm.addEventListener('submit', function(event) {
        // console.log('submit form');
        event.preventDefault(); // Prevent default form submission // Halang penghantaran borang lalai

        // Validate step quantity before submission / Validasi step quantity sebelum hantar borang
        const stepQuantityInput = document.getElementById('stepQuantity');
        if (stepQuantityInput) {
            const stepQuantityValue = parseFloat(stepQuantityInput.value) || 0;

            if (isCountUom()) {
                // For count UOM, minimum value is 1 / Untuk UOM count, nilai minimum ialah 1
                if (stepQuantityValue < 1) {
                    document.getElementById('loading-screen').style.display = 'none';
                    Swal.fire({
                        icon: 'error',
                        title: 'Invalid Step Quantity',
                        text: 'Step quantity for count items cannot be less than 1.',
                    });
                    return;
                }
            } else {
                // For decimal UOM, minimum value is 0.001 / Untuk UOM decimal, nilai minimum ialah 0.001
                if (stepQuantityValue < 0.001) {
                    document.getElementById('loading-screen').style.display = 'none';
                    Swal.fire({
                        icon: 'error',
                        title: 'Invalid Step Quantity',
                        text: 'Step quantity cannot be less than 0.001.',
                    });
                    return;
                }
            }
        }

        // If empty fields found, show alert and prevent form submission
        if(rowCounter <= 0 && isVariantCheckbox.checked) {
            Swal.fire({
                icon: 'error',
                title: 'Oops...',
                text: 'No variant product found!',
            });
            return;
        }

        // loop variantes and buat foreach untuk check setiap sku. kalau ada yang sama terus ada popup
        const seen = new Set();
        let duplicateSku = null;

        for(let i=0; i < rowCounter; i++){
            const element = document.getElementById(`variant_${i}_sku`);
            if (!element) continue; //check dulu row ni exist ke tak

            const sku = element.value;
            if (seen.has(sku)) {
                duplicateSku = sku;
                break;
            }
            seen.add(sku);
        }

        if (duplicateSku) {
            document.getElementById('loading-screen').style.display = 'none';
            Swal.fire({
                icon: 'error',
                title: 'Oops...',
                text: `Duplicate SKU found: ${duplicateSku}`,
            });
            return;
        }

        // document.querySelectorAll('select:disabled').forEach(select => {
        //     select.disabled = false;
        //     select.removeAttribute('disabled');
        // });

        document.querySelectorAll('select').forEach(select => {
            const hidden = document.createElement('input');
            hidden.type = 'hidden';
            hidden.name = select.name;
            hidden.value = select.value;

            // Avoid duplicate hidden inputs
            if (!select.parentNode.querySelector(`input[name="${select.name}"]`)) {
                select.parentNode.appendChild(hidden);
            }
        });

        // Process all variant stock inputs
        document.querySelectorAll('.variant-stock-input').forEach(input => {
            if (input.value === 'Ready Stock') {
                input.value = '-100';
            }else{
                input.dataset.actualValue = input.value;
            }
        });

        // Now check SKUs via AJAX // Sekarang semak SKU melalui AJAX
        checkSkusBeforeSubmit();
    });

    // Function to check SKUs via AJAX before form submission // Fungsi untuk semak SKU melalui AJAX sebelum hantar borang
    function checkSkusBeforeSubmit() {
        document.getElementById('loading-screen').style.display = 'block';

        // console.log('checking SKUs for edit form');

        // Collect all SKUs to check // Kumpul semua SKU untuk disemak
        const skusToCheck = [];

        // Add main product SKU // Tambah SKU produk utama
        const mainSku = document.querySelector('input[name="productSKU"]').value;
        const mainProductName = document.querySelector('input[name="productName"]').value;
        const currentProductId = document.getElementById('main_product_id').value; // Current product ID to exclude from checks

        if (mainSku) {
            skusToCheck.push({
                sku: mainSku,
                name: mainProductName,
                type: 'main',
                exclude_product_id: currentProductId
            });
        }

        // Add variant SKUs if any // Tambah SKU variant jika ada
        if (isVariantCheckbox.checked) {
            for(let i = 0; i < rowCounter; i++){
                const skuElement = document.getElementById(`variant_${i}_sku`);
                const nameElement = document.querySelector(`input[name="variants[${i}][name]"]`);
                const variantIdElement = document.querySelector(`input[name="variants[${i}][id]"]`);

                if (skuElement && nameElement && skuElement.value) {
                    skusToCheck.push({
                        sku: skuElement.value,
                        name: nameElement.value,
                        type: 'variant',
                        exclude_variant_id: variantIdElement ? variantIdElement.value : null
                    });
                }
            }
        }

        // console.log(skusToCheck);

        // Make AJAX request to check all SKUs // Buat permintaan AJAX untuk semak semua SKU
        fetch('{{ route('product.check.multiple.sku.edit') }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                skus: skusToCheck
            })
        })
        .then(response => response.json())
        .then(data => {
            document.getElementById('loading-screen').style.display = 'none';

            // console.log(data);

            if (data.result === false) {
                // Show error popup and don't submit form // Tunjuk popup ralat dan jangan hantar borang
                Swal.fire({
                    icon: 'error',
                    title: 'SKU Duplicate Found!',
                    text: data.message,
                });
            } else {
                // All SKUs are available, proceed with form submission // Semua SKU tersedia, teruskan dengan penghantaran borang
                submitFormAfterValidation();
            }
        })
        .catch(error => {
            document.getElementById('loading-screen').style.display = 'none';
            console.error('Error:', error);
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: 'Failed to check SKU. Please try again.',
            });
        });
    }

    // Function to actually submit the form after validation // Fungsi untuk benar-benar hantar borang selepas pengesahan
    function submitFormAfterValidation() {
        document.getElementById('loading-screen').style.display = 'block';

        // Create a new form submission without the event listener // Buat penghantaran borang baharu tanpa event listener
        const formData = new FormData(productForm);

        // Submit using fetch to avoid infinite loop // Hantar menggunakan fetch untuk elak gelung tanpa had
        fetch(productForm.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
            }
        })
        .then(response => {
            if (response.redirected) {
                // If server redirects, follow it // Jika pelayan mengalihkan, ikuti
                window.location.href = response.url;
            } else {
                return response.text();
            }
        })
        .then(html => {
            if (html) {
                // If there's HTML content, it might be an error page // Jika ada kandungan HTML, mungkin halaman ralat
                document.open();
                document.write(html);
                document.close();
                document.getElementById('loading-screen').style.display = 'none';
            }
        })
        .catch(error => {
            document.getElementById('loading-screen').style.display = 'none';
            console.error('Error:', error);
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: 'Failed to submit form. Please try again.',
            });
        });
    }

    // Function to validate number input
    window.validateNumberInput = function(input) {
        input.value = input.value.replace(/[^0-9]/g, '');
    };

    // Add this function to disable buttons
    window.disableButtons = function() {
        // document.getElementById('btnGenerateVariant').disabled = true;
        document.getElementById('btnGenerateVariant').setAttribute('disabled', true);
        // document.getElementById('btnAddVariant').readonly = true;
    };

    // Function to update hidden variant1 input when select changes
    // window.updateHiddenVariant1 = function() {
    //     const selectElement = document.getElementById('variant1');
    //     const hiddenInput = document.getElementById('hidden_variant1');
    //     if (selectElement && hiddenInput) {
    //         hiddenInput.value = selectElement.value;
    //     }
    // }

    // // Function to update hidden variant2 input when select changes
    // window.updateHiddenVariant2 = function() {
    //     const selectElement = document.getElementById('variant2');
    //     const hiddenInput = document.getElementById('hidden_variant2');
    //     if (selectElement && hiddenInput) {
    //         hiddenInput.value = selectElement.value;
    //     }
    // }

    // // Initialize the hidden input with the current select value on page load
    // updateHiddenVariant1();
    // updateHiddenVariant2();
});

// Add this function to show the delete confirmation modal
window.showDeleteConfirmModal = function(button, index) {

    const row = button.closest('.row');
    const variantIdInput = row.querySelector('input[name^="variants"][name$="[id]"]');

    // Only show confirmation popup if variant ID exists
    if (variantIdInput && variantIdInput.value) {
        const deleteConfirmModal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
        document.getElementById('deleteRowIndex').value = index;

        // Remove any existing event listeners by cloning and replacing the button
        const confirmBtn = document.getElementById('confirmDeleteBtn');
        const newConfirmBtn = confirmBtn.cloneNode(true);
        confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);

        // Add the event listener to the new button
        document.getElementById('confirmDeleteBtn').addEventListener('click', function() {
            deleteVariantRow(button, index);
            deleteConfirmModal.hide();
        });

        deleteConfirmModal.show();
    } else {
        // If variant ID doesn't exist, delete directly without confirmation
        deleteVariantRow(button, index);
    }
}
</script>
@endpush
