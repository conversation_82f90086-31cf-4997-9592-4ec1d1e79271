@extends('backend.layout.default')

@section('title', 'Manage Subscription Plans')

@push('css')
<link href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css" rel="stylesheet">
@endpush

@section('content')
<div class="page-heading">
    <div class="page-title">
        <div class="row">
            <div class="col-12 col-md-6 order-md-1 order-last">
                <h3>LHDN E-Invoice</h3>
                <p class="text-subtitle text-muted">
                    Manage your e-Invoice integration here
                </p>
            </div>
            <div class="col-12 col-md-6 order-md-2 order-first">
                <nav aria-label="breadcrumb" class="breadcrumb-header float-start float-lg-end">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item">
                            <a href="{{ route('dashboard') }}">Integration</a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">
                            LHDN E-Invoice
                        </li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <section class="section">
        <div class="card mt-4">
            {{-- <div class="card-header">
                <h6>General Info</h6>
            </div> --}}
            <div class="card-body">
                <div class="card-header d-flex justify-content-center">
                    {{-- <a href="{{ route('lhdn.testjson') }}" class="btn btn-primary">Next Page</a> --}}

                    <div>
                        <button type="button" class="btn btn-outline-primary btn-sm me-1 toggle-btn active" id="btnGeneral">General Info</button>
                        <button type="button" class="btn btn-outline-primary btn-sm toggle-btn" id="btnTax">My Tax API</button>
                    </div>
                </div>
                @if (session('success'))
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        {{ session('success') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                @endif
                <form  action="{{ route('lhdn.store') }}" method="POST">
                    @csrf
                    <div id="generalInfoSection" class="transition-section" style="display: block;">
                        <div class="row">
                            <div class="form-group col-md-6">
                                <label for="employeeType" class="form-label">Select Type</label>
                                <select id="businessType" class="form-select" name="business_type" required>
                                    <option value="" disabled {{ $com['business_type'] == '' ? 'selected' : '' }}>Select Type</option>
                                    <option value="individual" {{ $com['business_type'] == 'individual' ? 'selected' : '' }}>Individual</option>
                                    <option value="business" {{ $com['business_type'] == 'business' ? 'selected' : '' }}>Business</option>
                                </select>
                                @error('business_type')
                                    <div class="text-danger">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Individual Inputs -->
                        <div id="individualFields" class="row" style="display: none;">
                            <div class="form-group col-md-6">
                                <label class="form-label">Full Name *</label>
                                <div class="form-text text-muted">Please enter your name as per in your NRIC.</div>
                                <input type="text" class="form-control @error('full_name') is-invalid @enderror" placeholder="Full Name" id="full_name" value="{{ $com['name'] }}" name="full_name"  required>
                                @error('full_name')
                                    <div class="text-danger">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="form-group col-md-6">
                                <label class="form-label">IC Number *</label>
                                <div class="form-text text-muted">Please enter NRIC withtout slash (-).</div>
                                <input type="text" class="form-control @error('ic_no') is-invalid @enderror" placeholder="IC Number" id="ic_no" value="{{ $com['ic_no'] }}" name="ic_no" required>
                                @error('ic_no')
                                    <div class="text-danger">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Business Inputs -->
                        <div id="businessFields" class="row" style="display: none;">
                            <div class="form-group col-md-6">
                                <label class="form-label">SSM Registered Name *</label>
                                <input type="text" class="form-control @error('ssm_name') is-invalid @enderror" placeholder="SSM Registered Name" id="ssm_name" value="{{ $com['company_ssm_no'] }}" name="ssm_name" required>
                                @error('ssm_name')
                                    <div class="text-danger">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="form-group col-md-6">
                                <label class="form-label">SSM Registrtion Number (BRN) *</label>
                                <input type="text" class="form-control @error('ssm_no') is-invalid @enderror" placeholder="Registration Number" id="ssm_no" value="{{ $com['company_name'] }}" name="ssm_no" required>
                                @error('ssm_no')
                                    <div class="text-danger">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Shared Field -->
                        <div class="row">
                            <div class="form-group col-md-6">
                                <label class="form-label">Address *</label>
                                <div class="form-text text-muted invisible">.</div>
                                <input type="text" class="form-control @error('address') is-invalid @enderror" placeholder="Address" value="{{ $com['company_address'] }}" name="address" required>
                                @error('address')
                                    <div class="text-danger">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="form-group col-md-6">
                                <label class="form-label">Postcode *</label>
                                <div class="form-text text-muted invisible">.</div>
                                <input type="text" class="form-control @error('postcode') is-invalid @enderror" placeholder="postcode" value="{{ $com['postcode'] }}" name="postcode" required>
                                @error('postcode')
                                    <div class="text-danger">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="form-group col-md-6">
                                <label class="form-label">Email *</label>
                                <div class="form-text text-muted invisible">.</div>
                                <input type="email" class="form-control @error('email') is-invalid @enderror" placeholder="Email" value="{{ $com['company_email'] }}" name="email" required>
                                @error('email')
                                    <div class="text-danger">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="form-group col-md-6">
                                <label class="form-label">Contact No. *</label>
                                <div class="form-text text-muted invisible">.</div>
                                <input type="text" class="form-control @error('phoneno') is-invalid @enderror" placeholder="Contact No." value="{{ $com['company_phone'] }}" name="phoneno" required>
                                @error('phoneno')
                                    <div class="text-danger">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="form-group col-md-6">
                                <label class="form-label">SST Registration No. (SST No)</label>
                                <div class="form-text text-muted">Leave blank if this not relevant.</div>
                                <input type="text" class="form-control @error('sst_no') is-invalid @enderror" placeholder="SST Registration No." value="{{ $com['company_sst_no'] }}" name="sst_no">
                                @error('sst_no')
                                    <div class="text-danger">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="form-group col-md-6">
                                <label class="form-label">SST % (Sales Tax)</label>
                                <div class="form-text text-muted">Leave blank if this not relevant.</div>
                                <input type="text" class="form-control @error('sst_percent') is-invalid @enderror" placeholder="SST% (Sales Tax)" value="{{ $com['company_sst_percent'] }}" name="sst_percent">
                                @error('sst_percent')
                                    <div class="text-danger">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="form-group col-md-6">
                                <label class="form-label">Tourism Tax Registarion No.</label>
                                <div class="form-text text-muted">Leave blank if this not relevant.</div>
                                <input type="text" class="form-control @error('tourism_tax_no') is-invalid @enderror" placeholder="Tourism Tax Registarion No." value="{{ $com['tourism_tax_no'] }}" name="tourism_tax_no">
                                @error('tourism_tax_no')
                                    <div class="text-danger">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="form-group col-md-6">
                                <label class="form-label">Business Activity Code (MSIC Code) *</label>
                                <div class="form-text text-muted invisible">.</div>
                                <select class="form-control @error('msic_code') is-invalid @enderror" name="msic_code" required>
                                    <option value="">Select MSIC Code</option>
                                    @foreach($msicCodes as $code)
                                        <option value="{{ $code->code }}" {{ $com['msic_code'] == $code->code ? 'selected' : '' }}>
                                            {{ $code->code }} - {{ $code->description }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('msic_code')
                                    <div class="text-danger">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="form-group col-md-6">
                                <label class="form-label">Tax Identification No. (TIN) *</label>
                                <div class="form-text text-muted">Can be found at https://mytax.hasil.gov.my (e.g: C20830570210).</div>
                                <input type="text" class="form-control @error('tin_no') is-invalid @enderror" placeholder="Tax Identification No." value="{{ $com['tin_no'] }}" name="tin_no" required>
                                @error('tin_no')
                                    <div class="text-danger">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div id="taxApiSection" class="transition-section" style="display: none;">
                        <div class="row">
                            <div class="form-group col-md-6">
                                <label for="employeeType" class="form-label">Server LHDN</label>
                                <select id="businessType" class="form-select">
                                    <option value="staging">Staging (PREPROD)</option>
                                    {{-- <option value="business" {{ $com['business_type'] == 'business' ? 'selected' : '' }}>Business</option> --}}
                                </select>
                            </div>

                            <div class="form-group col-md-6">
                                <label class="form-label">Client ID</label>
                                <input type="text" class="form-control" placeholder="Client ID" value="{{ $com['client_id'] }}" name="client_id">
                            </div>

                            <div class="form-group col-md-6">
                                <label class="form-label">Client Secret ID 1</label>
                                <input type="text" class="form-control" placeholder="Client Secret ID 1" value="{{ $com['client_secret_id_1'] }}" name="client_secret_id_1">
                            </div>

                            <div class="form-group col-md-6">
                                <label class="form-label">Client Secret ID 2</label>
                                <input type="text" class="form-control" placeholder="Client Secret ID 2" value="{{ $com['client_secret_id_2'] }}" name="client_secret_id_2">
                            </div>
                        </div>
                    </div>

                    <div class="mt-4">
                        <button type="submit" class="btn btn-success w-100">Save</button>
                    </div>
                </form>
            </div>
        </div>

    </section>
</div>
@endsection

@push('scripts')
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="{{ asset('mazer/extensions/datatables.net-bs5/js/dataTables.bootstrap5.min.js') }}"></script>
<script>
    $(document).ready(function () {
        const individual = document.getElementById('individualFields');
        const business = document.getElementById('businessFields');
        const businessTypeSelect = document.getElementById('businessType');

        function toggleFields(type) {
            individual.style.display = 'none';
            business.style.display = 'none';

            if (type === 'individual') {
                individual.style.display = 'flex';

                document.getElementById('ssm_name').removeAttribute('required');
                document.getElementById('ssm_no').removeAttribute('required');
            } else if (type === 'business') {
                business.style.display = 'flex';

                document.getElementById('ic_no').removeAttribute('required');
                document.getElementById('full_name').removeAttribute('required');
            }
        }

        // Initial load
        toggleFields(businessTypeSelect.value);

        // Event listener for business type selection
        businessTypeSelect.addEventListener('change', function () {
            toggleFields(this.value);
        });

        const $general = $('#generalInfoSection');
        const $tax = $('#taxApiSection');

        // Ensure default view
        $general.show();
        $tax.hide();

        // Ensure default active button
        $('#btnGeneral').addClass('active');
        $('#btnTax').removeClass('active');

        $('.toggle-btn').on('click', function () {
            $('.toggle-btn').removeClass('active');
            $(this).addClass('active');
        });

        $('#btnGeneral').on('click', function () {
            $tax.fadeOut(200, function () {
                $general.fadeIn(200);
            });
        });

        $('#btnTax').on('click', function () {
            $general.fadeOut(200, function () {
                $tax.fadeIn(200);
            });
        });
    });
</script>

@endpush
