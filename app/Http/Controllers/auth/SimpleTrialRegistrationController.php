<?php

namespace App\Http\Controllers\auth;

use App\Models\User;
use App\Models\Company;
use App\Models\Receipt;
use App\Models\UserDetail;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Mail\WelcomeTrialUserSimple;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use App\Models\Subscription\Subscription;
use App\Models\Subscription\SubscriptionPlan;

class SimpleTrialRegistrationController extends Controller
{
    /**
     * Show the registration form.
     *
     * @return \Illuminate\View\View
     */
    public function showRegisterForm()
    {
        return view("simple_event_trial_register");
    }

    /**
     * Handle the registration request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function register(Request $request)
    {
        $request->validate([
            "full_name" => ["required", "string", "max:255"],
            "email" => [
                "required",
                "string",
                "email",
                "max:255",
                "unique:users",
            ],
            "mobile" => ["required", "string", "min:10", "max:11"],
            "g-recaptcha-response" => ["required", new \App\Rules\Recaptcha],
            "honeypot" => "nullable|string|max:0" // Honeypot field - must be empty
        ]);

        // Split full name into first and last name
        $nameParts = explode(" ", $request->full_name, 2);
        $firstName = $nameParts[0];
        $lastName = isset($nameParts[1]) ? $nameParts[1] : "";

        $temp_password = Str::random(12);

        $user = User::create([
            "username" => $request->email,
            "email" => $request->email,
            "password" => $temp_password,
            "email_verified_at" => now(),
            "onboarding_complete" => true, // Skip onboarding
            "isBizappUser" => "N",
            "access_module" => "connected",
        ]);

        // Create user details
        $userDetail = UserDetail::create([
            "user_id" => $user->id,
            "first_name" => $firstName,
            "last_name" => $lastName,
            "mobile" => $request->mobile,
            "address" => "",
            "city" => "",
            "state" => "1",
            "postcode" => "",
            "country" => "MALAYSIA",
        ]);

        // Auto-create company
        $company = Company::create([
            "user_id" => $user->id,
            "com_name" => $request->full_name,
            "com_address" => "Not provided",
            "com_city" => "Not provided",
            "com_state" => "1",
            "com_postcode" => "00000",
            "com_country" => "MALAYSIA",
            "com_registration_no" => null,
            "com_mobile" => $request->mobile,
            "com_email" => $request->email,
            "com_sst_value" => "0",
            "account_type" => "individual",
            "ic_number" => "************",
        ]);

        // Create receipt template
        Receipt::create([
            "company_id" => $company->id,
            "user_id" => $user->id,
            "title" => $company->com_name,
            "name" => $request->full_name,
            "email" => $request->email,
            "phone" => $request->mobile,
            "address" => "Not provided",
            "city" => "Not provided",
            "state" => "1",
            "postcode" => "00000",
            "country" => "MALAYSIA",
            "sst" => "0,0",
        ]);

        // Find the trial plan
        $trialPlan = SubscriptionPlan::where("name", "Starter - Trial")
            ->where("is_active", true)
            ->first();

        if ($trialPlan) {
            // Create trial subscription
            Subscription::create([
                "company_id" => $company->id,
                "subscription_plan_id" => $trialPlan->id,
                "starts_at" => now(),
                "ends_at" => now()->addDays(7),
                "trial_ends_at" => now()->addDays(7),
                "status" => "active",
                "bill_type" => "trial",
            ]);
        }

        \Log::info("Trial registration completed", [
            "user_id" => $user->id,
            "email" => $user->email,
            "company_id" => $company->id,
        ]);

        try {
            // Send welcome email with trial details
            Mail::to($user->email)->send(
                new WelcomeTrialUserSimple($user, $temp_password)
            );
        } catch (\Exception $e) {
            \Log::error("Failed to send welcome email: " . $e->getMessage());
            // Continue with registration even if email fails
        }

        // Log the user in and redirect to dashboard
        Auth::login($user);

        return redirect()
            ->route("dashboard")
            ->with(
                "success",
                "Registration successful! Your 7-day trial account is ready. Please check your email for login credentials."
            );
    }
}
