<?php

namespace App\Http\Controllers\MBI\API;

use Exception;
use Carbon\Carbon;
use App\Models\PBT;
use App\Models\User;
use App\Models\Order;
use App\Models\Company;
use App\Models\Product;
use Illuminate\Http\Request;
use App\Models\CompanyMaterial;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Cache;

class DashboardController_optimized extends Controller
{
    public function index(Request $request){
        // return view('mbi.dashboard.index');
    }

    function calculateGrowth($current, $last){
        if($last == 0){
            if($current > 0){
                return "+100%";
            } else {
                return "0%";
            }
        }

        $growth = (($current - $last) / $last) * 100;

        // Format the growth percentage to include a + or - sign
        return ($growth >= 0 ? "+" : "") . round($growth, 2) . "%";
    }

    /**
     * Optimized helper method for date filtering
     */
    private function getDateFilters($tab)
    {
        $currentYear = Carbon::now()->year;
        
        switch ($tab) {
            case 'all':
                return [
                    'start' => '2024-01-01 00:00:00',
                    'end' => Carbon::now()->endOfDay()->format('Y-m-d H:i:s'),
                    'apply_year' => false
                ];
            case 'today':
                return [
                    'start' => Carbon::now()->startOfDay()->format('Y-m-d H:i:s'),
                    'end' => Carbon::now()->endOfDay()->format('Y-m-d H:i:s'),
                    'apply_year' => false
                ];
            case $currentYear:
                return [
                    'start' => Carbon::createFromDate($currentYear, 1, 1)->startOfYear()->format('Y-m-d H:i:s'),
                    'end' => Carbon::createFromDate($currentYear, 12, 31)->endOfYear()->format('Y-m-d H:i:s'),
                    'apply_year' => true,
                    'year' => $currentYear
                ];
            case $currentYear - 1:
                return [
                    'start' => Carbon::createFromDate($currentYear - 1, 1, 1)->startOfYear()->format('Y-m-d H:i:s'),
                    'end' => Carbon::createFromDate($currentYear - 1, 12, 31)->endOfYear()->format('Y-m-d H:i:s'),
                    'apply_year' => true,
                    'year' => $currentYear - 1
                ];
            default:
                if (is_numeric($tab) && strlen($tab) == 4) {
                    return [
                        'start' => Carbon::createFromDate($tab, 1, 1)->startOfYear()->format('Y-m-d H:i:s'),
                        'end' => Carbon::createFromDate($tab, 12, 31)->endOfYear()->format('Y-m-d H:i:s'),
                        'apply_year' => true,
                        'year' => $tab
                    ];
                }
                // Default to today
                return [
                    'start' => Carbon::now()->startOfDay()->format('Y-m-d H:i:s'),
                    'end' => Carbon::now()->endOfDay()->format('Y-m-d H:i:s'),
                    'apply_year' => false
                ];
        }
    }

    public function dataSales(Request $request){
        
        try{
            $tab = $request->input('tab');
            $dateFilters = $this->getDateFilters($tab);
            
            // Cache key for this expensive query
            $cacheKey = "sales_data_{$tab}_" . Carbon::now()->format('Y-m-d-H');
            
            $report = Cache::remember($cacheKey, 300, function() use ($dateFilters) {
                $currentYear = Carbon::now()->year;
                $lastYear = $currentYear - 1;
                
                return Order::select([
                    DB::raw('SUM(CASE WHEN YEAR(orders.order_date) = ' . $currentYear . ' THEN orders.grandtotal_decimal ELSE 0 END) as sales_year'),
                    DB::raw('SUM(CASE WHEN YEAR(orders.order_date) = ' . $lastYear . ' THEN orders.grandtotal_decimal ELSE 0 END) as sales_last_year'),
                    DB::raw('COUNT(CASE WHEN YEAR(orders.order_date) = ' . $currentYear . ' THEN 1 ELSE NULL END) as total_transaction_year'),
                    DB::raw('COUNT(CASE WHEN YEAR(orders.order_date) = ' . $lastYear . ' THEN 1 ELSE NULL END) as total_transaction_last_year'),
                    DB::raw('SUM(CASE WHEN DATE(orders.order_date) = CURDATE() THEN orders.grandtotal_decimal ELSE 0 END) as sales_today'),
                    DB::raw('SUM(CASE WHEN DATE(orders.order_date) = CURDATE() - INTERVAL 1 DAY THEN orders.grandtotal_decimal ELSE 0 END) as sales_yesterday'),
                    DB::raw('COUNT(CASE WHEN DATE(orders.order_date) = CURDATE() THEN 1 ELSE NULL END) as total_transaction_today'),
                    DB::raw('COUNT(CASE WHEN DATE(orders.order_date) = CURDATE() - INTERVAL 1 DAY THEN 1 ELSE NULL END) as total_transaction_yesterday'),
                ])
                ->whereNull('orders.deleted_at')
                ->join('users', 'orders.user_id', '=', 'users.id')
                ->where('users.access_module', 'like', '%plats%')
                ->where('users.username', 'not like', 'uplats012')
                ->first();
            });
                
            return response()->json([
                'status' => '1',
                'sales_year' => [
                    'title' => 'Jumlah Jualan ' . ($tab !== 'all' ? ($tab === 'today' ? 'Semasa' : $tab) : 'Keseluruhan'),
                    'value' => "RM " . number_format($report->sales_year, 2),
                ],
                'sales_last_year' => [
                    'title' => 'Jualan ' . ($tab !== 'all' ? ($tab === 'today' ? 'Semasa' : $tab - 1) : Carbon::now()->subYear()->format('Y')),
                    'value' => "RM " . number_format($report->sales_last_year, 2),
                ],
                'total_transaction_year' => [
                    'title' => 'Rekod Transaksi ' . ($tab !== 'all' ? ($tab === 'today' ? 'Semasa' : $tab) : 'Keseluruhan'),
                    'value' => number_format($report->total_transaction_year, 0, '', ','),
                ],
                'total_transaction_last_year' => [
                    'title' => 'Rekod Transaksi ' . ($tab !== 'all' ? ($tab === 'today' ? 'Semasa' : $tab - 1) : Carbon::now()->subYear()->format('Y')),
                    'value' => number_format($report->total_transaction_last_year, 0, '', ','),
                ],
                'sales_today' => "RM " . number_format($report->sales_today, 2),
                'sales_yesterday' => "RM " . number_format($report->sales_yesterday, 2),
                'total_transaction_today' => number_format($report->total_transaction_today, 0, '', '.'),
                'total_transaction_yesterday' => number_format($report->total_transaction_yesterday, 0, '', '.'),
                'growth' => [
                    'transaction_yearly' => [
                        'background' => ($report->total_transaction_year > $report->total_transaction_last_year ? 'bg-success' : 'bg-error'),
                        'value' => $this->calculateGrowth($report->total_transaction_year, $report->total_transaction_last_year),
                    ],
                    'transaction_daily' => [
                        'background' => ($report->total_transaction_today > $report->total_transaction_yesterday ? 'bg-success' : 'bg-error'),
                        'value' => $this->calculateGrowth($report->total_transaction_today, $report->total_transaction_yesterday),
                    ],
                ],
            ]);
        } catch (Exception $e) {
            return response()->json([
                'status' => '0',
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * HEAVILY OPTIMIZED statisticActive function
     */
    public function statisticActive(Request $request){
        $tab = $request->input('tab', 'today');
        $dateFilters = $this->getDateFilters($tab);
        
        // Cache key for this expensive operation
        $cacheKey = "statistic_active_{$tab}_" . Carbon::now()->format('Y-m-d-H');
        
        return Cache::remember($cacheKey, 300, function() use ($dateFilters, $request) {
            // Single query to get all required user and company data
            $userCompanyData = DB::table('users')
                ->join('companies', 'companies.user_id', '=', 'users.id')
                ->select([
                    'users.id as user_id',
                    'companies.id as company_id',
                    'companies.com_state',
                    'companies.category_id',
                    'companies.created_at',
                    'companies.pbt_id'
                ])
                ->where('users.access_module', 'like', '%plats%')
                ->where('users.username', 'not like', 'uplats012')
                ->get();

            $companyIds = $userCompanyData->pluck('company_id');
            $totalMerchant = $userCompanyData->count();

            // Get PBT data with single query
            $pbtData = PBT::select(['id', 'name', 'code', 'state', 'status'])
                ->where('state', 'Selangor')
                ->get();

            // Single optimized query for all order-related statistics
            $orderStats = DB::table('orders')
                ->leftJoin('companies', 'companies.id', '=', 'orders.company_id')
                ->leftJoin('pbts', 'pbts.id', '=', 'companies.pbt_id')
                ->select([
                    DB::raw('COUNT(DISTINCT companies.user_id) as active_merchants'),
                    DB::raw('COUNT(DISTINCT CASE WHEN pbts.state = "Selangor" AND pbts.status = "active" THEN pbts.code END) as active_pbts'),
                    DB::raw('COUNT(DISTINCT CASE WHEN orders.customer_name NOT LIKE "POS-%" THEN orders.customer_name END) as total_customers'),
                    DB::raw('COUNT(*) as total_orders')
                ])
                ->whereIn('companies.id', $companyIds)
                ->whereBetween('orders.order_date', [$dateFilters['start'], $dateFilters['end']])
                ->first();

            // Get latest orders efficiently
            $latestOrders = DB::table('orders')
                ->leftJoin('companies', 'companies.id', '=', 'orders.company_id')
                ->select([
                    'orders.id',
                    'companies.com_name as company_name',
                    'orders.customer_name',
                    'orders.order_date'
                ])
                ->whereIn('companies.id', $companyIds)
                ->whereBetween('orders.order_date', [$dateFilters['start'], $dateFilters['end']])
                ->orderBy('orders.order_date', 'desc')
                ->limit(5)
                ->get();

            // Get new companies this month
            $newCompaniesCount = $userCompanyData
                ->where('created_at', '>=', Carbon::now()->startOfMonth()->format('Y-m-d H:i:s'))
                ->count();

            $newCompaniesByCategory = $userCompanyData
                ->where('created_at', '>=', Carbon::now()->startOfMonth()->format('Y-m-d H:i:s'))
                ->groupBy('category_id')
                ->map->count();

            // Get heatmap data with caching
            $heatMaps = $this->getHeatMapData($dateFilters['start'], $dateFilters['end']);

            return [
                'status' => '1',
                'pbt' => [
                    'total' => $pbtData->count(),
                    'active' => $orderStats->active_pbts ?? 0,
                ],
                'merchant' => [
                    'total' => $totalMerchant,
                    'active' => $orderStats->active_merchants ?? 0,
                ],
                'new' => [
                    'total' => $newCompaniesCount,
                    'category' => $newCompaniesByCategory,
                ],
                'total_customer' => $orderStats->total_customers ?? 0,
                'heat_maps' => $heatMaps,
                'latest_orders' => $latestOrders,
                'year' => $request->tab ?? Carbon::now()->year,
            ];
        });
    }

    /**
     * Optimized heatmap data retrieval
     */
    private function getHeatMapData($startDate, $endDate)
    {
        $cacheKey = "heatmap_{$startDate}_{$endDate}";
        
        return Cache::remember($cacheKey, 600, function() use ($startDate, $endDate) {
            $heatMapData = Order::select([
                DB::raw('ROUND(order_latitude, 3) as lat'),
                DB::raw('ROUND(order_longitude, 3) as lng'),
                DB::raw('COUNT(*) as count')
            ])
            ->whereNotNull('order_latitude')
            ->whereNotNull('order_longitude')
            ->whereBetween('order_date', [$startDate, $endDate])
            ->groupBy(DB::raw('ROUND(order_latitude, 3), ROUND(order_longitude, 3)'))
            ->orderByDesc('count')
            ->limit(1000)
            ->get();

            return $heatMapData->map(function($point) {
                return [
                    'lat' => (float)$point->lat,
                    'lng' => (float)$point->lng,
                    'count' => $point->count
                ];
            })->toArray();
        });
    }

    public function graph(Request $request){
        $tab = $request->input('tab', 'all');
        $dateFilters = $this->getDateFilters($tab);
        
        // Cache the expensive graph data
        $cacheKey = "graph_data_{$tab}_" . Carbon::now()->format('Y-m-d-H');
        
        return Cache::remember($cacheKey, 300, function() use ($dateFilters) {
            // Get user IDs once
            $userIds = User::where('access_module', 'like', '%plats%')
                ->where('users.username', 'not like', 'uplats012')
                ->pluck('id');

            // Optimized payment method query
            $allPaymentMethod = Order::selectRaw(
                'pay_method, SUM(payment_received_decimal) as value, COUNT(*) as quantity'
            )
            ->whereNull('deleted_at')
            ->whereIn('user_id', $userIds)
            ->whereBetween('order_date', [$dateFilters['start'], $dateFilters['end']])
            ->groupBy('pay_method')
            ->havingRaw('value > 0 OR quantity > 1')
            ->orderBy('value', 'DESC')
            ->get();

            // Optimized product stock query
            $allProducts = Product::select([
                'product_name',
                DB::raw('SUM(product_stock) as quantity'),
            ])
            ->where('products.product_stock', '>', 0)
            ->groupBy('product_name')
            ->orderBy('quantity', 'desc')
            ->limit(10)
            ->get();

            // Process payment methods
            $payment_method = $this->processPaymentMethods($allPaymentMethod);
            $products = $this->processProducts($allProducts);

            return [
                'status' => '1',
                'products' => $products,
                'paymentMethod' => $payment_method
            ];
        });
    }

    /**
     * Helper method to process payment methods
     */
    private function processPaymentMethods($paymentMethods)
    {
        $backgroundColors = [
            "#ef4444", "#f97316", "#fcd34d", "#84cc16", "#4ade80",
            "#22d3ee", "#3b82f6", "#818cf8", "#e879f9", "#fb7185"
        ];

        return $paymentMethods->map(function($item, $key) use ($backgroundColors) {
            $name = ($item->pay_method === 'QR_PAY') ? 'QR_GKASH' : $item->pay_method;
            $background = $backgroundColors[$key % count($backgroundColors)];

            return [
                'name' => $name,
                'quantity' => $item->quantity,
                'amount' => $item->value,
                'background' => $background,
            ];
        })->toArray();
    }

    /**
     * Helper method to process products
     */
    private function processProducts($products)
    {
        $backgroundColors = [
            "#ef4444", "#f97316", "#fcd34d", "#84cc16", "#4ade80",
            "#22d3ee", "#3b82f6", "#818cf8", "#e879f9", "#fb7185"
        ];

        return $products->map(function($item, $key) use ($backgroundColors) {
            $background = $backgroundColors[$key % count($backgroundColors)];

            return [
                'name' => $item->name ?? 'Tiada Kategori',
                'quantity' => $item->quantity,
                'background' => $background,
            ];
        })->toArray();
    }

    public function companySales(Request $request){
        // Get the date range from the request (following original logic)
        $fromDate = $request->input('start_date');
        $toDate = $request->input('end_date');
        $search = $request->input('search');
        $minSale = $request->input('minSales');
        $maxSale = $request->input('maxSales');
        $userType = $request->input('userType');
        $com_pbt = $request->input('comPbt');

        if($request->has('tab') && $request->tab != 'all'){
            $fromDate = Carbon::createFromDate($request->tab, 1, 1)->startOfYear()->format('Y-m-d');
            $toDate = Carbon::createFromDate($request->tab, 12, 31)->endOfYear()->format('Y-m-d');
        } else if ($request->has('tab') && $request->tab == 'all'){
            $fromDate = Carbon::createFromDate(2024, 1, 1)->startOfYear()->format('Y-m-d');
            $toDate = Carbon::now()->endOfDay()->format('Y-m-d');
        }

        // Cache PBT data
        $pbts = Cache::remember('pbts_selangor', 3600, function() {
            return PBT::select(['id', 'name', 'code', 'state'])
                ->where('state', 'Selangor')
                ->get();
        });

        // Get the userIds excluding 'uplats012' (following original approach)
        $userIds = User::where('access_module', 'like', '%plats%')
            ->where('users.username', 'not like', 'uplats012')
            ->has('companies')
            ->pluck('id');

        $companyIds = Company::whereIn('user_id', $userIds)->pluck('id');

        // Debug: Log the filters being applied
        Log::info('Company Sales Filters', [
            'tab' => $request->input('tab'),
            'fromDate' => $fromDate,
            'toDate' => $toDate,
            'search' => $search,
            'minSale' => $minSale,
            'maxSale' => $maxSale,
            'userType' => $userType,
            'com_pbt' => $com_pbt,
            'userIds_count' => $userIds->count(),
            'companyIds_count' => $companyIds->count()
        ]);

        try {
            // --- Start of Subquery ---
            $subQuery = Order::select([
                'users.id as user_id',
                'companies.com_name as com_name',
                'companies.pbt_id as pbt_id',
                'companies.com_address as com_address',
                'pbts.name as pbt_name',
                'pbts.code as pbt_code',
                'categories.name as category',
                'user_details.first_name as first_name',
                'users.latitude as lat',
                'users.longitude as long',
                DB::raw('SUM(orders.grandtotal_decimal) as total_sales')
            ])
            ->whereIn('orders.company_id', $companyIds)
            ->join('companies', 'companies.id', '=', 'orders.company_id')
            ->join('users', 'users.id', '=', 'companies.user_id')
            ->leftJoin('categories', 'categories.id', '=', 'companies.category_id')
            ->leftJoin('pbts', 'pbts.id', '=', 'companies.pbt_id')
            ->leftJoin('user_details', 'user_details.user_id', '=', 'users.id')
            ->whereNull('orders.deleted_at');

            // Apply date range filter if provided
            if ($fromDate && $toDate) {
                $subQuery->whereBetween('orders.created_at', [$fromDate, $toDate]);
            }

            // Apply user type filter
            if ($userType) {
                $subQuery->where('users.access_module', $userType);
            }

            // Apply PBT filter
            if($com_pbt) {
                $subQuery->where('companies.pbt_id', $com_pbt);
            }

            $subQuery->whereIn('users.id', $userIds)
                ->groupBy(
                    'users.id',
                    'companies.com_name',
                    'companies.pbt_id',
                    'companies.com_address',
                    'categories.name',
                    'user_details.first_name',
                    'users.latitude',
                    'users.longitude',
                    'pbts.name',
                    'pbts.code'
                );
            // --- End of Subquery ---

            // --- Main Query using the Subquery ---
            $mainQuery = DB::query()->fromSub($subQuery, 'sales');

            // Apply search filter to the main query
            if ($search) {
                $mainQuery->where(function ($query) use ($search) {
                    $query->where('com_name', 'like', "%$search%")
                          ->orWhere('category', 'like', "%$search%")
                          ->orWhere('first_name', 'like', "%$search%");
                });
            }
            
            // Apply sales range filter to the main query
            if ($minSale !== null && $minSale !== '') {
                $mainQuery->where('total_sales', '>=', (float)$minSale);
            }
            if ($maxSale !== null && $maxSale !== '') {                
                $mainQuery->where('total_sales', '<=', (float)$maxSale);
            }

            $mainQuery->orderBy($request->input('sortBy', 'total_sales'), $request->input('sortDir', 'desc'));
            // Debug: Log the SQL query being executed
            Log::info('Company Sales Query', [
                'sql' => $mainQuery->toSql(),
                'bindings' => $mainQuery->getBindings()
            ]);

            $list = $mainQuery->paginate(10);

            // Debug: Log the results
            Log::info('Company Sales Results', [
                'total_items' => $list->total(),
                'current_page' => $list->currentPage(),
                'per_page' => $list->perPage(),
                'items_count' => $list->count()
            ]);

            return response()->json([
                'status' => '1',
                'pbt_selangor' => $pbts,
                'list' => $list,
                'no' => $request->get('page', 1) * 10 - 10,
            ]);

        } catch (\Exception $e) {
            Log::error('Company Sales Query Error: ' . $e->getMessage());
            Log::error('Query Trace: ' . $e->getTraceAsString());
            
            return response()->json([
                'error' => 'Query execution failed',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
