<?php

namespace App\Http\Controllers\backend;

use App\Models\Product;
use App\Models\LogStock;
use App\Models\Collection;
use App\Models\ProductVariantAttribute;
use Illuminate\Http\Request;
use App\Models\ProductDetail;
use Illuminate\Validation\Rule;
use App\Models\ProductsCategory;
use App\Models\CollectionProduct;
use App\Jobs\SyncProductBizappJob;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\Material;
use Exception;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Validator;
use App\Models\VariantAttribute;
use App\Models\VariantAttributeValue;
use App\Models\CompositeAttributeValue;
use Illuminate\Http\UploadedFile;
use App\Jobs\BizappAddProductJob;
use App\Jobs\BizappAddProductBatchJob;
use App\Jobs\BizappUpdateProductBatchJob;
use Illuminate\Support\Facades\Storage;
use App\Models\ProductLhdnCode;
use App\Models\Subscription\Subscription;
use App\Models\Subscription\SubscriptionPlan;
use App\Models\Feature;

class ProductController extends Controller
{
    public function index()
    {
        return view('backend.product.index');
    }

    public function data(Request $request){
        $company_id = null;
        if($request->user()->companies){
            $company_id = $request->user()->companies->id;
        } else {
            $company_id = $request->user()->employee->company_id;
        }
        $query = Product::query();
        // Include uom_id for relationship + Sertakan uom_id untuk relationship
        $query->select('id','product_name','product_SKU','product_stock','product_price','product_stock_status','uom_id');
        // Eager load UOM relationship + Eager load relationship UOM
        $query->with('uom:id,uom_name,uom_code,uom_type');
        $query->where('parent_company', $company_id);
        $query->when($request->search, callback: function($q) use($request){
            $q->where(function($q) use($request){
                $q->where('product_name', 'like', '%' . $request->search . '%');
                $q->orWhere('product_SKU', 'like', '%' . $request->search . '%');
            });
        });
        $query->orderBy('product_name', 'ASC');
        $list = $query->paginate($request->show ?? 10);

        // Log::info($list);

        return view('backend.product.list', compact('list'));
    }

    /**
     * Get products data for AJAX requests
     */
    public function getProductsData(Request $request){
        $company_id = null;
        if($request->user()->companies){
            $company_id = $request->user()->companies->id;
        } else {
            $company_id = $request->user()->employee->company_id;
        }

        $query = Product::query();
        // Load UOM relationship + Load relationship UOM
        $query->with('productDetail','uom');
        // Include uom_id for relationship + Sertakan uom_id untuk relationship
        $query->select('id', 'product_name', 'product_SKU', 'product_stock', 'product_price', 'product_stock_status', 'uom_id');
        $query->where('parent_company', $company_id);
        $query->whereNotNull('product_SKU');
        $query->where('product_status', '1');

        // Handle search
        if($request->search) {
            $query->where(function($q) use($request){
                $q->where('product_name', 'like', '%' . $request->search . '%');
                $q->orWhere('product_SKU', 'like', '%' . $request->search . '%');
            });
        }

        // Filter by collection if specified
        if($request->collection_id) {
            $query->whereHas('collectionProducts', function($q) use($request) {
                $q->where('collection_id', $request->collection_id);
            });
        }

        $query->orderBy('product_name', 'ASC');
        $products = $query->paginate($request->per_page ?? 10);

        // Format the response + Format respons // Format respons
        $formattedProducts = collect($products->items())->map(function($product) {
            return [
                'id' => $product->id,
                'product_name' => $product->product_name,
                'product_SKU' => $product->product_SKU,
                'product_stock' => $product->product_stock,
                'product_price' => $product->product_price,
                'product_attachment' => $product->productDetail ? $product->productDetail->product_attachment : null,
                // UOM details + Detail UOM
                'uom_id' => $product->uom_id,
                'uom_name' => $product->uom?->uom_name,
                'uom_code' => $product->uom?->uom_code,
                'uom_type' => $product->uom?->uom_type,
            ];
        });

        return response()->json([
            'data' => $formattedProducts,
            'current_page' => $products->currentPage(),
            'last_page' => $products->lastPage(),
            'per_page' => $products->perPage(),
            'total' => $products->total()
        ]);
    }

    public function syncProduct(Request $request){
        $user = $request->user();
        if($user->isBizappUser !== "Y"){
            return response()->json([
                'result' => false,
                'message' => "You're not bizapp user"
            ]);
        }

        SyncProductBizappJob::dispatch($user)->onQueue('syncProduct');

        return response()->json([
            'result' => true,
            'message' => "Sync in progress , this may take a while...",
            'redirect' => route('products')
        ]);
    }

    public function productList(Request $request)
    {
        // Log::info('searching started');
        $user = getUser();
        if(!$user){
            return $this->sendResponseV3(false, "0" ,"Please log in to continue");
        }

        $query = Product::query();
        // Load relationships including UOM + Load relationship termasuk UOM
        $query->with('productDetail','category','uom');
        $query->where('parent_company', $user['company']['id']);
        $query->where('product_status', '1');

        // search
        $query->when($request->search, function($q) use ($request){
            $q->where(function($q) use ($request){
                $search = "%{$request->search}%";
                $q->where('product_name', 'like', $search);
                $q->orWhere('product_SKU', 'like', $search);
            });
        });
        // price range
        $query->when($request->min_price || $request->max_price, function($q) use ($request){
            $q->where(function($q) use ($request){
                $minPrice = $request->min_price;
                $maxPrice = $request->max_price;
                $q->whereBetween('product_price', [
                    $minPrice ?? 0,
                    $maxPrice ?? 100000
                ]);
            });
        });

        $query->when($request->in_stock, function($q) use ($request){
            $q->where(function($q) use ($request){
                $q->where('product_stock', '!=' , 0);
            });
        });

        $query->when($request->sort_by, function ($q) use ($request) {
            // User-defined sort
            switch ($request->sort_by) {
                case 'price_asc':
                    $q->orderBy('product_price', 'asc');
                    break;
                case 'price_desc':
                    $q->orderBy('product_price', 'desc');
                    break;
                case 'newest':
                    $q->orderBy('created_at', 'desc');
                    break;
                case 'oldest':
                    $q->orderBy('created_at', 'asc');
                    break;
                default:
                    // If sort_by is invalid, use default sorting
                    $q->orderByRaw('CASE WHEN product_fav = true THEN 0 ELSE 1 END, created_at DESC');
                    break;
            }
        }, function ($q) {
            // Default sort if no sort_by is provided
            $q->orderByRaw('CASE WHEN product_fav = true THEN 0 ELSE 1 END, product_name ASC');
        });

        // $query->orderByRaw('CASE WHEN product_fav = true THEN 0 ELSE 1 END, created_at DESC');
        $list = $query->paginate($request->show ?? 3000);

        $array = [];
        foreach($list as $item){
            $array[] = [
                'id' => $item->id,
                'bizapp_id' => $item->product_id_bizapp,
                'name' => $item->product_name,
                'sku' => $item->product_SKU,
                'description' => $item->product_description,
                'note' => $item->product_note,
                'brand' => $item->product_brand,
                'category' => $item->category?->category_name,
                'price' => $item->product_price,
                'stock' => $item->product_stock,
                'cost_price' => $item->cost_price,
                'product_weight' => $item->product_weight,
                'product_fav' => ($item->product_fav == "1") ? "1" : "0",
                'created_at' => $item->created_at->format('d/m/Y'),
                'updated_at' => $item->updated_at->format('d/m/Y H:i:s'),
                'product_attachment' => $item->productDetail?->product_attachment,
                // UOM details + Detail UOM
                'uom_id' => $item->uom_id,
                'uom_name' => $item->uom?->uom_name,
                'uom_code' => $item->uom?->uom_code,
                'uom_type' => $item->uom?->uom_type,
            ];
        }
        if(is_array($array) && empty($array)    ){
            return $this->sendResponseV3($request->search . " not found");
        }
        $data = [
            'list' => $array,
            'pagination' => [
                'total' => $list->total(),
                'per_page' => $list->perPage(),
                'current_page' => $list->currentPage(),
                'last_page' => $list->lastPage(),
                'from' => $list->firstItem(),
                'to' => $list->lastItem()
            ]
        ];

        return $data;
    }
    public function checkSKU(Request $request){
        $validator = Validator::make($request->all(), [
            'sku' => 'required|string'
        ]);

        if($validator->fails()){
            return response()->json([
                'result' => false,
                'message' => $validator->errors()->first()
            ]);
        }

        // if($request->user()->isBizappUser === "Y"){
        //     $urlPos = config('bizappos.bizappos_api_url');
        //     $checkSKU = Http::asForm()->post($urlPos . 'api_name=TRACK_CHECK_PRODUCTSKU_DTLS', [
        //         'DOMAIN' => auth()->user()->domain,
        //         'pid' => auth()->user()->pid,
        //         'productsku' => $request->sku
        //     ])->throw()->json();
        //     // dd($checkSKU);
        //     if(!is_array($checkSKU)){
        //         return response()->json([
        //             'result' => false,
        //             'message' => "Failed to check SKU"
        //         ]);
        //     } else if(count($checkSKU) > 0){
        //         $product = $checkSKU[0]['productname'] ?? "";
        //         return response()->json([
        //             'result' => false,
        //             'message' => "SKU Already used for product [ {$product} ] "
        //         ]);
        //     } else {
        //         return response()->json([
        //             'result' => true,
        //             'message' => "Success, please wait....",
        //             'redirect' => route('product.create', ['sku' => $request->sku])
        //         ]);
        //     }
        // } else { // for non bizapp users

            $company_id = '';
            $company = auth()->user()->companies;

            if(!$company){
                $company_id = auth()->user()->employee->id;
            } else {
                $company_id = $company->id;
            }

            $product = Product::where('product_SKU',$request->sku)->where('parent_company',$company_id)->first();

            if($product){
                return response()->json([
                    'result' => false,
                    'message' => "SKU already used for product [ {$product->product_name} ] "
                ]);
            } else {
                return response()->json([
                    'result' => true,
                    'message' => "Success, please wait....",
                    'redirect' => route('product.create', ['sku' => $request->sku])
                ]);
            }
        // }


    }

    /**
     * Check multiple SKUs for duplicates before form submission // Semak berbilang SKU untuk pendua sebelum hantar borang
     */
    public function checkMultipleSKU(Request $request){
        $validator = Validator::make($request->all(), [
            'skus' => 'required|array',
            'skus.*.sku' => 'required|string',
            'skus.*.name' => 'required|string',
            'skus.*.type' => 'required|string|in:main,variant'
        ]);

        if($validator->fails()){
            return response()->json([
                'result' => false,
                'message' => $validator->errors()->first()
            ]);
        }

        $company_id = '';
        $company = auth()->user()->companies;

        if(!$company){
            $company_id = auth()->user()->employee->parent_company;
        } else {
            $company_id = $company->id;
        }

        // Check for duplicates in local database first // Semak pendua dalam pangkalan data tempatan dahulu
        $listSku = [];
        foreach($request->skus as $skuData) {
            // Check in main products table // Semak dalam jadual produk utama
            $product = Product::where('product_SKU', $skuData['sku'])
                ->where('parent_company', $company_id)
                ->first();

            if($product){
                return response()->json([
                    'result' => false,
                    'message' => "SKU '{$skuData['sku']}' already used for product [ {$product->product_name} ]"
                ]);
            }

            // Check in variant products table // Semak dalam jadual produk variant
            $variantProduct = ProductVariantAttribute::where('product_sku', $skuData['sku'])
                ->whereHas('product', function($q) use($company_id) {
                    $q->where('parent_company', $company_id);
                })
                ->first();

            if($variantProduct){
                // for variant product [ {$variantProduct->product_name} ]
                return response()->json([
                    'result' => false,
                    'message' => "SKU '{$skuData['sku']}' already used"
                ]);
            }

            $returnCheckSku = $this->productCheckSkuForVariant($skuData['sku']);
            if ($returnCheckSku['result']) {
                return response()->json([
                    'result' => false,
                    'message' => $returnCheckSku['message']
                ]);
            }

            $listSku[] = [
                'productsku' => $skuData['sku'],
                'productname' => $skuData['name']
            ];


        }

        if(auth()->user()->isBizappUser === "Y"){
            $checkSku = $this->bizappCheckSku('', $listSku, auth()->user()->pid);

            // Log::info('Check SKU: ' . json_encode($checkSku));

            if(!$checkSku['result']){
                return response()->json([
                    'result' => false,
                    'message' => $checkSku['message']
                ]);
            }
        }

        return response()->json([
            'result' => true,
            'message' => 'All SKUs are available'
        ]);
    }

    /**
     * Check multiple SKUs for duplicates before editing form submission // Semak berbilang SKU untuk pendua sebelum hantar borang edit
     */
    public function checkMultipleSKUEdit(Request $request){
        $validator = Validator::make($request->all(), [
            'skus' => 'required|array',
            'skus.*.sku' => 'required|string',
            'skus.*.name' => 'required|string',
            'skus.*.type' => 'required|string|in:main,variant',
            'skus.*.exclude_product_id' => 'nullable|string',
            'skus.*.exclude_variant_id' => 'nullable|string'
        ]);

        if($validator->fails()){
            return response()->json([
                'result' => false,
                'message' => $validator->errors()->first()
            ]);
        }

        $company_id = '';
        $company = auth()->user()->companies;

        if(!$company){
            $company_id = auth()->user()->employee->parent_company;
        } else {
            $company_id = $company->id;
        }

        // Check for duplicates in local database first // Semak pendua dalam pangkalan data tempatan dahulu
        $listSku = [];
        foreach($request->skus as $skuData) {
            // Check in main products table // Semak dalam jadual produk utama
            $product = Product::where('product_SKU', $skuData['sku'])
                ->where('parent_company', $company_id);

            // Exclude current product if editing main product // Kecualikan produk semasa jika mengedit produk utama
            if(isset($skuData['exclude_product_id'])) {
                $product->where('id', '!=', $skuData['exclude_product_id']);
            }

            $existingProduct = $product->first();
            // Log::info('Exclude product ID: ' . json_encode($existingProduct));

            if($existingProduct){
                return response()->json([
                    'result' => false,
                    'message' => "SKU '{$skuData['sku']}' already used for product [ {$existingProduct->product_name} ]"
                ]);
            }

            // Check in variant products table // Semak dalam jadual produk variant
            $variantProduct = ProductVariantAttribute::where('product_sku', $skuData['sku'])
                ->whereHas('product', function($q) use($company_id) {
                    $q->where('parent_company', $company_id);
                });

            // Exclude current variant if editing variant product // Kecualikan variant semasa jika mengedit produk variant
            if(isset($skuData['exclude_variant_id']) && $skuData['exclude_variant_id']) {
                $variantProduct->where('id', '!=', $skuData['exclude_variant_id']);
            }

            $existingVariant = $variantProduct->first();

            // Log::info('Exclude variant ID: ' . json_encode($skuData['sku']));

            if($existingVariant){
                return response()->json([
                    'result' => false,
                    'message' => "SKU '{$skuData['sku']}' already used for variant product"
                ]);
            }

            // $returnCheckSku = $this->productCheckSkuForVariant($skuData['sku']);
            // if ($returnCheckSku['result']) {
            //     return response()->json([
            //         'result' => false,
            //         'message' => $returnCheckSku['message']
            //     ]);
            // }

            if($existingProduct || $existingVariant){
                $listSku[] = [
                    'exist' => true,
                    'productsku' => $skuData['sku'],
                    'productname' => $skuData['name']
                ];
            }

            // $listSku[] = [
            //     'exist' => $existingProduct ? true : false,
            //     'productsku' => $skuData['sku'],
            //     'productname' => $skuData['name']
            // ];
        }

        // Log::info('List SKU: ' . json_encode($listSku));

        // Only check Bizapp if user is Bizapp user // Hanya semak Bizapp jika pengguna adalah pengguna Bizapp
        if(auth()->user()->isBizappUser === "Y"){
            $checkSku = $this->bizappCheckSku('', $listSku, auth()->user()->pid);

            if(!$checkSku['result']){
                return response()->json([
                    'result' => false,
                    'message' => $checkSku['message']
                ]);
            }
        }

        return response()->json([
            'result' => true,
            'message' => 'All SKUs are available'
        ]);
    }

    public function create(Request $request){
        // $categories = Category::where('parent_id', $request->user()->companies->category_id)->get();
        // $materials = Cache::remember('materials_' . $request->user()->id, 60*60*24, function(){
        //     $array = [];
        //     $defaults = Material::select('id','company_id','name','slug')->whereNull('company_id')->orderBy('name','asc')->get();
        //     foreach($defaults as $default){
        //         $array[] = $default;
        //     }
        //     $companyMaterials = Material::select('id','company_id','name','slug')->where('company_id', auth()->user()->companies->id)->orderBy('name','asc')->get();
        //     foreach($companyMaterials as $companyMaterial){
        //         $array[] = $companyMaterial;
        //     }
        //     $array = collect($array)->sortBy('name')->values()->all();
        //     return $array;
        // });

        $pCategory = ProductsCategory::orderBy('category_name')->get();

        // Get active UOMs / Dapatkan UOM aktif
        $uoms = \App\Models\Uom::active()->orderBy('uom_type')->orderBy('uom_name')->get();

        $variantAttributes = VariantAttribute::where('company_id', $request->user()->companies->id)
            ->orWhereNull('company_id')
            ->orderBy('name')
            ->get();

        $variantValues = VariantAttributeValue::all()->groupBy('variant_attribute_id');

        $productLhdnCodes = ProductLhdnCode::all();

        return view('backend.product-create', [
            'sku' => $request->sku,
            // 'materials' => $materials,
            'categories' => $pCategory,
            'uoms' => $uoms,
            'variantAttributes' => $variantAttributes,
            'variantValues' => $variantValues,
            'productLhdnCodes' => $productLhdnCodes
        ]);
    }

    public function storeV2(Request $request){
        $user = auth()->user();
        if ($user->isBizappUser === 'N') {
            $company_id = $user->companies->id;

            $activeSubscription = Subscription::where('company_id', $company_id)
                ->where('status', 'active')
                ->where(function($query) {
                    $query->whereNull('ends_at')
                        ->orWhere('ends_at', '>', now());
                })
                ->with('subscriptionPlan.features')
                ->first();

            if ($activeSubscription) {
                $plan = $activeSubscription->subscriptionPlan;
                $skuLimitFeature = $plan->features->where('type', 'limit_upgrade')->first();

                if ($skuLimitFeature) {
                    $limit = $skuLimitFeature->pivot->limit ?? 0;
                    $currentProductCount = Product::where('parent_company', $company_id)->count();

                    if ($currentProductCount >= $limit) {
                        return redirect()->back()->with('error', 'You have reached your SKU limit. Please upgrade your plan to add more products.');
                    }
                }
            }
        }
        $validator = Validator::make($request->all(), [
            'main-image' => 'required|image|mimes:png,jpg,jpeg,heic|max:4096',
            'sku' => 'required|string',
            'name' => 'required|string',
            'category' => 'required|string',
            'brand' => 'string',
            'description' => 'string',
            'price' => 'required|string',
            'inventory' => 'required|string',
        ]);

        if($validator->fails()){
            return response()->json([
                'result' => false,
                'message' => $validator->errors()->first()
            ]);
        }

        $user = getUser();

        //Check Category
        $category = Category::where('id', $request->category)->first();
        if(!$category){
            return response()->json([
                'result' => false,
                'message' => "Category not found"
            ]);
        }

        //Check SKU
        $checkSKU = Product::where([
            'product_SKU' => $request->sku,
            'parent_company' => $user['company']['id']
        ])->first();
        if($checkSKU){
            return response()->json([
                'result' => false,
                'message' => "SKU already used for product [ {$checkSKU->product_name} ] "
            ]);
        }

        $inventory = ($request->inventory == "Ready Stock") ? '-100' : (int) $request->inventory;
        $price = str_replace([',','RM'],'',$request->price);

        try {
            DB::beginTransaction();
            $materials = Material::whereIn('id', explode(',', $request->materials))->pluck('id')->toArray();

            $product = Product::create([
                'product_brand' => $request->brand ?? 'TIADA BRAND',
                'product_SKU' => $request->sku,
                'product_name' => $request->name,
                'product_stock' => $inventory,
                'product_stock_status' => $inventory == "-100" ? 'Y' : 'N',
                'product_price' => $price,
                'product_description' => $request->description,
                'parent_company' => $user['company']['id'],
                'category_id' => $category->id ?? null,
                'material_ids' => $materials
            ]);

            $prodDetail = ProductDetail::create([
                'product_id' => $product->id,
                'product_attachment' => null
            ]);

            if($request->user()->isBizappUser == "Y"){
                try {
                    $urlPos = config('bizappos.bizappos_api_url');
                    $urlPos123 = config('bizappos.bizappos_api_url_test');
                    $addProductToBizapp = Http::asForm()->post($urlPos123 . 'api_name=TRACK_ADDPRODUCT&TX=',[
                        'pid' => auth()->user()->pid,
                        'token' => 'aa',
                        'DOMAIN' => auth()->user()->domain,
                        'productname' => $request->name,
                        'sku' => $request->sku,
                        'price' => $request->price,
                        'stoktidakterhad' => $inventory == "-100" ? 'Y' : 'N',
                        'bilstok' => $inventory,
                        'productcategory' => $category->bizapp_code ?? null,
                        'productbrand' => $request->brand
                    ])
                    ->throw()
                    ->json();
                    if($addProductToBizapp){
                        $product->update([
                            'product_id_bizapp' => $addProductToBizapp
                        ]);

                        if($request->file('main-image')){
                            Http::attach(
                                'file', file_get_contents($request->file('main-image')->getRealPath()),
                                $request->file('main-image')->getClientOriginalName()
                            )->asMultipart()->post($urlPos123 . 'api_name=TRACK_SAVE_PRODUCT_ATTACHMENT&TX=', [
                                'pid' => auth()->user()->pid,
                                'TOKEN' => 'toke',
                                'DOMAIN' => auth()->user()->domain,
                                'productid' => $addProductToBizapp,
                            ])->throw()->json();
                        }

                        if($request->inventory == "Ready Stock"){
                            $updateStockToBizapp = Http::asForm()->post($urlPos123 . 'api_name=TRACK_READYSTOCKINVENTORI&TX=',[
                                'pid' => auth()->user()->pid,
                                'domain' => auth()->user()->domain,
                                'TOKEN' => 'tke',
                                'bilstok'=> '0',
                                'nota' => 'Update stock count in bizappos back-office',
                                'balance' => '0',
                                'productid' => $addProductToBizapp,
                            ])->throw()->json();
                        } else {
                            $updateStockToBizapp = Http::asForm()->post($urlPos123 . 'api_name=TRACK_ADDINVENTORI&TX=',[
                                'pid' => auth()->user()->pid,
                                'domain' => auth()->user()->domain,
                                'TOKEN' => 'tke',
                                'bilstok'=> $inventory,
                                'nota' => 'Update stock count in bizappos back-office',
                                'balance' => $inventory,
                                'productid' => $addProductToBizapp,
                            ])->throw()->json();
                        }

                        $updateProductStatus = Http::asForm()->post($urlPos123 . 'api_name=TRACK_SHOWPRODUCT_DONTSILENCE&TX=',[
                            'pid' => auth()->user()->pid,
                            'token' => 'aa',
                            'DOMAIN' => auth()->user()->domain,
                            'productid' => $addProductToBizapp
                        ])->throw()->json();

                        $getDetailFromBizapp = Http::asForm()->post($urlPos . 'api_name=TRACK_GET_PRODUCT_INFO',[
                            'pid' => auth()->user()->pid,
                            'token' => 'aa',
                            'DOMAIN' => auth()->user()->domain,
                            'productid' => $addProductToBizapp
                        ])->throw()->json();
                        if(!empty($getDetailFromBizapp) && $getDetailFromBizapp[0]['STATUS'] === '1'){
                            $prodDetail->product_attachment = $getDetailFromBizapp[0]['attachment'];
                            $prodDetail->save();
                        }
                    }
                } catch (Exception $e){
                    DB::rollback();
                    Log::error('BizApp store product API error: ' . $e->getMessage());
                    return response()->json([
                        'result' => false,
                        'message' => "Error updating product in BizApp"
                    ]);
                }
            }

            DB::commit();
            return response()->json([
                'result' => true,
                'message' => "Product inserted successfully",
                'redirect' => route('products')
            ]);
        } catch (Exception $e){
            DB::rollback();
            return response()->json([
                'result' => false,
                'message' => "Failed to create product"
            ]);
        }
    }

    public function store(Request $request){
        $user = auth()->user();
        if ($user->isBizappUser === 'N') {
            $company_id = $user->companies->id;

            $activeSubscription = Subscription::where('company_id', $company_id)
                ->where('status', 'active')
                ->where(function($query) {
                    $query->whereNull('ends_at')
                        ->orWhere('ends_at', '>', now());
                })
                ->with('subscriptionPlan.features')
                ->first();

            if ($activeSubscription) {
                $plan = $activeSubscription->subscriptionPlan;
                $skuLimitFeature = $plan->features->where('type', 'limit_upgrade')->first();

                if ($skuLimitFeature) {
                    $limit = $skuLimitFeature->pivot->limit ?? 0;
                    $currentProductCount = Product::where('parent_company', $company_id)->count();

                    if ($currentProductCount >= $limit) {
                        return redirect()->back()->with('error', 'You have reached your SKU limit. Please upgrade your plan to add more products.');
                    }
                }
            }
        }
        // dd($request->all());
        $validator = Validator::make($request->all(), [
            'productSKU' => 'required|string|max:50',
            'productName' => 'required|string',
            'productCategory' => 'required|string',
            'productUom' => 'required|exists:uoms,id',
            'productPrice' => 'required|decimal:0,2',
            'productInventory' => ['required', function ($attribute, $value, $fail) {
                if ($value === 'Ready Stock') {
                    return; // Allow "Ready Stock" / Benarkan "Ready Stock"
                }
                if (!is_numeric($value)) {
                    $fail('The stock quantity must be a number or "Ready Stock".');
                    return;
                }
                // Check 3 decimal places max / Periksa maksimum 3 tempat decimal
                if (preg_match('/\.\d{4,}/', $value)) {
                    $fail('The stock quantity may not have more than 3 decimal places.');
                }
            }],
            'minQuantity' => 'nullable|numeric|min:0|regex:/^\d+(\.\d{1,3})?$/',
            'stepQuantity' => ['nullable', 'numeric', 'min:0', function ($attribute, $value, $fail) use ($request) {
                // Get UOM type from productUom ID / Dapatkan jenis UOM dari ID productUom
                $uom = \App\Models\Uom::find($request->productUom);
                if ($uom && $uom->uom_type === 'count') {
                    // For count UOM, only allow integers / Untuk UOM count, hanya benarkan integer
                    // Check if value is a whole number / Semak jika nilai adalah nombor bulat
                    if (floor((float)$value) != (float)$value) {
                        $fail('Step quantity must be a whole number for count UOM.');
                        return;
                    }
                } else {
                    // For other UOM types, allow up to 3 decimal places / Untuk jenis UOM lain, benarkan sehingga 3 tempat decimal
                    if (preg_match('/\.\d{4,}/', $value)) {
                        $fail('The step quantity may not have more than 3 decimal places.');
                    }
                }
            }],
            'main-image' => 'file|mimes:png,jpg,jpeg,heic|max:2048',
            'isVariant' => 'nullable|string',
            'productLhdnCode' => 'nullable|exists:product_lhdn_codes,id'
        ]);

        if ($validator->fails()) {
            Log::error('Add product validation error : ' . json_encode($validator->errors()));
            return redirect()->back()->withErrors($validator)->withInput();
        }

        // if ada variant, baru proceed
        if($request->isVariant == "on"){
            // check validator for variant product
            $variantValidator = Validator::make($request->all(), [
                'variant1' => 'nullable|string',
                'variant2' => 'nullable|string',
                'variants' => 'required|array',
                'variants.*.name' => 'required|string',
                'variants.*.sku' => 'required|string',
                'variants.*.stock' => 'required|string',
                'variants.*.price' => 'required|string',
                'variants.*.main-image' => 'file|mimes:png,jpg,jpeg,heic|max:2048',
                'variants.*.variant1_value' => 'required|string',
                'variants.*.variant2_value' => 'nullable|string'
            ]);

            if($variantValidator->fails()){
                return redirect()->back()->withErrors($variantValidator)->withInput();
            }
            // check sku dari bizapp juga (tak siap lagi)
            // $listSku = [];
            // foreach($request->variants as $variant){
            //     $listSku[] = [
            //         'productsku' => $variant['sku'],
            //         'productname' => $variant['name']
            //     ];
            // }

            // $checkSku = $this->bizappCheckSku($request->productSKU, $listSku, auth()->user()->pid);
            // if(!$checkSku['result']){
            //     return redirect()->back()->with('duplicate_sku', $checkSku['message'])->withInput();
            // }

            // // productCheckSkuForVariant()
            // foreach($request->variants as $variant){
            //     $returnCheckSku = $this->productCheckSkuForVariant($variant['sku']);
            //     if ($returnCheckSku['result']) {
            //         return redirect()->back()->with('duplicate_sku', $returnCheckSku['message'])->withInput();
            //     }
            // }
        }

        // $category = Category::where('slug', $request->productCategory)->first();
        // if(!$category){
        //     return redirect()->back()->with('error', 'Category not found');
        // }

        $userProductCategory = ProductsCategory::Where('id',$request->productCategory)->first();
        if (!$userProductCategory) {
            Log::error('Add product validation error : productcategory not found');
            return redirect()->back()->with('error', 'Product category not found')->withInput();
        }

        // Collect variant data for batch processing // Kumpul data variant untuk pemprosesan batch
        $variantBatchData = [];
        $userProductCategory = ProductsCategory::where('id', $request->productCategory)->first();

        try{
            DB::transaction(function () use ($request, &$variantBatchData, &$product, &$prodDetails) {
                $product = Product::create([
                    'product_brand' => $request->productBrand ?? 'TIADA BRAND',
                    'product_SKU' => $request->productSKU,
                    'product_name' => $request->productName,
                    'uom_id' => $request->productUom,
                    'product_stock' => $request->productInventory == "Ready Stock" ? '-100' : $request->productInventory,
                    'product_stock_status' => $request->productInventory == "Ready Stock" ? 'Y' : 'N',
                    'product_price' => $request->productPrice,
                    'min_quantity' => $request->minQuantity ?? 0,
                    'step_quantity' => $request->stepQuantity ?? 0,
                    'product_description' => $request->productDescription,
                    'parent_company' => auth()->user()->companies->id ?? auth()->user()->employee->parent_company,
                    'isVariant' => $request->isVariant == "on" ? 'Y' : 'N',
                    'product_lhdn_code_id' => $request->productLhdnCode ?? 66
                    // 'category_id' => $request->productCategory ?? null
                ]);

                // Store product attachment // Simpan lampiran produk
                $imageFilename = null;
                if($request->file('main-image')){
                    $imageFilename = $this->storeImageAws($request->file('main-image'), $request->productSKU, $product->id);
                }else{
                    $imageFilename = $product->productDetail->product_attachment ?? null;
                }

                $prodDetails = ProductDetail::create([
                    'category_id' => $request->productCategory ?? null,
                    'product_id' => $product->id,
                    'product_attachment' => $imageFilename
                ]);

                // Handle variants if isVariant is on
                if($request->isVariant == "on") {
                    // dd($request->variants);
                    // Log::info('Total variants to process: ' . count($request->variants));
                    foreach($request->variants as $key => $variant) {
                        // Log::info('Processing variant ' . ($key + 1) . ' of ' . count($request->variants));

                        $stockValue = $variant['stock'] == "Ready Stock" ? '-100' : $variant['stock'];
                        $stockStatus = $variant['stock'] == "Ready Stock" ? 'Y' : 'N';

                        // Store variant image // Simpan gambar variant
                        // // Handle image upload atau existing image / Urus upload gambar atau gambar sedia ada
                        // $imageFilename = null;
                        // if (isset($variant['main-image']) && $variant['main-image'] instanceof UploadedFile) {
                        //     // User uploaded new image / Pengguna upload gambar baru
                        //     $imageFilename = $this->storeProductImage($variant['main-image'], $variant['sku']);
                        // } elseif (isset($variant['existing_image']) && !empty($variant['existing_image'])) {
                        //     // Keep existing image / Kekalkan gambar sedia ada
                        //     $imageFilename = $variant['existing_image'];
                        // }
                        $variantProduct = ProductVariantAttribute::create([
                            'product_id' => $product->id,
                            'product_bizapp_id' => null, // letak null dulu, nanti dah insert dalam bizapp baru update
                            'product_name' => $variant['name'],
                            'product_sku' => $variant['sku'],
                            'product_stock' => $stockValue,
                            'uom_id' => $request->productUom,
                            'min_quantity' => $request->minQuantity ?? 0,
                            'step_quantity' => $request->stepQuantity ?? 0,
                            'product_stock_status' => $stockStatus,
                            'product_price' => $variant['price'],
                            'product_status' => '1',
                            'product_weight' => null,
                            'product_lhdn_code_id' => $request->productLhdnCode ?? 66
                        ]);

                        $imageFilename = $this->storeImageAws($variant['main-image'] ?? null, '', $variantProduct->id);

                        $productDetail = ProductDetail::create([
                            'category_id' => $request->productCategory ?? null,
                            'product_id' => $product->id,
                            'product_variant_attribute_id' => $variantProduct->id,
                            'product_attachment' => $imageFilename ?? null
                        ]);

                        // composite attribute value
                        CompositeAttributeValue::create([
                            'product_variant_attribute_id' => $variantProduct->id,
                            'variant_attribute_value_id' => $variant['variant1_value'],
                            'order_index' => 1
                        ]);

                        if($request->variant2){
                            CompositeAttributeValue::create([
                                'product_variant_attribute_id' => $variantProduct->id,
                                'variant_attribute_value_id' => $variant['variant2_value'],
                                'order_index' => 2
                            ]);
                        }

                        // Collect variant data for batch processing // Kumpul data variant untuk batch
                        if(auth()->user()->isBizappUser === 'Y'){
                            $variantBatchData[] = [
                                'variant' => $variantProduct,
                                'productDetail' => $productDetail,
                                'imageFile1' => $imageFilename, // Image already stored locally, just pass filename + gambar dah disimpan tempatan, hantar nama fail sahaja
                                'imageFile2' => $imageFilename2 ?? null,
                                'imageFile3' => $imageFilename3 ?? null,
                                'imageFile4' => $imageFilename4 ?? null
                            ];
                        }
                    }
                }
            });

                /// if product tu bukan variant then hanta to bizapp
            if(auth()->user()->isBizappUser === 'Y' && $request->isVariant !== "on" && app()->environment('production')){ // update the product in BIZAPP to sync
                // For single products, still use immediate processing // Untuk produk tunggal, masih guna pemprosesan segera
                $this->bizappAddProduct($request, $product, null, $prodDetails, null);
            }
            // dd($variantBatchData);

            // Dispatch batch job for variant products // Hantar batch job untuk produk variant
            if(auth()->user()->isBizappUser === 'Y' && !empty($variantBatchData) && app()->environment('production')){
                $userData = [
                    'pid' => auth()->user()->pid,
                    'domain' => auth()->user()->domain
                ];

                $requestData = [
                    'productBrand' => $request->productBrand ?? 'TIADA BRAND',
                    'category_bizapp_code' => $userProductCategory->bizapp_code ?? null
                ];

                // Dispatch batch job to queue // Hantar batch job ke queue
                BizappAddProductBatchJob::dispatch($userData, $variantBatchData, $requestData, $product->id, $prodDetails)
                    ->onQueue('bizapp-batch-products');

                Log::info('Batch job dispatched for ' . count($variantBatchData) . ' variant products');
            }

            return redirect()->route('products')->with('success', 'Product inserted successfully');
        } catch (Exception $e) {
            DB::rollback();
            Log::error('insert product error : ' . $e->getMessage());
            return redirect()->back()->with('error', 'Something is wrong, please contact admin')->withInput();
        }

    }

    public function createProduct(Request $request)
    {
        $sku = $request->input('sku');

        if(auth()->user()->isBizappUser == 'Y' && app()->environment('production')){

        $urlPos = config('bizappos.bizappos_api_url');
        $checkSKU = Http::asForm()->post($urlPos . 'api_name=TRACK_CHECK_PRODUCTSKU_DTLS', [
            'DOMAIN' => auth()->user()->domain,
            'pid' => auth()->user()->pid,
            'productsku' => $sku
        ])->throw()->json();

            if ($checkSKU == []) {
                $getAllProductCategory = ProductsCategory::orderBy('category_name','asc')->get();
                return view('backend.product-create')
                    ->with('sku',$sku)
                    ->with('productsCategory',$getAllProductCategory);
            } else {
                return redirect()->route('products')->with('inuse','SKU is already in use');
            }
        }

    }

    public function productDelete($productId)
    {

        try {
            DB::beginTransaction();
            $product = Product::with('productDetail')->findOrFail($productId);
            if (auth()->user()->isBizappUser === 'Y' && app()->environment('production')) {
                $this->deleteBizappProduct($product->product_id_bizapp);
            }

            $this->deleteLocalProduct($product);

            DB::commit();
            return response()->json([
                'success' => true,
                'message' => 'Product deleted successfully'
            ]);
        } catch (Exception $e) {
            Log::error('Product deletion failed: ' . $e->getMessage());
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete product: ' . $e->getMessage()
            ], 500);
        }
    }

    private function deleteBizappProduct($productIdBizapp)
    {
        $urlPos = config('bizappos.bizappos_api_url');
        $domain = auth()->user()->domain;

        $secretKey = $this->getBizappSecretKey($urlPos, $domain);

        $response = Http::asForm()->post($urlPos . 'api_name=TRACK_DELETEPRODUCT&TX=', [
            'DOMAIN' => $domain,
            'pid' => auth()->user()->pid,
            'productid' => $productIdBizapp,
            'TOKEN' => 'aa',
            'secretkey' => $secretKey
        ])->throw()->json();

        // You might want to check the response and handle any API-specific errors here
    }

    private function getBizappSecretKey($urlPos, $domain)
    {
        $response = Http::asForm()->post($urlPos . 'api_name=TRACK_LOGIN', [
            'DOMAIN' => $domain,
            'username' => auth()->user()->username,
            'password' => config('bizappos.bizapp_mpw'),
            'platform' => 'POS'
        ])->throw()->json();

        return $response[0]['secretkey_boss'];
    }

    private function deleteLocalProduct(Product $product)
    {
        try{
            $product->productDetail()->delete();
            $product->collectionProducts()->delete();

            // Delete composite attributes for each variant // Padamkan nilai atribut komposit untuk setiap variant
            if ($product->variants) {
                foreach ($product->variants as $variant) {
                    if ($variant) {
                        $variant->compositeAttributeValues()->delete();
                    }
                }
            }

            // Delete variants // Padamkan variant
            if ($product->variants) {
                foreach ($product->variants as $variant) {
                    if ($variant && $variant->id) {
                        // Delete product detail first // Padamkan detail produk terlebih dahulu
                        // $variant->productDetail()->delete();

                        // Delete product attachment from S3 // Padamkan lampiran produk dari S3
                        // $productAttachment = DB::table('product_variant_attribute')
                        //     ->join('product_details', 'product_variant_attribute.id', '=', 'product_details.product_variant_attribute_id')
                        //     ->where('product_variant_attribute.id', $variant->id)
                        //     ->value('product_details.product_attachment');

                        $productAttachment = ProductDetail::where('product_variant_attribute_id', $variant->id)->value('product_attachment');

                        Log::info('Product attachment: ' . $productAttachment);

                        if ($productAttachment && $productAttachment !== null && $productAttachment !== '') {
                            $this->deleteImageAws($productAttachment);
                        }

                        // Delete the variant last // Padamkan variant terakhir
                        $variant->delete();
                    }
                }
            }
            // $product->variants()->delete();
            $product->delete();
        }catch (Exception $e){
            Log::error('Product deletion failed: ' . $e->getMessage());
            throw $e;
        }
    }

    public function edit(Request $request, $id){
        $product = Product::with(['productDetail', 'variants'])->find($id);
        $user = getUser();
        $categories = Category::where('parent_id', $user['company']['category_id'])->get();
        $materials = Cache::remember('materials_' . $user['id'], 60*60*24, function() use($user){
            $array = [];
            $defaults = Material::select('id','company_id','name','slug')
                ->whereNull('company_id')
                ->orderBy('name','asc')
                ->get();
            foreach($defaults as $default){
                $array[] = $default;
            }
            $companyMaterials = Material::select('id','company_id','name','slug')
                ->where('company_id', $user['company']['id'])
                ->orderBy('name','asc')
                ->get();
            foreach($companyMaterials as $companyMaterial){
                $array[] = $companyMaterial;
            }
            $array = collect($array)->sortBy('name')->values()->all();
            return $array;
        });

        return view('backend.product.edit', [
            'product' => $product,
            'materials' => $materials,
            'categories' => $categories,
        ]);
    }

    public function update(Request $request, $id){
        $validator = Validator::make($request->all(), [
            'main-image' => 'nullable|image|mimes:png,jpg,jpeg|max:4096',
            'sku' => 'required|string',
            'name' => 'required|string',
            'category' => 'required|string',
            'brand' => 'string',
            'description' => 'string',
            'price' => 'required|string',
            'inventory' => 'required|string',
        ]);

        if($validator->fails()){
            return response()->json([
                'result' => false,
                'message' => $validator->errors()->first()
            ]);
        }

        $user = getUser();
        $product = Product::with('productDetail')->where([
            'id' => $id,
            'parent_company' => $user['company']['id'] ?? null
        ])->first();
        if(!$product){
            return response()->json([
                'result' => false,
                'message' => "Product not found"
            ]);
        }

        //Check Category
        $category = Category::where('id', $request->category)->first();
        if(!$category){
            return response()->json([
                'result' => false,
                'message' => "Category not found"
            ]);
        }

        //Check SKU
        $checkSKU = Product::where([
            'product_SKU' => $request->sku,
            'parent_company' => $user['company']['id']
        ])->where('id', '!=', $id)->first();
        if($checkSKU){
            return response()->json([
                'result' => false,
                'message' => "SKU already used for product [ {$checkSKU->product_name} ] "
            ]);
        }

        try {
            DB::beginTransaction();

            $materials = Material::whereIn('id', explode(',', $request->materials))->pluck('id')->toArray();
            $inventory = ($request->inventory == "Ready Stock") ? '-100' : (int) $request->inventory;
            $price = str_replace([',','RM'],'',$request->price);

            //Check negatif inventory
            if($request->inventory !== "Ready Stock" && $inventory < 0){
                return response()->json([
                    'result' => false,
                    'message' => "Inventory cannot be negative"
                ]);
            }

            if($request->user()->isBizappUser === "Y"){
                try {
                    $urlPos = config('bizappos.bizappos_api_url');
                    $updateProductToBizapp = Http::asForm()->post($urlPos . 'api_name=TRACK_UPDATEPRODUCT_IGNORESTOCK&TX=',[
                        'pid' => auth()->user()->pid,
                        'token' => 'aa',
                        'DOMAIN' => auth()->user()->domain,
                        'productid' => $product->product_id_bizapp,
                        'new_productname' => $request->name,
                        'new_productsku' => $request->sku,
                        'new_sellingprice' => $price,
                        'statusstokX' => $inventory == "-100" ? 'Y' : 'N',
                        'bilstokX' => $inventory,
                        'new_productcategory' => $category->bizapp_code,
                        'new_productbrand' => $request->brand
                    ])->throw()->json();
                    if($updateProductToBizapp){
                        $product->product_id_bizapp = $updateProductToBizapp;

                        if($request->file('main-image')){
                            Http::attach(
                                'file', file_get_contents($request->file('main-image')->getRealPath()),
                                $request->file('main-image')->getClientOriginalName()
                            )->asMultipart()->post($urlPos . 'api_name=TRACK_SAVE_PRODUCT_ATTACHMENT&TX=', [
                                'pid' => auth()->user()->pid,
                                'TOKEN' => 'toke',
                                'DOMAIN' => auth()->user()->domain,
                                'productid' => $updateProductToBizapp,
                            ])->throw()->json();
                        }

                        if($request->inventory === "Ready Stock"){
                            try {
                                $updateStockToBizapp = Http::asForm()->post($urlPos . 'TRACK_READYSTOCKINVENTORI&TX=', [
                                    'pid' => auth()->user()->pid,
                                    'domain' => auth()->user()->domain,
                                    'TOKEN' => 'tke',
                                    'bilstok' => '0',
                                    'nota' => 'Update stock count in bizappos back-office',
                                    'balance' => '0',
                                    'productid' => $updateProductToBizapp,
                                ])->throw()->json();
                                Log::info("Update product to bizapp for : $updateProductToBizapp , statusstokX = Ready stock");
                            } catch (\Exception $e) {
                                Log::error("Update Ready Stock failed for $updateProductToBizapp: " . $e->getMessage());
                            }
                        } else if ($request->inventory !== "Ready Stock" && (int) $request->inventory > $product->product_stock) {
                            try {
                                $updateStockToBizapp = Http::asForm()->post($urlPos . 'api_name=TRACK_ADDINVENTORI&TX=', [
                                    'pid' => auth()->user()->pid,
                                    'domain' => auth()->user()->domain,
                                    'TOKEN' => 'tke',
                                    'bilstok' => (int) $request->inventory - (int) $product->product_stock,
                                    'nota' => 'Update stock count in bizappos back-office',
                                    'balance' => $product->product_stock != -100 ? $product->product_stock : 0,
                                    'productid' => $updateProductToBizapp,
                                ])->throw()->json();
                                Log::info("Update product to bizapp for : $updateProductToBizapp , statusstokX = " . (int)$request->inventory - $product->product_stock);
                            } catch (\Exception $e) {
                                Log::error("Update Add Inventory failed for $updateProductToBizapp: " . $e->getMessage());
                            }
                        } else if ($request->inventory !== "Ready Stock" && (int)$request->inventory < $product->product_stock) {
                            try {
                                $updateStockToBizapp = Http::asForm()->post($urlPos . 'api_name=TRACK_MINUSINVENTORI&TX=', [
                                    'pid' => auth()->user()->pid,
                                    'domain' => auth()->user()->domain,
                                    'TOKEN' => 'tke',
                                    'bilstok' => (int) $product->product_stock - (int) $request->inventory,
                                    'nota' => 'Update stock count in bizappos back-office',
                                    'balance' => $product->product_stock != -100 ? $product->product_stock : 0,
                                    'productid' => $updateProductToBizapp,
                                ])->throw()->json();
                                Log::info("Update product to bizapp for : $updateProductToBizapp , statusstokX = " .  (int)$product->product_stock - (int)$request->inventory);
                            } catch (\Exception $e) {
                                Log::error("Update Minus Inventory failed for $updateProductToBizapp: " . $e->getMessage());
                            }
                        } else {
                            Log::error("Update product to bizapp for : $updateProductToBizapp failed due to condition mismatch.");
                        }

                        $getDetailFromBizapp = Http::asForm()->post($urlPos . 'api_name=TRACK_GET_PRODUCT_INFO',[
                            'pid' => auth()->user()->pid,
                            'token' => 'aa',
                            'DOMAIN' => auth()->user()->domain,
                            'productid' => $updateProductToBizapp
                        ])->throw()->json();
                        if(!empty($getDetailFromBizapp) && $getDetailFromBizapp[0]['STATUS'] === '1'){
                            $product->productDetail->product_attachment = $getDetailFromBizapp[0]['attachment'];
                        }
                    }

                    if($request->productInventory != 'Ready Stock'){
                        // $product_stock = (int) $getProduct->product_stock;
                        // Calculate remain, ensuring it does not go negative
                        // $remain = $request->productInventory ;
                        LogStock::create([
                            'company_id' => $getProduct->parent_company ?? '',
                            'user_id' => auth()->user()->id,
                            'product_id' => $product->id,
                            'name' => auth()->user()->username,
                            'product_name' => $product->product_name,
                            'product_sku' => $product->product_SKU ?? "",
                            'quantity' => $request->inventory,
                            'remain' => $request->inventory,
                            'note' => 'Backoffice update',
                        ]);

                    }
                } catch (Exception $e){
                    Log::error('BizApp API error: ' . $e->getMessage());
                    return response()->json([
                        'result' => false,
                        'message' => "Error updating product in BizApp"
                    ]);
                }
            }

            $product->product_brand = $request->brand;
            $product->product_SKU = $request->sku;
            $product->product_name = $request->name;
            $product->product_stock = $inventory;
            $product->product_stock_status = $inventory == '-100'? 'Y' : 'N';
            $product->product_price = $price;
            $product->product_description = $request->description;
            $product->parent_company = $user['company']['id'] ?? null;
            $product->category_id = $category->id ?? null;
            $product->material_ids = $materials;

            $product->save();

            // Save product detail separately
            $productDetail = $product->productDetail;
            if ($productDetail) {
                $productDetail->save();
            }

            DB::commit();
            return response()->json([
                'result' => true,
                'message' => "Product updated successfully",
                'redirect' => route('products')
            ]);
        } catch (Exception $e){
            DB::rollback();
            return response()->json([
                'result' => false,
                'message' => "Failed to create product"
            ]);
        }
    }

    /**
     * Edit product page - loads all necessary data for display + halaman edit produk - muat semua data untuk paparan
     * Returns: product data, variant data, categories, variant attributes/values for dropdown display
     */
    public function productEdit(Request $request, $id)
    {
        // Load product with all necessary relationships + muat produk dengan semua hubungan yang diperlukan
        $getProduct = Product::with(['productDetail' => function($query) {
            $query->whereNull('product_variant_attribute_id');
        }, 'variants.productDetail', 'productLhdnCode'])->find($id);
        $user = getUser();
        $variantData = collect();

        // Check is variant product + semak jika produk variant
        if($getProduct->isVariant == 'Y'){
            $productVariantAttributes = ProductVariantAttribute::where('product_id', $getProduct->id)
                ->with(['productDetail', 'productLhdnCode'])
                ->get();

            foreach($productVariantAttributes as $variant){
                $compositeValues = CompositeAttributeValue::where('product_variant_attribute_id', $variant->id)
                    ->with([
                        'variantAttributeValue.variantAttribute.variantAttributeValues',
                        'variantAttributeValue'
                    ])
                    ->orderBy('order_index')
                    ->get();

                $variantData->push([
                    'variant' => $variant,
                    'attributeValues' => $compositeValues
                ]);
            }
        }
        // Add variant-related data
        $variantAttributes = VariantAttribute::where('company_id', $user['company']['id'])
            ->orWhereNull('company_id')
            ->orderBy('name')
            ->with('values')
            ->get();

        $variantValues = $variantAttributes->mapWithKeys(function ($attribute) {
            return [$attribute->id => $attribute->values];
        });

        // if(auth()->user()->isBizappUser == 'Y' && app()->environment('production')){
        //     $urlPos = config('bizappos.bizappos_api_url');

        //     $getProductDetailedBizapp = Http::asForm()->post($urlPos . 'api_name=TRACK_GET_PRODUCT_INFO',[
        //         'pid' => auth()->user()->pid,
        //         'productid' => $getProduct->product_id_bizapp,
        //         'token' => 'aa',
        //         'DOMAIN' => auth()->user()->domain,
        //     ])->throw()->json();
        //         if($getProductDetailedBizapp){
        //             $productsCategories = ProductsCategory::where('bizapp_code',$getProductDetailedBizapp[0]['productcategorycode'])->first();
        //             $getProduct->product_brand = $getProductDetailedBizapp[0]['productbrand'];
        //             $getProduct->product_SKU = $getProductDetailedBizapp[0]['productsku'];
        //             $getProduct->product_name = $getProductDetailedBizapp[0]['productname'];
        //             $getProduct->product_stock = $getProductDetailedBizapp[0]['bilstok'];
        //             $getProduct->product_stock_status = $getProductDetailedBizapp[0]['statusstok'];
        //             $getProduct->product_price = $getProductDetailedBizapp[0]['price'];

        //             // Save the product first
        //             $getProduct->save();

        //             // Update category_id in product detail if needed
        //             if ($productsCategories) {
        //                 $productDetail = $getProduct->productDetail;
        //                 if ($productDetail) {
        //                     $productDetail->category_id = $productsCategories->id;
        //                     $productDetail->save();
        //                 }
        //             }
        //         }
        // }

        $getAllProductCategory = ProductsCategory::orderBy('category_name','asc')->get();
        $productLhdnCodes = ProductLhdnCode::all();

        // dd([
        //     'variantData' => $variantData,
        //     'product' => $getProduct,
        //     'productsCategory' => $getAllProductCategory,
        //     'variantValues' => $variantValues,
        //     'variantAttributes' => $variantAttributes
        // ]);
        // dd(([
        //     'variantData' => $variantData,
        //     'product' =>
        // ]));
        // dd($getProduct->productDetail);

        // Get active UOMs / Dapatkan UOM aktif
        $uoms = \App\Models\Uom::active()->orderBy('uom_type')->orderBy('uom_name')->get();

        if($getProduct){
            return view('backend.product-edit')
            ->with('productsCategory',$getAllProductCategory)
            ->with('uoms',$uoms)
            ->with('products',$getProduct)
            ->with('variantData',$variantData)
            ->with('variantValues',$variantValues)
            ->with('variantAttributes',$variantAttributes)
            ->with('productLhdnCodes',$productLhdnCodes);
        } else {
            return redirect()->back()->withError('Something is wrong, please contact admin');
        }
    }

    public function productUpdate(Request $request,$id)
    {
        // dd($request->all());
        $getProduct = Product::with('productDetail')->find($id);

        // Build validation rules
        $rules = [
            'productCategory'  => 'required|exists:products_categories,category_name',
            'productName'      => 'required',
            'productSKU'       => 'required',  // initial validation before checking uniqueness
            'productUom'       => 'required|exists:uoms,id',
            'productInventory' => ['required', function ($attribute, $value, $fail) {
                if ($value === 'Ready Stock') {
                    return; // Allow "Ready Stock" / Benarkan "Ready Stock"
                }
                if (!is_numeric($value)) {
                    $fail('The stock quantity must be a number or "Ready Stock".');
                    return;
                }
                // Check 3 decimal places max / Periksa maksimum 3 tempat decimal
                if (preg_match('/\.\d{4,}/', $value)) {
                    $fail('The stock quantity may not have more than 3 decimal places.');
                }
            }],
            'productStatus'    => 'nullable|string',
            'productPrice'     => 'required|numeric',
            'minQuantity'      => 'nullable|numeric|min:0|regex:/^\d+(\.\d{1,3})?$/',
            'stepQuantity' => ['nullable', 'numeric', 'min:0', function ($attribute, $value, $fail) use ($request) {
                // Get UOM type from productUom ID / Dapatkan jenis UOM dari ID productUom
                $uom = \App\Models\Uom::find($request->productUom);

                if ($uom && $uom->uom_type === 'count') {
                    // For count UOM, only allow integers / Untuk UOM count, hanya benarkan integer
                    if (floor((float)$value) != (float)$value) {
                        $fail('Step quantity must be a whole number for count UOM.');
                        return;
                    }
                } else {
                    // For other UOM types, allow up to 3 decimal places / Untuk jenis UOM lain, benarkan sehingga 3 tempat decimal
                    if (preg_match('/\.\d{4,}/', $value)) {
                        $fail('The step quantity may not have more than 3 decimal places.');
                    }
                }
            }],
            'main-image'       => 'file|mimes:png,jpg,jpeg,heic|max:2048',
            'isVariant'        => 'nullable|string',
            'productLhdnCode'  => 'nullable|exists:product_lhdn_codes,id'
        ];

        // If the submitted productSKU is different from the current product's SKU,
        // add a uniqueness validation scoped to the company.
        if ($request->productSKU !== $getProduct->product_SKU) {
            $rules['productSKU'] = [
                'required',
                Rule::unique('products', 'product_SKU')->ignore($getProduct->id)
                    ->where(function ($query) {
                        $companyId = auth()->user()->companies->id ?? auth()->user()->employee->parent_company;
                        return $query->where('products.parent_company', $companyId)
                                     ->whereNull('products.deleted_at'); // Only non-soft-deleted rows
                    }),
            ];
        }

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        // check if the product already Y or N
        // check the request isVariant is on or off
        if($request->isVariant == "on") {
            // check validator for variant product
            $variantValidator = Validator::make($request->all(), [
                'deleted_variants' => 'nullable|array',
                'variants' => 'required|array',
                'variants.*.id' => 'nullable|string',
                'variants.*.name' => 'required|string',
                'variants.*.sku' => 'required|string',
                'variants.*.stock' => 'required|string',
                'variants.*.price' => 'required|string',
                'variants.*.main-image' => 'file|mimes:png,jpg,jpeg,heic|max:2048',
                'variants.*.variant1_value' => 'required|string',
                'variants.*.variant2_value' => 'nullable|string'
            ]);
            if($variantValidator->fails()){
                return redirect()->back()->withErrors($variantValidator)->withInput();
            }
        }

        $userProductCategory = ProductsCategory::Where('category_name',$request->productCategory)->first();
        if (!$getProduct || !$userProductCategory) {
            return redirect()->back()->with('error', 'Product or category not found');
        }
        // 1 = add, 2 = update, 3 = delete
        $variantBatchData = [];

        try{
            DB::beginTransaction();
            // delete the variants that has id in the deleted_variants array
            if($request->deleted_variants){
                // loop the deleted_variants array
                foreach ($request->deleted_variants as $id) {
                    // get product variant based on id
                    $productVariant = ProductVariantAttribute::find($id);
                    if($productVariant){
                        $variantBatchData[] = [
                            'action' => 3,
                            // 'product' => $getProduct,
                            'variant' => $productVariant,
                            'productDetail' => null,
                            'imageFile' => null,
                        ];
                        $productVariant->delete();
                    }
                    // delete composite attribute values based on product variant attribute id
                    CompositeAttributeValue::where('product_variant_attribute_id', $id)->delete();
                    // delete product detail based on product variant attribute id
                    ProductDetail::where('product_variant_attribute_id', $id)->delete();

                    // delete product attachment from s3

                    // $variantBatchData[] = [
                    //     'action' => 3,
                    //     // 'product' => $getProduct,
                    //     'variant' => $productVariant,
                    //     'productDetail' => null,
                    //     'imageFile' => null,
                    // ];

                    // delete from bizapp
                    // if(auth()->user()->isBizappUser === 'Y'  ){ // update the product in BIZAPP to sync
                    //     $this->deleteBizappProduct($productVariant->product_id_bizapp);
                    // }
                }
            }
            if($request->isVariant == "on"){
                // loop the variants array
                foreach ($request->variants as $variant) {
                    // check if id is null or not. if have id it will update. no id will create as new variant product
                    $stockValue =  $variant['stock']; // $variant['stock'] == "Ready Stock" ? '-100' : $variant['stock'];
                    $stockStatus = $variant['stock'] === "-100" ? 'Y' : 'N'; // $variant['stock'] == "Ready Stock" ? 'Y' : 'N';
                    if (!isset($variant['id']) || empty($variant['id'])) {
                        // check if the variant sku already exist in the database
                        // $returnCheckSku = $this->productCheckSkuForVariant($variant['sku']);
                        // if ($returnCheckSku['result']) {
                        //     return redirect()->back()->with('duplicate_sku', $returnCheckSku['message']);
                        // }
                        // // check sku kat bizapp jugak

                        // validate the variant. create new variant
                        $variantProduct = ProductVariantAttribute::create([
                            'product_id' => $getProduct->id,
                            'product_id_bizapp' => null, // letak null dulu, nanti dah insert dalam bizapp baru update
                            'product_name' => $variant['name'],
                            'product_sku' => $variant['sku'],
                            'product_stock' => $stockValue,
                            'product_stock_status' => $stockStatus,
                            'product_price' => $variant['price'],
                            'product_status' => $request->productStatus == "on" ? 1 : 0,
                            'product_weight' => null,
                            'step_quantity' => $request->stepQuantity,
                            'uom_id' => $request->productUom,
                            'product_lhdn_code_id' => $request->productLhdnCode ?? 66
                        ]);

                        // store product attachment to s3
                        $imagePath = $this->storeImageAws($variant['main-image'] ?? null, '', $variantProduct->id);

                        // $imageFilename = $this->storeProductImage($variant['main-image'] ?? null, auth()->user()->username . '_' . $variant['sku']);

                        $productDetail = ProductDetail::create([
                            'category_id' => $userProductCategory->id,
                            'product_id' => $getProduct->id,
                            'product_variant_attribute_id' => $variantProduct->id,
                            'product_attachment' => $imagePath
                        ]);

                        // composite attribute value
                        CompositeAttributeValue::create([
                            'product_variant_attribute_id' => $variantProduct->id,
                            'variant_attribute_value_id' => $variant['variant1_value'],
                            'order_index' => 1
                        ]);

                        if(isset($variant['variant2_value'])){
                            CompositeAttributeValue::create([
                                'product_variant_attribute_id' => $variantProduct->id,
                                'variant_attribute_value_id' => $variant['variant2_value'],
                                'order_index' => 2
                            ]);
                        }

                        $variantBatchData[] = [
                            'action' => 1,
                            'variant' => $variantProduct,
                            'productDetail' => $productDetail,
                            'imageFile' => $imagePath // Image already stored to S3, just pass the path + gambar dah disimpan ke S3, hantar path sahaja
                        ];

                        // // create kat bizapp
                        // if(auth()->user()->isBizappUser === 'Y'){ // update the product in BIZAPP to sync
                        //     $this->bizappAddProduct($request, null, $variantProduct, $productDetail, $variant['main-image'] ?? null);
                        // }
                    }else{
                        // validate the variant. update current variant
                        // get all variant
                        $currVariant = ProductVariantAttribute::find($variant['id']);
                        $productDetail = ProductDetail::where('product_variant_attribute_id', $variant['id'])->first();
                        // check if currVariant exist or not
                        if(!$currVariant){
                            return redirect()->back()->with('error', 'Variant not found');
                        }

                        // if($currVariant->product_sku != $variant['sku']){
                        //     // check if the variant sku already exist in the database
                        //     $returnCheckSku = $this->productCheckSkuForVariant($variant['sku']);
                        //     if ($returnCheckSku['result']) {
                        //         return redirect()->back()->with('duplicate_sku', $returnCheckSku['message']);
                        //     }

                        //     // check sku kat bizapp jugak
                        // }

                        // if(auth()->user()->isBizappUser === 'Y' ){ // update the product in BIZAPP to sync
                        //     $this->bizappUpdateProduct($request, null, $currVariant, $productDetail, $variant['main-image'] ?? null, $userProductCategory, $variant);
                        // }

                        $currVariant->update([
                            'product_name' => $variant['name'],
                            'product_sku' => $variant['sku'],
                            'product_stock' => $stockValue,
                            'product_stock_status' => $stockStatus,
                            'product_price' => $variant['price'],
                            'product_status' => $request->productStatus == "on" ? 1 : 0,
                            'product_weight' => null,
                            'product_lhdn_code_id' => $request->productLhdnCode ?? 66,
                            'step_quantity' => $request->stepQuantity,
                            'uom_id' => $request->productUom
                        ]);

                        // Handle image upload atau existing image / Urus upload gambar atau gambar sedia ada
                        $imageFilename = null;
                        if (isset($variant['main-image']) && $variant['main-image'] instanceof UploadedFile) {
                            // User uploaded new image / Pengguna upload gambar baru
                            // $imageFilename = $this->storeProductImage($variant['main-image'], $variant['sku']);
                            $imagePath = $this->updateImageAws($variant['main-image'] ?? null, $variant['sku'], $variant['id'], $productDetail->product_attachment);

                        } elseif (isset($variant['existing_image']) && !empty($variant['existing_image'])) {
                            // Keep existing image / Kekalkan gambar sedia ada
                            // $imageFilename = $variant['existing_image'];
                            $imagePath = $variant['existing_image'];
                        } else {
                            // Keep current image if no new upload and no existing_image field
                            // $imageFilename = $productDetail->product_attachment;
                            $imagePath = $productDetail->product_attachment;
                        }

                        $productDetail->update([
                            // 'category_id' => $userProductCategory->id,
                            // category id ni kene null ke?
                            'product_attachment' => $imagePath
                        ]);

                        // update composite attribute value
                        $compositeValues = CompositeAttributeValue::where('product_variant_attribute_id', $variant['id'])->get();
                        $compositeValues->first()->update([
                            'variant_attribute_value_id' => $variant['variant1_value'],
                            'order_index' => 1
                        ]);

                        // update variant 2
                        if(isset($variant['variant2_value'])){
                            $compositeValues->last()->update([
                                'variant_attribute_value_id' => $variant['variant2_value'],
                                'order_index' => 2
                            ]);
                        }

                        // update detail kat bizapp

                        $currVariant->save();
                        $productDetail->save();

                        // Clean variant data to remove UploadedFile objects for serialization // Bersihkan data variant untuk buang objek UploadedFile untuk serialization
                        $cleanVariantData = $variant;
                        unset($cleanVariantData['main-image']); // Remove file object + buang objek fail

                        $variantBatchData[] = [
                            'action' => 2,
                            'variant' => $currVariant,
                            'requestData' => $cleanVariantData,
                            'productDetail' => $productDetail,
                            'imageFile' => $imagePath
                        ];

                        // $variantBatchData[] = [
                        //     'action' => 2,
                        //     'variant' => $currVariant,
                        //     'productDetail' => $productDetail,
                        //     'imageFile' => $imageFilename ? storage_path('app/public/products/' . $imageFilename) : null
                        // ];
                    }
                }
            }else{
                // macam ni, kalau request variant tu off. kita check terus ada variant tu tak? kalau ada, kita delete semua variant tu. kalau tak ada, kita skip.
                // check if the current product have variant or not, if have then it will delete all the variant product
                $list = ProductVariantAttribute::where('product_id', $getProduct->id)->get();
                // condition
                if($list->count() > 0){

                    foreach($list as $item){
                        // $item->delete();
                        // get product detail
                        $productDetail = ProductDetail::where('product_variant_attribute_id', $item->id)->first();
                        $productDetail->delete();
                        // delete composite attribute value
                        $compositeValues = CompositeAttributeValue::where('product_variant_attribute_id', $item->id)->get();
                        foreach($compositeValues as $item1){
                            $item1->delete();
                        }
                        // delete product attachment from s3
                        $this->deleteImageAws($productDetail->product_attachment);
                        // buat manual sebab dah buat item->delete tak jalan
                        // $item->delete();
                        $item->deleted_at = now(); // Set deleted_at to current time
                        $item->save();

                        $variantBatchData[] = [
                            'action' => 3,
                            'variant' => $item,
                            'productDetail' => $productDetail,
                            'imageFile' => null
                        ];
                        // delete product variant dari bizapp bila product yang sedia ada (as variant) tutup variant
                        // if(auth()->user()->isBizappUser === 'Y'  ){ // update the product in BIZAPP to sync
                        //     $this->deleteBizappProduct($item->product_id_bizapp);
                        // }
                    }

                    Log::info('Variant batch data delete: ' . json_encode($variantBatchData));
                }
            }
            // Collect variant data for batch processing // Kumpul data variant untuk pemprosesan batch
            // $variantBatchData = [];
            // $newVariantBatchData = [];

            // Process new variants for batch job // Proses variant baru untuk batch job
            // if($request->isVariant == "on") {
            //     foreach ($request->variants as $variant) {
            //         if (!isset($variant['id']) || empty($variant['id'])) {
            //             // New variant - add to batch processing // Variant baru - tambah ke pemprosesan batch
            //             $stockValue = $variant['stock'] == "Ready Stock" ? '-100' : $variant['stock'];
            //             $imageFilename = $this->storeProductImage($variant['main-image'] ?? null, auth()->user()->username . '_' . $variant['sku']);

            //             $variantProduct = ProductVariantAttribute::where('product_id', $getProduct->id)
            //                 ->where('product_sku', $variant['sku'])
            //                 ->first();

            //             if($variantProduct) {
            //                 $productDetail = ProductDetail::where('product_variant_attribute_id', $variantProduct->id)->first();

            //                 if(auth()->user()->isBizappUser === 'Y'){
            //                     $newVariantBatchData[] = [
            //                         'variant' => $variantProduct,
            //                         'productDetail' => $productDetail,
            //                         'imageFile' => $imageFilename ? storage_path('app/public/products/' . $imageFilename) : null
            //                     ];
            //                 }
            //             }
            //         }
            //     }
            // }

            /// if product tu ialah variant, tak perlu send ke bizapp
            if(auth()->user()->isBizappUser === 'Y' && $request->isVariant !== "on" && app()->environment('production')){ // update the product in BIZAPP to sync
                try {
                $this->bizappUpdateProduct($request, $getProduct, null, null, null, $userProductCategory, null);

                } catch (\Exception $e) {
                    Log::error('Bizapp product API error: ' . $e->getMessage());
                    return redirect()->back()->with('error', 'Error updating product in Bizapp');
                }

            }
            if ($getProduct) {

                if ($request->hasFile('main-image')) {
                    // $companyId = auth()->user()->companies->id;
                    // $userId = auth()->user()->id;
                    // $path = $request->file('main-image')->store("product/image/{$companyId}/{$getProduct->id}", 's3');
                    // $path = $this->storeImageAws($request->file('main-image'), $request->productSKU, $getProduct->id);
                    $path = $this->updateImageAws($request->file('main-image'), $request->productSKU, $getProduct->id, $getProduct->productDetail->product_attachment);
                    // Update product attachment only for main product (non-variant) // Kemaskini lampiran produk hanya untuk produk utama (bukan variant)
                    $getProduct->productDetail()
                        ->whereNull('product_variant_attribute_id')
                        ->update(['product_attachment' => $path]);
                }
                $getProduct->product_brand = $request->productBrand ?? 'TIADA BRAND';
                $getProduct->product_SKU = $request->productSKU;
                $getProduct->product_name = $request->productName;
                $getProduct->product_stock = $request->productInventory === "Ready Stock" ? '-100' : $request->productInventory;
                $getProduct->product_stock_status = $request->productInventory === "Ready Stock" ? 'Y' : 'N';
                $getProduct->product_price = $request->productPrice;
                $getProduct->product_status = $request->productStatus == "on" ? 1 : 0;
                $getProduct->product_description = $request->productDescription;
                $getProduct->productDetail()->where('product_id', $getProduct->id)->whereNull('product_variant_attribute_id')->update(['category_id' => $userProductCategory->id ?? null]);
                $getProduct->isVariant = $request->isVariant == "on" ? 'Y' : 'N';
                $getProduct->uom_id = $request->productUom;
                $getProduct->step_quantity = $request->stepQuantity;
                // $getProduct->min_quantity = $request->minQuantity ?? 1.000;
                $getProduct->product_lhdn_code_id = $request->productLhdnCode ?? 66;

                $getProduct->save();

                // dd($userProductCategory);
                // $getProduct->productDetail()->where('product_id', $getProduct->id)->whereNull('product_variant_attribute_id')->save();

                // Then update and save the product detail separately
                // $productDetail = $getProduct->productDetail;
                // if ($productDetail) {
                //     $productDetail->category_id = $userProductCategory->id;
                //     $productDetail->save();
                // }
            }
            DB::commit();

            // Dispatch batch job for new variant products // Hantar batch job untuk produk variant baharu
            if(auth()->user()->isBizappUser === 'Y' && !empty($variantBatchData) && app()->environment('production')){
                $userData = [
                    'pid' => auth()->user()->pid,
                    'domain' => auth()->user()->domain,
                    'username' => auth()->user()->username
                ];

                $requestData = [
                    'productBrand' => $request->productBrand ?? 'TIADA BRAND',
                    'category_bizapp_code' => $userProductCategory->bizapp_code ?? null
                ];

                // Dispatch batch job to queue // Hantar batch job ke queue
                BizappUpdateProductBatchJob::dispatch($userData, $variantBatchData, $requestData, $getProduct->id)
                    ->onQueue('bizapp-batch-products-update');

                Log::info('Update batch job dispatched for ' . count($variantBatchData) . ' new variant products');
            }

            return redirect()->route('products')->with('success', 'Product updated successfully');
        } catch (Exception $e) {
            Log::error('update product error : ' . $e->getMessage() . ' Stack trace: ' . $e->getTraceAsString());
            DB::rollback();
            return redirect()->back()->withError('Something is wrong, please contact admin');
        }
    }

    public function productCheckSkuForVariant(String $sku){
        $company_id = '';
        $company = auth()->user()->companies;

        if(!$company){
            $company_id = auth()->user()->employee->id;
        } else {
            $company_id = $company->id;
        }
        $productVariantAttribute = ProductVariantAttribute::where('product_SKU', $sku)
        ->whereHas('product', function ($q) use ($company_id) {
            $q->where('parent_company', $company_id);
        })
        ->orWhereHas('product', function ($q) use ($sku, $company_id) {
            $q->where('product_SKU', $sku)
            ->where('parent_company', $company_id);
        })
        ->first();

        if($productVariantAttribute){
            return Array(
                'result' => true,
                'message' => "Product SKU " . strtoupper($sku) . " already exists"
            );
        }else{
            return Array(
                'result' => false,
                'message' => 'Product SKU is available'
            );
        }
        // foreach($sku as $item){

        // }
        // return false;
    }

    private function bizappCheckSku($sku, $listSku = [], $pid = null){
        // check semua sku dalam bizapp
        // akan looping
        // kalau dah ada memang terus tendang, create sampai takde
        try{
            $urlPos = config('bizappos.bizappx_api_url');
            $getSku = Http::asForm()->post($urlPos . 'api_name=TRACK_CHECKSKUPRODUCT&pid=' . $pid, [
                'products_info' => $listSku
            ])->throw()->json();
            $totalFailedSku = 0;

            // Check response format and handle accordingly / Semak format respons dan uruskan sewajarnya
            if (is_array($getSku) && !empty($getSku)) {
                foreach ($getSku as $skuCheck) {
                    if(isset($skuCheck['result_productsku']) && $skuCheck['result_productsku'] === 'FAILED' && isset($skuCheck['result_productname']) && $skuCheck['result_productname'] === 'FAILED'){
                        $totalFailedSku++;
                        return [
                            'result' => false,
                            'sku' => $skuCheck['sku'],
                            'productname' => $skuCheck['productname'],
                            'message' => "Product SKU " . strtoupper($skuCheck['sku']) . " and Product Name " . strtoupper($skuCheck['productname']) . " already exists in BizApp"
                        ];
                    }

                    if (isset($skuCheck['result_productsku']) && $skuCheck['result_productsku'] === 'FAILED') {
                        $totalFailedSku++;
                        return [
                            'result' => false,
                            'sku' => $skuCheck['sku'],
                            'productname' => $skuCheck['productname'],
                            'message' => "Product SKU " . strtoupper($skuCheck['sku']) . " already exists in BizApp"
                        ];
                    }

                    if (isset($skuCheck['result_productname']) && $skuCheck['result_productname'] === 'FAILED') {
                        $totalFailedSku++;
                        return [
                            'result' => false,
                            'sku' => $skuCheck['sku'],
                            'productname' => $skuCheck['productname'],
                            'message' => "Product Name " . strtoupper($skuCheck['productname']) . " already exists in BizApp"
                        ];
                    }

                    // if(isset($skuCheck['result_productsku']) && $skuCheck['result_productsku'] === 'PASSED' && isset($skuCheck['result_productname']) && $skuCheck['result_productname'] === 'PASSED'){
                    //     return [
                    //         'result' => true,
                    //         'sku' => $skuCheck['sku'],
                    //         'productname' => $skuCheck['productname'],
                    //         'message' => "Create product successfully SKU: {" . $skuCheck['sku'] . "} | Product Name: {" . $skuCheck['productname'] . "}"
                    //     ];
                    // }
                }
            }

            if($totalFailedSku == 0){
                return [
                    'result' => true,
                    'message' => "All products are available"
                ];
            }

            // [
            //     {
            //         "sku": "C001AZXZA_COPY",
            //         "productname": "PRODUK 001E",
            //         "result_productsku": "FAILED",
            //         "result_productname": "PASSED"
            //     },
            //     {
            //         "sku": "W4",
            //         "productname": "001 HASHAS_COPY1",
            //         "result_productsku": "PASSED",
            //         "result_productname": "PASSED"
            //     },
            //     {
            //         "sku": "W4",
            //         "productname": "001 HASHAS_COPY",
            //         "result_productsku": "PASSED",
            //         "result_productname": "FAILED"
            //     },
            // ]
        }catch(\Exception $e){
            Log::error('BizApp API error: ' . $e->getMessage());
            return response()->json([
                'result' => false,
                'message' => "Error checking SKU in BizApp"
            ]);
        }
    }

    /**
     * Legacy method for single product processing
     * @deprecated Use BizappAddProductBatchJob for better performance
     * Kaedah lama untuk pemprosesan produk tunggal
     */
    private function bizappAddProduct(Request $request, ?Product $p = null, ?ProductVariantAttribute $pVariant = null, ?ProductDetail $prodDetails = null, ?UploadedFile $file = null){
        // loop the product
        try {
            $urlPos123 = config('bizappos.bizappos_api_url');
            $urlPos = config('bizappos.bizappos_api_url_test');
            $addProductToBizapp = Http::asForm()->post($urlPos123 . 'api_name=TRACK_ADDPRODUCT&TX=',[
                'pid' => auth()->user()->pid,
                'token' => 'aa',
                'DOMAIN' => auth()->user()->domain,
                'productname' => $pVariant->product_name ?? $request->productName,
                'sku' => $pVariant->product_sku ?? $request->productSKU,
                'price' => $pVariant->product_price ?? $request->productPrice,
                'stoktidakterhad' => $pVariant->product_stock_status ?? $request->productInventory === "Ready Stock" ? 'Y' : 'N',
                'bilstok' => $pVariant->product_stock ?? $request->productInventory === "Ready Stock" ? '-100' : $request->productInventory,
                'productcategory' => $category->bizapp_code ?? null,
                'productbrand' => $request->productBrand
            ])->throw()->json();

            // Log::info("Store product to bizapp endpoint : " . ($urlPos123 . 'api_name=TRACK_ADDPRODUCT&TX='));
            // Log::info("Store product to bizapp --stoktidakterhad : " .
            //     (($pVariant->product_stock) ?? ($request->productInventory === "Ready Stock" ? 'Y' : 'N'))
            // );
            // Log::info("Store product to bizapp --bilstok : " .
            //     (($pVariant->product_stock_status) ?? ($request->productInventory === "Ready Stock" ? 'Y' : 'N'))
            // );
            // Prepare and send the API request to Bizapp
            if($addProductToBizapp){
                if($pVariant){
                    $pVariant->product_id_bizapp = $addProductToBizapp;
                    $pVariant->save();
                }
                if($p){
                    $p->product_id_bizapp = $addProductToBizapp;
                    $p->save();
                }

                Http::asForm()->post($urlPos123 . 'api_name=TRACK_SAVE_PRODUCT_ATTACHMENT_URLONLY&TX=',[
                    'pid' => auth()->user()->pid,
                    'productid' => $addProductToBizapp,
                    'attachmenturl1' => $prodDetails->product_attachment ?? null,
                    'attachmenturl2' => $prodDetails->product_attachment_1 ?? null,
                    'attachmenturl3' => $prodDetails->product_attachment_2 ?? null,
                    'attachmenturl4' => $prodDetails->product_attachment_3 ?? null,
                ])->throw()->json();


                // if($file){
                //     Http::attach(
                //         'file', file_get_contents($file->getRealPath()),
                //         $file->getClientOriginalName()
                //     )->asMultipart()->post($urlPos123 . 'api_name=TRACK_SAVE_PRODUCT_ATTACHMENT&TX=', [
                //         'pid' => auth()->user()->pid,
                //         'TOKEN' => 'toke',
                //         'DOMAIN' => auth()->user()->domain,
                //         'productid' => $addProductToBizapp,
                //     ])->throw()->json();
                // }

                // if($file === null && $request->file('main-image')){
                //     Http::attach(
                //         'file', file_get_contents($request->file('main-image')->getRealPath()),
                //         $request->file('main-image')->getClientOriginalName()
                //     )->asMultipart()->post($urlPos123 . 'api_name=TRACK_SAVE_PRODUCT_ATTACHMENT&TX=', [
                //         'pid' => auth()->user()->pid,
                //         'TOKEN' => 'toke',
                //         'DOMAIN' => auth()->user()->domain,
                //         'productid' => $addProductToBizapp,
                //     ])->throw()->json();
                // }
                // $response as in saving attachments does not return anything, make another API call for product detail based

                $updateStockToBizapp = Http::asForm()->post($urlPos123 . 'api_name=TRACK_ADDPRODUCT_STOCK_PLUSINVENTORY&TX=',[
                    'pid' => auth()->user()->pid,
                    'domain' => auth()->user()->domain,
                    'TOKEN' => 'tke',
                    'bilstok'=> isset($pVariant->product_stock) ? $pVariant->product_stock : ($request->productInventory === "Ready Stock" ? '-100' : $request->productInventory),
                    'bilstokalert' => '0',
                    'stoktidakterhad' => isset($pVariant->product_stock_status) ? $pVariant->product_stock_status : ($request->productInventory === "Ready Stock" ? 'Y' : 'N'),
                    'productname' => isset($pVariant->product_name) ? $pVariant->product_name : $request->productName,
                ])->throw()->json();

                Http::asForm()->post($urlPos123 . 'api_name=TRACK_SHOWPRODUCT_DONTSILENCE&TX=',[
                    'pid' => auth()->user()->pid,
                    'domain' => auth()->user()->domain,
                    'TOKEN' => 'tke',
                    'productid' => $addProductToBizapp,
                ])->throw()->json();

                // Log::info("test add baru", array(
                //     'pid' => auth()->user()->pid,
                //     'domain' => auth()->user()->domain,
                //     'TOKEN' => 'tke',
                //     'bilstok'=> isset($pVariant->product_stock) ? $pVariant->product_stock : ($request->productInventory === "Ready Stock" ? '-100' : $request->productInventory),
                //     'bilstokalert' => '0',
                //     'stoktidakterhad' => isset($pVariant->product_stock_status) ? $pVariant->product_stock_status : ($request->productInventory === "Ready Stock" ? 'Y' : 'N'),
                //     'productname' => isset($pVariant->product_name) ? $pVariant->product_name : $request->productName,
                // ));


                /// supposedly tk pakai dah code ni
                // if($pVariant){
                //     $updateStockToBizapp = Http::asForm()->post($urlPos123 . 'TRACK_ADDPRODUCT_STOCK_PLUSINVENTORY&TX=',[
                //         'pid' => auth()->user()->pid,
                //         'domain' => auth()->user()->domain,
                //         'TOKEN' => 'tke',
                //         'bilstok'=> $pVariant->product_stock ?? $request->productInventory === "Ready Stock" ? '-100' : $request->productInventory,
                //         'bilstokalert' => '0',
                //         'stoktidakterhad' => $pVariant->product_stock_status ?? $request->productInventory === "Ready Stock" ? 'Y' : 'N',
                //         'productname' => $request->productName,
                //     ])->throw()->json();
                // }

                // else{

                //     $updateStockToBizapp = Http::asForm()->post($urlPos123 . 'TRACK_ADDPRODUCT_STOCK_PLUSINVENTORY&TX=',[
                //         'pid' => auth()->user()->pid,
                //         'domain' => auth()->user()->domain,
                //         'TOKEN' => 'tke',
                //         'bilstok'=> $request->productInventory === "Ready Stock" ? '-100' : $request->productInventory,
                //         'bilstokalert' => '0',
                //         'stoktidakterhad' => $request->productInventory === "Ready Stock" ? 'Y' : 'N',
                //         'productname' => $request->productName,
                //     ])->throw()->json();
                // //     if($pVariant->product_stock_status === 'Y'){
                // //         Http::asForm()->post($urlPos123 . 'TRACK_READYSTOCKINVENTORI&TX=',[
                // //             'pid' => auth()->user()->pid,
                // //             'domain' => auth()->user()->domain,
                // //             'TOKEN' => 'tke',
                // //             'bilstok'=> '0',
                // //             'nota' => 'Update stock count in bizappos back-office',
                // //             'balance' => '0',
                // //             'productid' => $addProductToBizapp,
                // //         ])->throw()->json();
                // //     }else{
                // //         Http::asForm()->post($urlPos123 . 'api_name=TRACK_ADDINVENTORI&TX=',[
                // //             'pid' => auth()->user()->pid,
                // //             'domain' => auth()->user()->domain,
                // //             'TOKEN' => 'tke',
                // //             'bilstok'=> $pVariant->product_stock,
                // //             'nota' => 'Update stock count in bizappos back-office',
                // //             'balance' => $pVariant->product_stock,
                // //             'productid' => $addProductToBizapp,
                // //         ])->throw()->json();
                // //     }
                // // }else{
                // //     if($request->productInventory === "Ready Stock"){
                // //         Http::asForm()->post($urlPos123 . 'TRACK_READYSTOCKINVENTORI&TX=',[
                // //             'pid' => auth()->user()->pid,
                // //             'domain' => auth()->user()->domain,
                // //             'TOKEN' => 'tke',
                // //             'bilstok'=> '0',
                // //             'nota' => 'Update stock count in bizappos back-office',
                // //             'balance' => '0',
                // //             'productid' => $addProductToBizapp,
                // //         ])->throw()->json();
                // //     }else{
                // //         Http::asForm()->post($urlPos123 . 'api_name=TRACK_ADDINVENTORI&TX=',[
                // //             'pid' => auth()->user()->pid,
                // //             'domain' => auth()->user()->domain,
                // //             'TOKEN' => 'tke',
                // //             'bilstok'=> $request->productInventory === "Ready Stock" ? '-100' : $request->productInventory,
                // //             'nota' => 'Update stock count in bizappos back-office',
                // //             'balance' => $request->productInventory === "Ready Stock" ? '-100' : $request->productInventory,
                // //             'productid' => $addProductToBizapp,
                // //         ])->throw()->json();
                // //     }
                // }

                // $getDetailFromBizapp = Http::asForm()->post($urlPos . 'api_name=TRACK_GET_PRODUCT_INFO',[
                //     'pid' => auth()->user()->pid,
                //     'token' => 'aa',
                //     'DOMAIN' => auth()->user()->domain,
                //     'productid' => $addProductToBizapp
                // ])->throw()->json();

                // if(!empty($getDetailFromBizapp) && $getDetailFromBizapp[0]['STATUS'] === '1'){
                //     $prodDetails->product_attachment = $getDetailFromBizapp[0]['attachment'];
                //     $prodDetails->save();
                // }

                Log::info('siap:' . now());
            }

        } catch (\Exception $e) {
            Log::error('BizApp add product API error: ' . $e->getMessage());
            // return redirect()->back()->with('error', 'Error updating product in BizApp');
        }

    }

    private function bizappUpdateProduct(Request $request, ?Product $getProduct = null, ?ProductVariantAttribute $pVariant = null, ?ProductDetail $prodDetails = null, ?UploadedFile $file = null, ?ProductsCategory $userProductCategory = null, ?array $requestVariant = null){
        // loop the product
        try {
            $urlPos = config('bizappos.bizappos_api_url');
            $urlPos123 = config('bizappos.bizappos_api_url_test');

            $updateProductToBizapp = Http::asForm()->post($urlPos . 'api_name=TRACK_UPDATEPRODUCT_IGNORESTOCK&TX=', [
                'pid' => auth()->user()->pid,
                'token' => 'aa',
                'DOMAIN' => auth()->user()->domain,
                'productid' => $pVariant->product_id_bizapp ?? $getProduct->product_id_bizapp,
                'new_productname' => $requestVariant['name'] ?? $request->productName,
                'new_productsku' => $requestVariant['sku'] ?? $request->productSKU,
                'new_sellingprice' => $requestVariant['price'] ?? $request->productPrice,
                'statusstokX' => ($requestVariant['stock'] ?? $request->productInventory) === "Ready Stock" ? 'Y' : 'N',
                'bilstokX' => ($requestVariant['stock'] ?? $request->productInventory) === "Ready Stock" ? '-100' : ($requestVariant['stock'] ?? $request->productInventory),
                'new_productcategory' => $userProductCategory->bizapp_code,
                'new_productbrand' => $request->productBrand
            ])->throw()->json();
            if ($updateProductToBizapp) {

                if($pVariant){
                    $pVariant->product_id_bizapp = $updateProductToBizapp;
                    $pVariant->save();
                }
                if($getProduct){
                    $getProduct->product_id_bizapp = $updateProductToBizapp;
                    $getProduct->save();
                }

                if($file){
                    Http::attach(
                        'file', file_get_contents($file->getRealPath()),
                        $file->getClientOriginalName()
                    )->asMultipart()->post($urlPos . 'api_name=TRACK_SAVE_PRODUCT_ATTACHMENT&TX=', [
                        'pid' => auth()->user()->pid,
                        'TOKEN' => 'toke',
                        'DOMAIN' => auth()->user()->domain,
                        'productid' => $updateProductToBizapp,
                    ])->throw()->json();
                }

                if($file === null && $request->file('main-image')){
                    Http::attach(
                        'file', file_get_contents($request->file('main-image')->getRealPath()),
                        $request->file('main-image')->getClientOriginalName()
                    )->asMultipart()->post($urlPos . 'api_name=TRACK_SAVE_PRODUCT_ATTACHMENT&TX=', [
                        'pid' => auth()->user()->pid,
                        'TOKEN' => 'toke',
                        'DOMAIN' => auth()->user()->domain,
                        'productid' => $updateProductToBizapp,
                    ])->throw()->json();
                }

                if($requestVariant){
                    $curStock = $pVariant->product_stock == -100 ? 0 : $pVariant->product_stock;
                    // Log::info("data", [
                    //     'id' => $updateProductToBizapp,
                    //     'request_quantity' => $requestVariant['stock'],
                    //     'current_quantity' => $curStock,
                    // ]);
                    if($requestVariant['stock'] === 'Ready Stock'){
                        try {
                            Http::asForm()->post($urlPos . 'api_name=TRACK_READYSTOCKINVENTORI&TX=', [
                                'pid' => auth()->user()->pid,
                                'domain' => auth()->user()->domain,
                                'TOKEN' => 'tke',
                                'bilstok' => '',
                                'nota' => 'Update stock count in bizappos back-office',
                                'balance' => '0',
                                'productid' => $updateProductToBizapp,
                            ])->throw()->json();
                            Log::info("Update product to bizapp for : $updateProductToBizapp , statusstokX = Ready stock");
                        } catch (\Exception $e) {
                            Log::error("Update Ready Stock failed for $updateProductToBizapp: " . $e->getMessage());
                        }
                    }elseif ($requestVariant['stock'] !== "Ready Stock" && (int)$requestVariant['stock'] > (int)$curStock) {
                        Http::asForm()->post($urlPos123 . 'api_name=TRACK_ADDINVENTORI&TX=',[
                            'pid' => auth()->user()->pid,
                            'domain' => auth()->user()->domain,
                            'TOKEN' => 'tke',
                            'bilstok'=> (int)$requestVariant['stock'] - (int)$curStock,
                            'nota' => 'Update stock count in bizappos back-office',
                            'balance' => $curStock,
                            'productid' => $updateProductToBizapp,
                        ])->throw()->json();
                    }elseif ($requestVariant['stock'] !== "Ready Stock" && (int)$requestVariant['stock'] < (int)$curStock) {
                        Http::asForm()->post($urlPos123 . 'api_name=TRACK_MINUSINVENTORI&TX=',[
                            'pid' => auth()->user()->pid,
                            'domain' => auth()->user()->domain,
                            'TOKEN' => 'tke',
                            'bilstok'=> (int)$curStock - (int)$requestVariant['stock'],
                            'nota' => 'Update stock count in bizappos back-office',
                            'balance' => $curStock,
                            'productid' => $updateProductToBizapp,
                        ])->throw()->json();
                    }else {
                        Log::info("Product Update for variant $updateProductToBizapp : $requestVariant[stock]");
                    }
                }else{
                    $curStock = $getProduct->product_stock == -100 ? 0 : $getProduct->product_stock;
                    if ($request->productInventory === "Ready Stock") {
                        try {
                            $updateStockToBizapp = Http::asForm()->post($urlPos . 'api_name=TRACK_READYSTOCKINVENTORI&TX=', [
                                'pid' => auth()->user()->pid,
                                'domain' => auth()->user()->domain,
                                'TOKEN' => 'tke',
                                'bilstok' => '',
                                'nota' => 'Update stock count in bizappos back-office',
                                'balance' => '0',
                                'productid' => $updateProductToBizapp,
                            ])->throw()->json();
                            Log::info("Update product to bizapp for : $updateProductToBizapp , statusstokX = Ready stock");
                        } catch (\Exception $e) {
                            Log::error("Update Ready Stock failed for $updateProductToBizapp: " . $e->getMessage());
                        }
                    } else if ($request->productInventory !== "Ready Stock" && (int)$request->productInventory > $curStock) {
                        try {
                            $updateStockToBizapp = Http::asForm()->post($urlPos . 'api_name=TRACK_ADDINVENTORI&TX=', [
                                'pid' => auth()->user()->pid,
                                'domain' => auth()->user()->domain,
                                'TOKEN' => 'tke',
                                'bilstok' => (int)$request->productInventory - (int)$curStock,
                                'nota' => 'Update stock count in bizappos back-office',
                                'balance' => $curStock,
                                'productid' => $updateProductToBizapp,
                            ])->throw()->json();
                            Log::info("Update product to bizapp for : $updateProductToBizapp , statusstokX = " . (int)$request->productInventory - $curStock);
                        } catch (\Exception $e) {
                            Log::error("Update Add Inventory failed for $updateProductToBizapp: " . $e->getMessage());
                        }
                    } else if ($request->productInventory !== "Ready Stock" && (int)$request->productInventory < $curStock) {
                        try {
                            $updateStockToBizapp = Http::asForm()->post($urlPos123 . 'api_name=TRACK_MINUSINVENTORI&TX=', [
                                'pid' => auth()->user()->pid,
                                'domain' => auth()->user()->domain,
                                'TOKEN' => 'tke',
                                'bilstok' => (int)$curStock - (int)$request->productInventory,
                                'nota' => 'Update stock count in bizappos back-office',
                                'balance' => $curStock,
                                'productid' => $updateProductToBizapp,
                            ])->throw()->json();
                            Log::info("Update product to bizapp for : $updateProductToBizapp , statusstokX = " .  (int)$curStock - (int)$request->productInventory);
                        } catch (\Exception $e) {
                            Log::error("Update Minus Inventory failed for $updateProductToBizapp: " . $e->getMessage());
                        }
                    } else {
                        Log::info("Product Update for $updateProductToBizapp : $request->productInventory");
                    }
                }

                $getDetailFromBizapp = Http::asForm()->post($urlPos . 'api_name=TRACK_GET_PRODUCT_INFO', [
                    'pid' => auth()->user()->pid,
                    'token' => 'aa',
                    'DOMAIN' => auth()->user()->domain,
                    'productid' => $updateProductToBizapp
                ])->throw()->json();

                if (!empty($getDetailFromBizapp) && $getDetailFromBizapp[0]['STATUS'] === '1') {
                    // $getProduct->productDetail->product_attachment = $getDetailFromBizapp[0]['attachment'];
                    // $getProduct->productDetail->save();

                    // if($pVariant) {
                    //     $prodDetails->product_attachment = $getDetailFromBizapp[0]['attachment'];
                    //     // $pVariant->save();
                    // }else{
                    //     $getProduct->productDetail->product_attachment = $getDetailFromBizapp[0]['attachment'];
                    //     // $getProduct->productDetail->save();
                    // }
                }
            }
            if (($requestVariant['stock'] ?? $request->productInventory) != 'Ready Stock') {
                $product_stock = (int) $getProduct->product_stock;
                // Calculate remain, ensuring it does not go negative // Kira baki, pastikan tidak negatif
                $remain = ($requestVariant['stock'] ?? $request->productInventory);
                LogStock::create([
                    'company_id' => $getProduct->parent_company ?? '',
                    'user_id' => auth()->user()->id,
                    'product_id' => $pVariant->product_id ?? $getProduct->id,
                    'name' => auth()->user()->username,
                    'product_name' => $pVariant->product_name ?? $getProduct->product_name,
                    'product_sku' => $pVariant->product_sku ??$getProduct->product_SKU ?? "",
                    'quantity' => $request->productInventory,
                    'remain' => $remain,
                    'note' => 'Backoffice update',
                ]);
            }
        } catch (\Exception $e) {
            Log::error('BizApp add product API error: ' . $e->getMessage());
            // return redirect()->back()->with('error', 'Error updating product in BizApp');
        }

    }

    /**
     * Store uploaded file and return filename // Simpan fail upload dan kembalikan nama fail
     */
    private function storeProductImage($file, $sku)
    {
        if (!$file || !($file instanceof UploadedFile)) {
            return null;
        }

        $timestamp = now()->format('YmdHis');
        $filename = $timestamp . '_' . $sku . '_' . $file->getClientOriginalName();

        // Store file in storage/app/public/products directory // Simpan fail dalam direktori storage/app/public/products
        $file->storeAs('products', $filename, 'public');

        return $filename;
    }

    private function storeImageAws($file, $sku, $productId)
    {
        try{
            if (!$file || !($file instanceof UploadedFile)) {
                return null;
            }

            $companyId = auth()->user()->companies->id;
            $userId = auth()->user()->id;
            $path = $file->store("product/image/{$companyId}/{$productId}", 's3');

            // Generate S3 URL manually // Jana URL S3 secara manual
            $s3BaseUrl = config('filesystems.disks.s3.url') ?? 'https://bizappos.s3.ap-southeast-1.amazonaws.com/';
            $s3Url = rtrim($s3BaseUrl, '/') . '/' . $path;

            return $s3Url;
        }catch(Exception $e){
            Log::error('Error storing image: ' . $e->getMessage());
            return null;
        }
    }

    private function updateImageAws($file, $sku, $productId, $oldImagePath = null)
    {
        try{
            if (!$file || !($file instanceof UploadedFile)) {
                return $oldImagePath; // Return old path if no new file // Kembalikan path lama jika tiada fail baru
            }

            // Extract S3 path from full URL for deletion // Ekstrak path S3 dari URL penuh untuk penghapusan
            // if ($oldImagePath) {
            //     // Remove the S3 base URL to get the relative path // Buang URL asas S3 untuk dapatkan path relatif
            //     $s3BaseUrl = 'https://bizappos.s3.ap-southeast-1.amazonaws.com/';
            //     $relativePath = str_replace($s3BaseUrl, '', $oldImagePath);

            //     if (Storage::disk('s3')->exists($relativePath)) {
            //         Storage::disk('s3')->delete($relativePath);
            //     }
            // }
            $this->deleteImageAws($oldImagePath);

            // Store new image in S3 // Simpan gambar baru dalam S3
            $s3Url = $this->storeImageAws($file, $sku, $productId);

            return $s3Url ?? null; // Return S3 URL if available, otherwise return null // Kembalikan URL S3 jika ada, jika tidak kembalikan null
        }catch(Exception $e){
            Log::error('Error updating image: ' . $e->getMessage());
            return $oldImagePath; // Return old path on error // Kembalikan path lama jika berlaku ralat
        }
    }

    private function deleteImageAws($oldImagePath = null)
    {
        if ($oldImagePath) {
            // Remove the S3 base URL to get the relative path // Buang URL asas S3 untuk dapatkan path relatif
            $s3BaseUrl = 'https://bizappos.s3.ap-southeast-1.amazonaws.com/';
            $relativePath = str_replace($s3BaseUrl, '', $oldImagePath);

            if (Storage::disk('s3')->exists($relativePath)) {
                Storage::disk('s3')->delete($relativePath);
            }
        }
    }

}
