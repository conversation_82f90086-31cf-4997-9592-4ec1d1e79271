<?php

namespace App\Http\Controllers\backend;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\CompanyLhdn;
use App\Models\CompanyLhdnToken;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\Http;
use App\Models\Order;
use App\Models\OrderDetail;
use Carbon\Carbon;
use Illuminate\Support\Facades\Hash;
use App\Models\OrderLhdn;
use App\Models\Postcode;
use App\Models\State;
use App\Models\LhdnLink;
use App\Models\LhdnMsicCode;
class LHDNController extends Controller
{
    private $stagingUrl = "https://preprod-api.myinvois.hasil.gov.my";
    private $stagingUrlNonApis = "https://preprod.myinvois.hasil.gov.my";
    private $prodUrl = "";
    // index
    public function index()
    {
        // check if the lhdn user already exists or not
        // dd(auth()->user()->userDetails);
        $exist = CompanyLhdn::where('company_id', auth()->user()->companies->id)->exists();
        $com = [];
        $msicCodes = LhdnMsicCode::all();
        $states = [
            '1' => 'Johor',
            '2' => 'Kedah',
            '3' => 'Kelantan',
            '4' => 'Melaka',
            '5' => 'Negeri Sembilan',
            '6' => 'Pahang',
            '7' => 'Penang',
            '8' => 'Perak',
            '9' => 'Perlis',
            '10' => 'Selangor',
            '11' => 'Terengganu',
            '12' => 'Sabah',
            '13' => 'Sarawak',
            '14' => 'Wilayah Persekutuan Kuala Lumpur',
            '15' => 'Wilayah Persekutuan Labuan',
            '16' => 'Wilayah Persekutuan Putrajaya'
        ];

        if (!$exist) {
            // If not exists, gunakan info dari table company
            $company = auth()->user()->companies;

            $addressParts = array_filter([
                $company->com_address ?? '',
                $company->com_city ?? '',
                $company->com_postcode ?? '',
                $states[$company->com_state] ?? '',
                $company->com_country ?? '',
            ]);

            $concatAddress =
            ($company->com_address ?? '') . ', ' .
            ($company->com_city ?? '') . ', ' .
            ($company->com_postcode ?? '') . ', ' .
            ($states[$company->com_state] ?? '') . ', ' .
            ($company->com_country ?? '');

            $com = [
                'company_id' => $company->id ?? '',
                'company_name' => $company->com_name ?? '',
                'company_address' => $concatAddress ?? '',
                'company_phone' => $company->com_mobile ?? '',
                'company_email' => $company->com_email ?? '',
                'company_sst_no' => $company->com_sst_number ?? '',
                'company_sst_percent' => $company->com_sst_value ?? '',
                'company_ssm_no' => $company->com_registration_no ?? '',
                'name' => '', // still hardcoded as empty
                'ic_no' => '',
                'client_id' => '',
                'client_secret_id_1' => '',
                'client_secret_id_2' => '',
                'business_type' => 'individual',
                'tourism_tax_no' => '',
                'tin_no' => '',
                'postcode' => '',
                'msic_code' => '',
            ];
            return view('backend.integration.lhdn.index') ->with('com', $com) ->with('msicCodes', $msicCodes);
        }else{
            $data = CompanyLhdn::where('company_id', auth()->user()->companies->id)->first();

            $com = [
                'company_id' => $data->company_id ?? '',
                'company_name' => $data->ssm_name ?? '',
                'company_address' => $data->address ?? '',
                'company_phone' => $data->phone ?? '',
                'company_email' => $data->email ?? '',
                'company_sst_no' => $data->sst_no ?? '',
                'company_sst_percent' => $data->sst_value ?? '',
                'company_ssm_no' => $data->ssm_no ?? '',
                'name' => $data->name ?? '',
                'ic_no' => $data->ic_no ?? '',
                'client_id' => $data->client_id ?? '',
                'client_secret_id_1' => $data->client_secret_id_1 ?? '',
                'client_secret_id_2' => $data->client_secret_id_2 ?? '',
                'business_type' => $data->business_type ?? '',
                'tourism_tax_no' => $data->tourism_tax_no ?? '',
                'tin_no' => $data->tin_no ?? '',
                'postcode' => $data->postcode ?? '',
                'msic_code' => $data->msic_code ?? '',
            ];
            return view('backend.integration.lhdn.index') ->with('com', $com) ->with('msicCodes', $msicCodes);
        }
    }

    // show
    public function show()
    {
        // Logic to show the form for creating a new LHDN record
        return view('backend.integration.lhdn.create');
    }

    // store
    public function store(Request $request)
    {
        // dd($request->all());
        // Logic to store a new LHDN record
        // Validate and save the data
        // Redirect or return a response

        try{
            DB::beginTransaction();
            $request->validate([
                'full_name' => 'nullable|string|max:500',
                'ic_no' => 'nullable|string|max:255',
                'ssm_name' => 'nullable|string|max:500',
                'ssm_no' => 'nullable|string|max:150',
                'address' => 'required|string|max:1000',
                'email' => 'required|email|max:100',
                'phoneno' => 'required|numeric|min:0',
                'sst_no' => 'nullable|string|max:100',
                'sst_percent' => 'nullable|string|max:5',
                'tourism_tax_no' => 'nullable|string|max:255',
                'client_id' => 'nullable|string|max:100',
                'client_secret_id_1' => 'nullable|string|max:100',
                'client_secret_id_2' => 'nullable|string|max:100',
                'business_type' => 'required|string|in:individual,business',
                'tin_no' => 'required|string|max:50',
                'msic_code' => 'required|string|max:100',
            ]);

            // if request->ic_no is not null, buang semua bukan nombor
            $cleanedIcNo = null;
            if($request->ic_no !== null && $request->ic_no !== ''){
                // Buang semua bukan nombor dari ic_no
                $cleanedIcNo = preg_replace('/\D/', '', $request->ic_no); // buang semua bukan nombor
            }

            // if ($validator->fails()) {
            //     Log::error('Update user info : ' . json_encode($validator->errors()));
            //     return redirect()->back()->withErrors($validator)->withInput();
            // }

            // check if the lhdn user already exists or not
            $exist = CompanyLhdn::where('company_id', auth()->user()->companies->id)->first();

            if($exist){
                // update the existing record
                $exist->update([
                    'name' => $request->full_name,
                    'ic_no' => $cleanedIcNo,
                    'ssm_name' => $request->ssm_name,
                    'ssm_no' => $request->ssm_no,
                    'address' => $request->address,
                    'email' => $request->email,
                    'phone' => $request->phoneno,
                    'sst_no' => $request->sst_no,
                    'sst_value' => $request->sst_percent,
                    'tourism_tax_no' => $request->tourism_tax_no,
                    'client_id' => $request->client_id,
                    'client_secret_id_1' => $request->client_secret_id_1,
                    'client_secret_id_2' => $request->client_secret_id_2,
                    'business_type' => $request->business_type,
                    'tin_no' => $request->tin_no,
                    'postcode' => $request->postcode,
                    'msic_code' => $request -> msic_code,
                ]);
            }else{
                // create a new record
                CompanyLhdn::create([
                    'company_id' => auth()->user()->companies->id,
                    'name' => $request->full_name,
                    'ic_no' => $cleanedIcNo,
                    'ssm_name' => $request->ssm_name,
                    'ssm_no' => $request->ssm_no,
                    'address' => $request->address,
                    'email' => $request->email,
                    'phone' => $request->phoneno,
                    'sst_no' => $request->sst_no,
                    'sst_value' => $request->sst_percent,
                    'tourism_tax_no' => $request->tourism_tax_no,
                    'client_id' => $request->client_id,
                    'client_secret_id_1' => $request->client_secret_id_1,
                    'client_secret_id_2' => $request->client_secret_id_2,
                    'business_type' => $request->business_type,
                    'tin_no' => $request->tin_no,
                    'postcode' => $request -> postcode,
                    'msic_code' => $request -> msic_code,
                ]);
            }
            $this->loginTaxpayer();
            DB::commit();
            return redirect()->route('lhdn.index')->with('success', 'LHDN record created successfully.');
        }catch (ValidationException $e) {
            DB::rollBack();
            // ✅ Log semua error
            Log::error('Validation failed: ', $e->errors());

            // ✅ Biar Laravel handle redirect + withErrors + old input
            throw $e;

        }catch(Exception $e){
            DB::rollBack();
            Log::error('Failed to create LHDN record: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to create LHDN record: ' . $e->getMessage());

        }
    }

    // update
    public function update(Request $request, $id)
    {
        // Logic to update an existing LHDN record
        // Validate and update the data
        // Redirect or return a response
        return redirect()->route('lhdn.index')->with('success', 'LHDN record updated successfully.');
    }
    // delete
    public function destroy($id)
    {
        // Logic to delete an existing LHDN record
        // Find the record and delete it
        return redirect()->route('lhdn.index')->with('success', 'LHDN record deleted successfully.');
    }

    // edit
    public function edit($id)
    {
        // Logic to show the form for editing an existing LHDN record
        // Retrieve the record by ID
        return view('backend.lhdn.edit', compact('id'));
    }

    public function submitLhdn($id, $customer = []){
        // $id ni ialah id order
        // dd($id);

        // cari order dulu
        try{
            DB::beginTransaction();
            $order = Order::findOrFail($id);

            // dd($order);
            $payload = [];
            $invoice_id = "BZPOS-{$order->bizapp_temp_id}";

            // check if order already has lhdn submission
            $orderLhdn = OrderLhdn::where('order_id', $id)->first();
            if($orderLhdn){
                return ([
                    'success' => false,
                    'data' => 'LHDN record already exists.'
                ]);
                // return redirect()->route('lhdn.index')->with('error', 'LHDN record already exists.');
            }

            if($order){
                $result = $this->singleJson($id, $order, $invoice_id, $customer);
                if($result['success']){
                    $payload = $result['data'];

                    // dd(json_encode($payload));
                }
            }

            // dd($payload);

            $base64DOC = base64_encode(json_encode($payload));              // Encode to Base64
            $base64DOCHASH = hash('sha256', json_encode($payload));

            // dd($base64DOCHASH);
            $token = $this->checkToken(null, $order->company->lhdn);
            // dd([
            //     'token' => $token['access_token'],
            //     'base64DOC' => $base64DOC,
            //     'base64DOCHASH' => $base64DOCHASH,
            //     'payload' => json_encode($payload)
            // ]);

            if($token['success'] && !empty($payload) && $payload !== null){
                // dd($token);
                $response = Http::withToken($token['access_token']) // example: 'eyJ0eXAiOiJKV1Qi...'
                ->withHeaders([
                    'Content-Type' => 'application/json'
                ])->post($this->stagingUrl . '/api/v1.0/documentsubmissions', [
                    'documents' => [
                        [
                            'format' => 'JSON',
                            'documentHash' => $base64DOCHASH,
                            'codeNumber' => $invoice_id,
                            'document' => $base64DOC,
                        ]
                    ]
                ]);

                // dd($response->json());

                // find company lhdn id from hash
                // $lhdnLink = LhdnLink::where('qr_id', $customer['hash'])->first();
                // $companyLhdn = null;
                // if($lhdnLink){
                //     $companyLhdn = $lhdnLink;
                // }

                // check response if response code is 200
                if($response->successful()){
                    // success
                    $responseData = $response->json();
                    // check if responseData has 'uuid' key
                    if(isset($responseData['submissionUid'])){
                        $uuid = $responseData['acceptedDocuments'][0]['uuid'] ?? null;
                        $codeNumber = $responseData['acceptedDocuments'][0]['invoiceCodeNumber'] ?? null;

                        // invoice type: HQ-CUSTOMER
                        // store to db bila ada response dalma order_lhdn
                        $orderLhdn = OrderLhdn::create([
                            'order_id' => $id,
                            // 'response' => json_encode($response->json()),
                            'company_lhdn_id' => $order->company->lhdn->id,
                            'invoice_type' => 'HQ-CUSTOMER', // hardcoded for now
                            'uuid' => $uuid,
                            'submission_uuid' => $responseData['submissionUid'] ?? null,
                            'status' => 'in progress',
                            'code_number' => $invoice_id,
                            'from_tin' => $order->company->lhdn->tin_no,
                            'to_tin' => $customer['tin_no'] ?? null,
                            'transaction_date' => now(),
                            'to_lhdn_type' => $customer['classification'] ?? null, // NRIC or BRN
                            'to_ic_ssm' => $customer['ic_no'] ?? null, // IC NO or SSM NO
                            'to_email' => $customer['email'] ?? null,
                            'lhdn_server' => 'staging', // staging or production
                            'batch' => 0, // 0 for single submission
                            'cancel_date' => null,
                            'cancel_error_code' => null, // get error code when not 200
                            'cancel_error_message' => null, // get error message when not 200get error details when not 200


                        ]);

                        DB::commit();

                        return ([
                            'success' => true,
                            'data' => $orderLhdn
                        ]);
                    }else{
                        throw new Exception('UUID not found in response');
                    }
                }
                // else{
                //     // {
                //     //     "error": {
                //     //         "code": "ValidationError",
                //     //         "message": null,
                //     //         "target": null,
                //     //         "details": [
                //     //             {
                //     //                 "code": "submission",
                //     //                 "target": "submission",
                //     //                 "message": "The authenticated TIN and documents TIN is not matching "
                //     //             }
                //     //         ]
                //     //     }
                //     // }
                //     $responseData = $response->json();

                //     OrderLhdn::create([
                //         'order_id' => $id,
                //         // 'response' => json_encode($response->json()),
                //         'company_lhdn_id' => auth()->user()->companies->lhdn->id,
                //         'invoice_type' => 'HQ-CUSTOMER', // hardcoded for now
                //         // 'uuid' => $uuid,
                //         'submission_uuid' => $responseData['submissionUid'] ?? null,
                //         'status' => 'submitted',
                //         'code_number' => $invoice_id ?? null,
                //         'from_tin' => 'from tin',
                //         'to_tin' => 'to tin',
                //         'transaction_date' => now(),
                //         'to_lhdn_type' => 'NRIC', // NRIC or BRN
                //         'to_ic_ssm' => 'IC NO', // IC NO or SSM NO
                //         'to_email' => 'customer email',
                //         'lhdn_server' => 'staging', // staging or production
                //         'batch' => 0, // 0 for single submission
                //         'cancel_date' => now(),
                //         'cancel_error_code' => $responseData['error']['code'] ?? null, // get error code when not 200
                //         'cancel_error_message' => $responseData['error']['details'][0]['message'] ?? null, // get error message when not 200get error details when not 200


                //     ]);
                // }




            }

            // $response = Http::withHeaders([
            //     'Content-Type' => 'application/json',
            //     "Authorization: Bearer {$access_token}"
            // ])->post($this->stagingUrl . '/api/v1.0/documentsubmissions', [
            //     'documents' => [
            //         [
            //             'format' => 'JSON',
            //             'documentHash' => $base64DOCHASH,
            //             'codeNumber' => $invoice_id,
            //             'document' => $base64DOC, // Your base64-encoded content
            //         ]
            //     ]
            // ])->throw();

            // dd($response);
            // DB::commit();

            // return ([
            //     'success' => true,
            //     'data' => [
            //         'uuid' => $responseData['acceptedDocuments'][0]['uuid'] ?? null,
            //         'submission_uuid' => $responseData['acceptedDocuments'][0]['uuid'] ?? null,
            //         'status' => 'submitted',
            //         // 'code_number' => $invoice_id,
            //     ]
            // ]);

        }catch(Exception $e){
            // error
            DB::rollBack();
            Log::error("something went wrong during submitlhdn: {$e}");
            return ([
                'success' => false,
                'data' => 'Error during submit LHDN'
            ]);
        }

        // cari order details untuk cari product tu

        // then try submit

    }

    /// login
    public function loginTaxpayer(?CompanyLhdn $lhdn = null){
        try {
            DB::beginTransaction();

            if(!$lhdn){
                $company = auth()->user()?->companies;
                $lhdnDetails = $company?->lhdn;
            }else{
                $lhdnDetails = $lhdn;
            }

            $lhdnToken = $lhdnDetails?->companylhdntoken;

            if (!$lhdnDetails) {
                throw new Exception('LHDN details not found.');
            }

            $response = Http::withHeaders([
                'onbehalfof' => 'IG5494016080',
            ])
            ->asForm()
            ->post($this->stagingUrl . '/connect/token', [
                'client_id' => $lhdnDetails->client_id,
                'client_secret' => $lhdnDetails->client_secret_id_1,
                'grant_type' => 'client_credentials',
                'scope'=> 'InvoicingAPI',
            ])->throw();

            // $response = Http::asForm()->post($this->stagingUrl . '/connect/token', [
            //     'client_id' => $lhdnDetails->client_id,
            //     'client_secret' => $lhdnDetails->client_secret_id_1,
            //     'grant_type' => 'client_credentials',
            //     'scope'=> 'InvoicingAPI',
            // ])->throw();

            $data = $response->json();

            if (!isset($data['access_token'], $data['expires_in'])) {
                throw new Exception('Invalid token response: ' . json_encode($data));
            }

            // $data['expires_in'] = 3600. add 3600 from current timestamp
            $tokenExpire = now()->addSeconds($data['expires_in']); // e.g. 3600 seconds from now

            if(!$lhdnToken){
                CompanyLhdnToken::create([
                    'company_lhdn_id' => $lhdnDetails->id,
                    'access_token' => $data['access_token'] ?? '',
                    'date_generated' => now(),
                    'expires_in' => $data['expires_in'] ?? '',
                    'scope' => $data['scope'] ?? '',
                    'expires_date' => $tokenExpire,
                    'token_type' => $data['token_type']
                ]);
            }else{
                $lhdnToken->update([
                    'access_token' => $data['access_token'] ?? '',
                    'date_generated' => now(),
                    'expires_in' => $data['expires_in'] ?? '',
                    'scope' => $data['scope'] ?? '',
                    'expires_date' => $tokenExpire,
                    'token_type' => $data['token_type']
                ]);
            }

            DB::commit();
            return ([
                'success' => true,
            ]);
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('Error during login taxpayer: ' . $e->getMessage());
            return ([
                'success' => false,
            ]);
        }
    }

    /// validate tin number, true kalau dapat return 200 without any response
    /// idType: NRIC, PASSPORT, BRN, ARMY
    public function validateTin($idType, $idValue){
        try {
            $company = auth()->user()?->companies;
            $lhdnDetails = $company?->lhdn;

            if (!$lhdnDetails) {
                throw new Exception('LHDN details not found.');
            }

            $response = Http::withToken($lhdnDetails->accesstoken)
            ->get("{$this->stagingUrl}/api/v1.0/taxpayer/validate/{$lhdnDetails->tin_no}", [
                'idType' => $idType,
                'idValue' => $idValue,
            ])->throw();

            $data = $response->json();

            // dd($data);
            return ([
                'success' => true,
                'data' => []
            ]);
        } catch (Exception $e) {
            Log::error('Error during login taxpayer: ' . $e->getMessage());
            return ([
                'success' => false,
                'data' => []
            ]);
        }

    }

    /// untuk search tin number based on ic
    public function searchTin(){
        try {
            // get all input from request
            $idType = request()->input('idType');
            $idValue = request()->input('idValue');
            $taxPayerName = request()->input('taxPayerName');
            $hash = request()->input('hash');

            $lhdnDetails = null;
            $lhdnToken = null;
            // if hash is not null, then get the order
            if($hash){
                $orderId = LhdnLink::where('qr_id', $hash)->first();
                if($orderId){
                    // find company id from order id
                    $order = Order::find($orderId->order_id);
                    if(!$order){
                        throw new Exception('Order not found.');
                    }
                    $company = $order->company;
                    $lhdnDetails = $company?->lhdn;

                    $lhdnToken = $lhdnDetails?->companylhdntoken;
                }
            }else{
                $company = auth()->user()?->companies;
                $lhdnDetails = $company?->lhdn;
                $lhdnToken = $lhdnDetails?->companylhdntoken;
            }


            if (!$lhdnToken) {
                throw new Exception('LHDN details not found.');
            }

            $this->checkToken($hash, $lhdnDetails);

            $response = Http::withToken($lhdnToken->access_token)
            ->get("{$this->stagingUrl}/api/v1.0/taxpayer/search/tin", [
                'idType' => $idType,
                'idValue' => $idValue,
                'taxpayerName' => '',
            ])->throw();

            $data = $response->json();

            // dd($data);
            return [
                'success' => true,
                'data' => $data['tin']
            ];
        } catch (Exception $e) {
            Log::error('Error during search tin: ' . $e->getMessage());
            return [
                'success' => false,
                'data' => []
            ];
        }
    }

    public function checkToken($hash = null, ?CompanyLhdn $lhdn = null){
        // check token dari db, kalau null then generate baru
        // kalau ada kene check dengan current date dah expired blom. kalau lebih dari 3600s, kene generate baru
        try{
            $lhdntoken = null;
            $lhdnDetails = null;
            if ($lhdn) {
                $lhdntoken = $lhdn->companylhdntoken;
                $lhdnDetails = $lhdn;
            } else {
                $company = auth()->user()?->companies;
                $lhdn = $company?->lhdn;
                $lhdntoken = $lhdn?->companylhdntoken;
                $lhdnDetails = $lhdn;
            }

            if (!$lhdntoken) {
                throw new Exception('LHDN details not found.');
            }

            // check token. if $lhdnDetails->token_expire - $now = 3600
            $now = now();
            // dd('sini');
            if ($lhdntoken->date_generated && $lhdntoken->date_generated->diffInSeconds($now) >= $lhdntoken->expires_in) {
                $login = $this->loginTaxpayer($lhdnDetails);

                if(!$login['success']){
                    throw new Exception('failed to get taxpayer.');
                }
            }

            // dd($lhdntoken);
            return ([
                'success' => true,
                'access_token' => $lhdntoken->access_token
            ]);
        }catch(Exception $e){
            Log::error('Error during check token: ' . $e->getMessage());
            return ([
                'success' => false,
            ]);
        }
    }

    /// json untuk submit single submission
    public function singleJson($id, ?Order $order = null, $invoice_id, $customer = []){
        try{
            // $invoice_id = "BZPOS-{$id}";
            $now = Carbon::now()->setTimezone('UTC');

            $issue_date = $now->toDateString();       // e.g. "2025-05-30"
            $issue_time = $now->format('H:i:s\Z');    // e.g. "07:45:00Z"

            // $industryClassificationCode = "00000";
            // $industryClassificationName = "NOT APPLICABLE";

            $supplier = null;
            if($customer['hash']){
                $supplier = LhdnLink::where('qr_id', $customer['hash'])->first();
                if($supplier){
                    $supplier = $order->company;
                }
                // dd([
                //     'supplier' => $supplier,
                // ]);
            }else{
                $supplier = auth()->user()?->companies;
                // dd($supplier);
            }
            $supplierLhdn = $supplier?->lhdn;

            $industryClassificationCode = $supplierLhdn?->msic_code ?? '00000';
            // Get MSIC code description from LHDN MSIC code table + Dapatkan penerangan kod MSIC dari jadual kod MSIC LHDN
            $industryClassificationName = LhdnMsicCode::where('code', $industryClassificationCode)->first()?->description ?? 'NOT APPLICABLE';

            // hq
            // find postcode and state based on $supplierLhdn->address_postcode
            $postcode = $supplierLhdn?->postcode ? Postcode::where('postcode', $supplierLhdn->postcode)->first() : null;
            $state = $postcode?->state_code ? State::where('state_code', $postcode->state_code)->first() : null;
            // get state code from state model stateList
            $stateCode = $state?->state_code ? $state->stateList($state->state_code) : '17'; // default to NOT APPLICABLE if no state found

            $supplierTin = $supplierLhdn?->tin_no ?? '';
            $supplierBrn = $supplierLhdn?->ssm_name ?? '';
            $supplierSst = $supplierLhdn?->sst_no ?? '';
            $supplierTourismSst = $supplierLhdn?->tourism_tax_no ?? '';
            $supplierAddressCity = $postcode->post_office ?? '';
            $supplierAddressPostcode = $postcode?->postcode ?? '53000';
            $supplierAddressCountryCode = $stateCode;
            $supplierAddressLine1 = $supplierLhdn?->address ?? '';
            $supplierAddressLine2 = "";
            $supplierAddressLine3 = "";
            $supplierName = $supplierLhdn?->name ?? '';
            $supplierPhoneNo = $supplierLhdn?->phone ?? '';
            $supplierEmail = $supplierLhdn?->email ?? '';
            $supplierNric = $supplierLhdn?->ic_no ?? '';

            // customer
            $postcode = !empty($customer['postcode']) ? Postcode::where('postcode', $customer['postcode'])->first() : null;
            $state = $postcode?->state_code ? State::where('state_code', $postcode->state_code)->first() : null;
            // get state code from state model stateList
            $stateCode = $state?->state_code ? $state->stateList($state->state_code) : '17'; // default to NOT APPLICABLE if no state found

            $customerAddressCity = $postcode?->post_office ?? '';
            $customerAddressPostcode = $postcode?->postcode ?? '86400';
            $customerAddressCountryCode = $stateCode;
            $customerAddressLine1 = $customer['address'] ?? '';
            $customerAddressLine2 = "";
            $customerAddressLine3 = "";
            $customerName = $customer['name'] ?? '';
            $customerTin = $customer['tin_no'] ?? '';
            $customerBrn = $customer['brn_no'] ?? '';
            $customerSst = $customer['sst_no'] ?? '';
            $customerTourismSst = $customer['tourism_sst'] ?? '';
            $customerNric = $customer['ic_no'] ?? '';
            $customerPhoneNo = $customer['contact_no'] ?? '';
            $customerEmail = $customer['email'] ?? '';

            // shipment
            $shipmentId = "BZPOS-SP-{$id}";
            $shipmentServiceReason = "";
            $shipmentServiceAmount = 0;

            // payment
            $payeeAccountNumber = "";
            $payeeTermNote = "";

            // prepaid
            $prepaidPaymentId = "";
            $prepaidAmount = 0;
            $prepaidDate = "";
            $prepaidTime = "";

            // allowance charge
            $allowanceReason = "DISCOUNT";
            $allowanceAmount = (float)$order->discounts_decimal ?? 0.0;

            // tax
            $taxAmount = (float)$order->sst_amount ?? 0.00;
            $taxSubtotalAbleAmount = 0.0;
            $taxSubtotalAmount = 0.0;
            $taxCategoryId = "01";
            $taxScheme = "";

            // legal monetary total
            $legalExtensionAmount = (float)$order->grandtotal_decimal ?? 0.0; // total net amount
            $legalTaxExclusiveAmount = (float)$order->subtotal_decimal ?? 0.0; // total excluding tax
            $legalTaxInclusiveAmount = (float)$order->grandtotal_decimal ?? 0.0; // total including tax
            $legalAllowanceTotalAmount = 0.0; //(float)$order->discounts_decimal ?? 0.0;
            $legalChargeTotalAmount = 0.0;
            $legalPayableRoundingAmount = 0.0; // total rounding amount
            $legalPayableAmount = (float)$order->grandtotal_decimal ?? 0.0; // total payable amount

            $invoceLine = [];

            // loop item details lepastu insert dalam invoice line array
            if($order && $order->orderDetails){

                // dd($order);
                foreach($order->orderDetails as $index => $od){
                    // insert return from invoiceLineJson func into $invoiceLine
                    $invoceLine[] = $this->invoiceLineJson($od, $index + 1, $order);
                    // dd($invoiceLine);
                }
            }

            // dd($invoceLine);

            $payload = [
                "_D" => "urn:oasis:names:specification:ubl:schema:xsd:Invoice-2",
                "_A" => "urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2",
                "_B" => "urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2",
                "Invoice" => [
                    [
                        "ID" => [
                            [
                                "_" => $invoice_id
                            ]
                        ],
                        "IssueDate" => [
                            [
                                "_" => $issue_date
                            ]
                        ],
                        "IssueTime" => [
                            [
                                "_" => $issue_time
                            ]
                        ],
                        "InvoiceTypeCode" => [
                            [
                                "_" => "01",
                                "listVersionID" => "1.0"
                            ]
                        ],
                        "DocumentCurrencyCode" => [
                            [
                                "_" => "MYR"
                            ]
                        ],
                        "TaxCurrencyCode" => [
                            [
                                "_" => "MYR"
                            ]
                        ],
                        "InvoicePeriod" => [
                            [
                                "StartDate" => [
                                    [
                                        "_" => ""
                                    ]
                                ],
                                "EndDate" => [
                                    [
                                        "_" => ""
                                    ]
                                ],
                                "Description" => [
                                    [
                                        "_" => ""
                                    ]
                                ]
                            ]
                        ],
                        "BillingReference" => [
                            [
                                "AdditionalDocumentReference" => [
                                    [
                                        "ID" => [
                                            [
                                                "_" => ""
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ],
                        "AdditionalDocumentReference" => [
                            [
                                "ID" => [
                                    [
                                        "_" => ""
                                    ]
                                ],
                                "DocumentType" => [
                                    [
                                        "_" => "CustomsImportForm"
                                    ]
                                ]
                            ],
                            [
                                "ID" => [
                                    [
                                        "_" => ""
                                    ]
                                ],
                                "DocumentType" => [
                                    [
                                        "_" => "213312dddddd"
                                    ]
                                ],
                                "DocumentDescription" => [
                                    [
                                        "_" => "asddasdwqfd ddq"
                                    ]
                                ]
                            ],
                            [
                                "ID" => [
                                    [
                                        "_" => ""
                                    ]
                                ],
                                "DocumentType" => [
                                    [
                                        "_" => "K2"
                                    ]
                                ]
                            ],
                            [
                                "ID" => [
                                    [
                                        "_" => "CIF"
                                    ]
                                ]
                            ]
                        ],
                        "AccountingSupplierParty" => [
                            [
                                "AdditionalAccountID" => [
                                    [
                                        "_" => "",
                                        "schemeAgencyName" => "CertEX"
                                    ]
                                ],
                                "Party" => [
                                    [
                                        "IndustryClassificationCode" => [
                                            [
                                                "_" => $industryClassificationCode,
                                                "name" => $industryClassificationName
                                            ]
                                        ],
                                        "PartyIdentification" => [
                                            [
                                                "ID" => [
                                                    [
                                                        "_" => $supplierTin,
                                                        "schemeID" => "TIN"
                                                    ]
                                                ]
                                            ],
                                            [
                                                "ID" => [
                                                    [
                                                        "_" => $supplierNric,
                                                        "schemeID" => "NRIC"
                                                    ]
                                                ]
                                            ],
                                            // [
                                            //     "ID" => [
                                            //         [
                                            //             "_" => $supplierBrn,
                                            //             "schemeID" => "BRN"
                                            //         ]
                                            //     ]
                                            // ],
                                            // [
                                            //     "ID" => [
                                            //         [
                                            //             "_" => $supplierSst,
                                            //             "schemeID" => "SST"
                                            //         ]
                                            //     ]
                                            // ],
                                            // [
                                            //     "ID" => [
                                            //         [
                                            //             "_" => $supplierTourismSst,
                                            //             "schemeID" => "TTX"
                                            //         ]
                                            //     ]
                                            // ]
                                        ],
                                        "PostalAddress" => [
                                            [
                                                "CityName" => [
                                                    [
                                                        "_" => $supplierAddressCity
                                                    ]
                                                ],
                                                "PostalZone" => [
                                                    [
                                                        "_" => $supplierAddressPostcode
                                                    ]
                                                ],
                                                "CountrySubentityCode" => [
                                                    [
                                                        "_" => $supplierAddressCountryCode
                                                    ]
                                                ],
                                                "AddressLine" => [
                                                    [
                                                        "Line" => [
                                                            [
                                                                "_" => $supplierAddressLine1
                                                            ]
                                                        ]
                                                    ],
                                                    [
                                                        "Line" => [
                                                            [
                                                                "_" => $supplierAddressLine2
                                                            ]
                                                        ]
                                                    ],
                                                    [
                                                        "Line" => [
                                                            [
                                                                "_" => $supplierAddressLine3
                                                            ]
                                                        ]
                                                    ]
                                                ],
                                                "Country" => [ // hold dulu
                                                    [
                                                        "IdentificationCode" => [
                                                            [
                                                                "_" => "MYS",
                                                                "listID" => "ISO3166-1",
                                                                "listAgencyID" => "6"
                                                            ]
                                                        ]
                                                    ]
                                                ]
                                            ]
                                        ],
                                        "PartyLegalEntity" => [
                                            [
                                                "RegistrationName" => [
                                                    [
                                                        "_" => $supplierName
                                                    ]
                                                ]
                                            ]
                                        ],
                                        "Contact" => [
                                            [
                                                "Telephone" => [
                                                    [
                                                        "_" => $supplierPhoneNo
                                                    ]
                                                ],
                                                "ElectronicMail" => [
                                                    [
                                                        "_" => $supplierEmail
                                                    ]
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ],
                        "AccountingCustomerParty" => [
                            [
                                "Party" => [
                                    [
                                        "PostalAddress" => [
                                            [
                                                "CityName" => [
                                                    [
                                                        "_" => $customerAddressCity
                                                    ]
                                                ],
                                                "PostalZone" => [
                                                    [
                                                        "_" => $customerAddressPostcode
                                                    ]
                                                ],
                                                "CountrySubentityCode" => [
                                                    [
                                                        "_" => $customerAddressCountryCode
                                                    ]
                                                ],
                                                "AddressLine" => [
                                                    [
                                                        "Line" => [
                                                            [
                                                                "_" => $customerAddressLine1
                                                            ]
                                                        ]
                                                    ],
                                                    [
                                                        "Line" => [
                                                            [
                                                                "_" => $customerAddressLine2
                                                            ]
                                                        ]
                                                    ],
                                                    [
                                                        "Line" => [
                                                            [
                                                                "_" => $customerAddressLine3
                                                            ]
                                                        ]
                                                    ]
                                                ],
                                                "Country" => [
                                                    [
                                                        "IdentificationCode" => [ // hold dulu
                                                            [
                                                                "_" => "MYS",
                                                                "listID" => "ISO3166-1",
                                                                "listAgencyID" => "6"
                                                            ]
                                                        ]
                                                    ]
                                                ]
                                            ]
                                        ],
                                        "PartyLegalEntity" => [
                                            [
                                                "RegistrationName" => [
                                                    [
                                                        "_" => $customerName
                                                    ]
                                                ]
                                            ]
                                        ],
                                        "PartyIdentification" => [
                                            [
                                                "ID" => [
                                                    [
                                                        "_" => $customerTin,
                                                        "schemeID" => "TIN"
                                                    ]
                                                ]
                                            ],
                                            [
                                                "ID" => [
                                                    [
                                                        "_" => $customerNric,
                                                        "schemeID" => "NRIC"
                                                    ]
                                                ]
                                            ],
                                            // [
                                            //     "ID" => [
                                            //         [
                                            //             "_" => $customerBrn,
                                            //             "schemeID" => "BRN"
                                            //         ]
                                            //     ]
                                            // ],
                                            // [
                                            //     "ID" => [
                                            //         [
                                            //             "_" => $customerSst,
                                            //             "schemeID" => "SST"
                                            //         ]
                                            //     ]
                                            // ],
                                            // [
                                            //     "ID" => [
                                            //         [
                                            //             "_" => $customerTourismSst,
                                            //             "schemeID" => "TTX"
                                            //         ]
                                            //     ]
                                            // ]
                                        ],
                                        "Contact" => [
                                            [
                                                "Telephone" => [
                                                    [
                                                        "_" => $customerPhoneNo
                                                    ]
                                                ],
                                                "ElectronicMail" => [
                                                    [
                                                        "_" => $customerEmail
                                                    ]
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ],
                        "Delivery" => [
                            [
                                "DeliveryParty" => [
                                    [
                                        "PartyLegalEntity" => [
                                            [
                                                "RegistrationName" => [
                                                    [
                                                        "_" => $customerName
                                                    ]
                                                ]
                                            ]
                                        ],
                                        "PostalAddress" => [
                                            [
                                                "CityName" => [
                                                    [
                                                        "_" => $customerAddressCity
                                                    ]
                                                ],
                                                "PostalZone" => [
                                                    [
                                                        "_" => $customerAddressPostcode
                                                    ]
                                                ],
                                                "CountrySubentityCode" => [
                                                    [
                                                        "_" => $customerAddressCountryCode
                                                    ]
                                                ],
                                                "AddressLine" => [
                                                    [
                                                        "Line" => [
                                                            [
                                                                "_" => $customerAddressLine1
                                                            ]
                                                        ]
                                                    ],
                                                    [
                                                        "Line" => [
                                                            [
                                                                "_" => $customerAddressLine2
                                                            ]
                                                        ]
                                                    ],
                                                    [
                                                        "Line" => [
                                                            [
                                                                "_" => $customerAddressLine3
                                                            ]
                                                        ]
                                                    ]
                                                ],
                                                "Country" => [ // hold dulu
                                                    [
                                                        "IdentificationCode" => [
                                                            [
                                                                "_" => "MYS",
                                                                "listID" => "ISO3166-1",
                                                                "listAgencyID" => "6"
                                                            ]
                                                        ]
                                                    ]
                                                ]
                                            ]
                                        ],
                                        "PartyIdentification" => [
                                            [
                                                "ID" => [
                                                    [
                                                        "_" => $customerTin,
                                                        "schemeID" => "TIN"
                                                    ]
                                                ]
                                            ],
                                            [
                                                "ID" => [
                                                    [
                                                        "_" => $customerBrn,
                                                        "schemeID" => "BRN"
                                                    ]
                                                ]
                                            ]
                                        ]
                                    ]
                                ],
                                "Shipment" => [
                                    [
                                        "ID" => [
                                            [
                                                "_" => $shipmentId
                                            ]
                                        ],
                                        "FreightAllowanceCharge" => [
                                            [
                                                "ChargeIndicator" => [
                                                    [
                                                        "_" => false
                                                    ]
                                                ],
                                                "AllowanceChargeReason" => [
                                                    [
                                                        "_" => $shipmentServiceReason
                                                    ]
                                                ],
                                                "Amount" => [
                                                    [
                                                        "_" => $shipmentServiceAmount,
                                                        "currencyID" => "MYR"
                                                    ]
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ],
                        "PaymentMeans" => [
                            [
                                "PaymentMeansCode" => [
                                    [
                                        "_" => "03"
                                    ]
                                ],
                                "PayeeFinancialAccount" => [
                                    [
                                        "ID" => [
                                            [
                                                "_" => $payeeAccountNumber
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ],
                        "PaymentTerms" => [
                            [
                                "Note" => [
                                    [
                                        "_" => $payeeTermNote
                                    ]
                                ]
                            ]
                        ],
                        "PrepaidPayment" => [
                            [
                                "ID" => [
                                    [
                                        "_" => $prepaidPaymentId
                                    ]
                                ],
                                "PaidAmount" => [
                                    [
                                        "_" => $prepaidAmount,
                                        "currencyID" => "MYR"
                                    ]
                                ],
                                "PaidDate" => [
                                    [
                                        "_" =>$prepaidDate
                                    ]
                                ],
                                "PaidTime" => [
                                    [
                                        "_" => $prepaidTime
                                    ]
                                ]
                            ]
                        ],
                        "AllowanceCharge" => [ // hold dulu
                            [
                                "ChargeIndicator" => [
                                    [
                                        "_" => false
                                    ]
                                ],
                                "AllowanceChargeReason" => [
                                    [
                                        "_" => $allowanceReason
                                    ]
                                ],
                                "Amount" => [
                                    [
                                        "_" => $allowanceAmount,
                                        "currencyID" => "MYR"
                                    ]
                                ]
                            ],
                            // [
                            //     "ChargeIndicator" => [
                            //         [
                            //             "_" => true
                            //         ]
                            //     ],
                            //     "AllowanceChargeReason" => [
                            //         [
                            //             "_" => $allowanceReason
                            //         ]
                            //     ],
                            //     "Amount" => [
                            //         [
                            //             "_" => $allowanceAmount,
                            //             "currencyID" => "MYR"
                            //         ]
                            //     ]
                            // ]
                        ],
                        "TaxTotal" => [ // hold dulu
                            [
                                "TaxAmount" => [ // jumlah tax yang dikenakan
                                    [
                                        "_" => $taxAmount,
                                        "currencyID" => "MYR"
                                    ]
                                ],
                                "TaxSubtotal" => [
                                    [
                                        "TaxableAmount" => [
                                            [
                                                "_" => $taxSubtotalAbleAmount,
                                                "currencyID" => "MYR"
                                            ]
                                        ],
                                        "TaxAmount" => [
                                            [
                                                "_" => $taxSubtotalAmount,
                                                "currencyID" => "MYR"
                                            ]
                                        ],
                                        "TaxCategory" => [
                                            [
                                                "ID" => [
                                                    [
                                                        "_" => $taxCategoryId
                                                    ]
                                                ],
                                                "TaxScheme" => [
                                                    [
                                                        "ID" => [
                                                            [
                                                                "_" => "OTH",
                                                                "schemeID" => "UN/ECE 5153",
                                                                "schemeAgencyID" => "6"
                                                            ]
                                                        ]
                                                    ]
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ],
                        "LegalMonetaryTotal" => [ // 1436.5
                            [
                                "LineExtensionAmount" => [ // grandtotal - tax
                                    [
                                        "_" => $legalExtensionAmount,
                                        "currencyID" => "MYR"
                                    ]
                                ],
                                "TaxExclusiveAmount" => [ // total semua bayaran (subtotal)
                                    [
                                        "_" => $legalTaxExclusiveAmount,
                                        "currencyID" => "MYR"
                                    ]
                                ],
                                "TaxInclusiveAmount" => [ // total bayaran termasuk cukai
                                    [
                                        "_" => $legalTaxInclusiveAmount,
                                        "currencyID" => "MYR"
                                    ]
                                ],
                                "AllowanceTotalAmount" => [ // diskaun total
                                    [
                                        "_" => $legalAllowanceTotalAmount,
                                        "currencyID" => "MYR"
                                    ]
                                ],
                                "ChargeTotalAmount" => [ // total bayaran product sebelum dikenakan cukai
                                    [
                                        "_" => $legalChargeTotalAmount,
                                        "currencyID" => "MYR"
                                    ]
                                ],
                                "PayableRoundingAmount" => [ // rounding value yang dikenakan
                                    [
                                        "_" => $legalPayableRoundingAmount,
                                        "currencyID" => "MYR"
                                    ]
                                ],
                                "PayableAmount" => [ // total bayaran termasuk tax dan rounding
                                    [
                                        "_" => $legalPayableAmount,
                                        "currencyID" => "MYR"
                                    ]
                                ]
                            ]
                        ],
                        "InvoiceLine" => $invoceLine
                    ]
                ]
            ];

            return([
                'success' => true,
                'data' => $payload
            ]);

        // dd($payload);
        }catch(Exception $e){
            Log::error("error during single json: {$e}");
            return ([
                'success' => false,
                'data' => []
            ]);
        }
    }

    /// json untuk submit consolidate submission
    public function consolidateJson(){

        $invoiceId = 'JSON-INV12345';
        // date
        $issueDate = date('Y-m-d');
        $issueTime = date('H:i:s');

        $invoiceTypeCode = '01';
        $documentCurrencyCode = 'MYR';
        $taxCurrencyCode = 'MYR';

        /// Accounting Supplier Party
        $supplierTin = '**********';
        $supplierBrn = '**********';
        $supplierSst = '**********';
        $supplierTtx = '**********';
        $industryClassificationCode = '46510';
        $industryClassificationName = 'Wholesale of computer hardware, software and peripherals';
        // Postal Address
        $supplierAddressLine1 = 'Lot 66';
        $supplierAddressLine2 = 'Bangunan Merdeka';
        $supplierAddressLine3 = 'Persiaran Jaya';
        $supplierCityName = 'Kuala Lumpur';
        $supplierPostalZone = '50480';
        $supplierCountrySubentityCode = '10';
        $supplierCountryIdentificationCode = 'MYS';
        $supplierRegistrationName = 'Supplier\'s Name';
        $supplierTelephone = '**********';
        $supplierElectronicMail = '<EMAIL>';
        $supplierPartyIdentificationSchemeId = 'TIN';
        $supplierPartyIdentificationSchemeAgencyId = '6';
        $supplierPartyIdentificationSchemeId = 'BRN';
        $supplierPartyIdentificationSchemeAgencyId = '6';
        $supplierPartyIdentificationSchemeId = 'SST';
        $supplierPartyIdentificationSchemeAgencyId = '6';

        /// Accounting Customer Party
        $customerTin = '**********';
        $customerBrn = '**********';
        $customerSst = '**********';
        $customerTtx = '**********';
        $customerAddressLine1 = 'Lot 66';
        $customerAddressLine2 = 'Bangunan Merdeka';
        $customerAddressLine3 = 'Persiaran Jaya';
        $customerCityName = 'Kuala Lumpur';
        $customerPostalZone = '50480';
        $customerCountrySubentityCode = '10';
        $customerCountryIdentificationCode = 'MYS';
        $customerRegistrationName = 'Customer\'s Name';
        $customerTelephone = '**********';
        $customerElectronicMail = '<EMAIL>';
        $customerPartyIdentificationSchemeId = 'TIN';
        $customerPartyIdentificationSchemeAgencyId = '6';
        $customerPartyIdentificationSchemeId = 'BRN';
        $customerPartyIdentificationSchemeAgencyId = '6';
        $customerPartyIdentificationSchemeId = 'SST';
        $customerPartyIdentificationSchemeAgencyId = '6';
        // Tax Total
        $taxAmount = 3000;
        $taxSubtotalAbleAmount = 30000;
        $taxSubtotalAmount = 3000;
        $taxCategoryId = '01';
        $taxSchemeId = 'OTH';
        $taxSchemeAgencyId = 'UN/ECE 5153';
        // Legal Monetary Total
        $legalExtensionAmount = 30000;
        $legalTaxExclusiveAmount = 30000;
        $legalTaxInclusiveAmount = 33000;
        $legalAllowanceTotalAmount = 0;
        $legalChargeTotalAmount = 0;
        $legalPayableRoundingAmount = 0;
        $legalPayableAmount = 33000;

        $payload = [
            "_D" => "urn:oasis:names:specification:ubl:schema:xsd:Invoice-2",
            "_A" => "urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2",
            "_B" => "urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2",
            "Invoice" => [
                [
                    "ID" => [["_" => $invoiceId]],
                    "IssueDate" => [["_" => $issueDate]],
                    "IssueTime" => [["_" => $issueTime]],
                    "InvoiceTypeCode" => [["_" => $invoiceTypeCode, "listVersionID" => "1.0"]],
                    "DocumentCurrencyCode" => [["_" => $documentCurrencyCode]],
                    "TaxCurrencyCode" => [["_" => $taxCurrencyCode]],
                    "AccountingSupplierParty" => [[
                        "Party" => [[
                            "IndustryClassificationCode" => [["_" => $industryClassificationCode, "name" => $industryClassificationName]],
                            "PartyIdentification" => [
                                ["ID" => [["_" => $supplierTin, "schemeID" => $supplierPartyIdentificationSchemeId]]],
                                ["ID" => [["_" => $supplierBrn, "schemeID" => $supplierPartyIdentificationSchemeId]]],
                                ["ID" => [["_" => "NA", "schemeID" => "SST"]]],
                                ["ID" => [["_" => "NA", "schemeID" => "TTX"]]]
                            ],
                            "PostalAddress" => [[
                                "CityName" => [["_" => $supplierCityName]],
                                "PostalZone" => [["_" => $supplierPostalZone]],
                                "CountrySubentityCode" => [["_" => $supplierCountrySubentityCode]],
                                "AddressLine" => [
                                    ["Line" => [["_" => $supplierAddressLine1]]],
                                    ["Line" => [["_" => $supplierAddressLine2]]],
                                    ["Line" => [["_" => $supplierAddressLine3]]]
                                ],
                                "Country" => [[
                                    "IdentificationCode" => [["_" => $supplierCountryIdentificationCode, "listID" => "ISO3166-1", "listAgencyID" => "6"]]
                                ]]
                            ]],
                            "PartyLegalEntity" => [["RegistrationName" => [["_" => $supplierRegistrationName]]]],
                            "Contact" => [["Telephone" => [["_" => $supplierTelephone]], "ElectronicMail" => [["_" => $supplierElectronicMail]]]]
                        ]]
                    ]],
                    "AccountingCustomerParty" => [[
                        "Party" => [[
                            "PostalAddress" => [[
                                "CityName" => [["_" => $customerCityName]],
                                "PostalZone" => [["_" => $customerPostalZone]],
                                "CountrySubentityCode" => [["_" => $customerCountrySubentityCode]],
                                "AddressLine" => [
                                    ["Line" => [["_" => "NA"]]],
                                    ["Line" => [["_" => $customerAddressLine2]]],
                                    ["Line" => [["_" => $customerAddressLine3]]]
                                ],
                                "Country" => [[
                                    "IdentificationCode" => [["_" => $customerCountryIdentificationCode, "listID" => "ISO3166-1", "listAgencyID" => "6"]]
                                ]]
                            ]],
                            "PartyLegalEntity" => [["RegistrationName" => [["_" => "Consolidated Buyer's"]]]],
                            "PartyIdentification" => [
                                ["ID" => [["_" => $customerTin, "schemeID" => $customerPartyIdentificationSchemeId]]],
                                ["ID" => [["_" => $customerBrn, "schemeID" => $customerPartyIdentificationSchemeId]]],
                                ["ID" => [["_" => "NA", "schemeID" => "SST"]]],
                                ["ID" => [["_" => "NA", "schemeID" => "TTX"]]]
                            ],
                            "Contact" => [["Telephone" => [["_" => $customerTelephone]], "ElectronicMail" => [["_" => $customerElectronicMail]]]]
                        ]]
                    ]],
                    "TaxTotal" => [[
                        "TaxAmount" => [["_" => $taxAmount, "currencyID" => $taxCurrencyCode]],
                        "TaxSubtotal" => [[
                            "TaxableAmount" => [["_" => $taxSubtotalAbleAmount, "currencyID" => $taxCurrencyCode]],
                            "TaxAmount" => [["_" => $taxSubtotalAmount, "currencyID" => $taxCurrencyCode]],
                            "TaxCategory" => [[
                                "ID" => [["_" => $taxCategoryId]],
                                "TaxScheme" => [["ID" => [["_" => $taxSchemeId, "schemeID" => $taxSchemeAgencyId, "schemeAgencyID" => $taxSchemeAgencyId]]]]
                            ]]
                        ]]
                    ]],
                    "LegalMonetaryTotal" => [[
                        "LineExtensionAmount" => [["_" => $legalExtensionAmount, "currencyID" => $taxCurrencyCode]],
                        "TaxExclusiveAmount" => [["_" => $legalTaxExclusiveAmount, "currencyID" => $taxCurrencyCode]],
                        "TaxInclusiveAmount" => [["_" => $legalTaxInclusiveAmount, "currencyID" => $taxCurrencyCode]],
                        "PayableAmount" => [["_" => $legalPayableAmount, "currencyID" => $taxCurrencyCode]]
                    ]],
                    "InvoiceLine" => [
                        [
                            "ID" => [["_" => "1"]],
                            "InvoicedQuantity" => [["_" => 1, "unitCode" => "C62"]],
                            "LineExtensionAmount" => [["_" => 10000, "currencyID" => "MYR"]],
                            "TaxTotal" => [[
                                "TaxAmount" => [["_" => 1000, "currencyID" => "MYR"]],
                                "TaxSubtotal" => [[
                                    "TaxableAmount" => [["_" => 10000, "currencyID" => "MYR"]],
                                    "TaxAmount" => [["_" => 1000, "currencyID" => "MYR"]],
                                    "Percent" => [["_" => 10]],
                                    "TaxCategory" => [[
                                        "ID" => [["_" => "01"]],
                                        "TaxScheme" => [["ID" => [["_" => "OTH", "schemeID" => "UN/ECE 5153", "schemeAgencyID" => "6"]]]]
                                    ]]
                                ]]
                            ]],
                            "Item" => [[
                                "CommodityClassification" => [["ItemClassificationCode" => [["_" => "004", "listID" => "CLASS"]]]],
                                "Description" => [["_" => "Receipt 001 - 100"]],
                                "OriginCountry" => [["IdentificationCode" => [["_" => "MYS"]]]]
                            ]],
                            "Price" => [["PriceAmount" => [["_" => 10000, "currencyID" => "MYR"]]]],
                            "ItemPriceExtension" => [["Amount" => [["_" => 10000, "currencyID" => "MYR"]]]]
                        ],
                        [
                            "ID" => [["_" => "2"]],
                            "InvoicedQuantity" => [["_" => 1, "unitCode" => "C62"]],
                            "LineExtensionAmount" => [["_" => 20000, "currencyID" => "MYR"]],
                            "TaxTotal" => [[
                                "TaxAmount" => [["_" => 2000, "currencyID" => "MYR"]],
                                "TaxSubtotal" => [[
                                    "TaxableAmount" => [["_" => 20000, "currencyID" => "MYR"]],
                                    "TaxAmount" => [["_" => 2000, "currencyID" => "MYR"]],
                                    "Percent" => [["_" => 10]],
                                    "TaxCategory" => [[
                                        "ID" => [["_" => "01"]],
                                        "TaxScheme" => [["ID" => [["_" => "OTH", "schemeID" => "UN/ECE 5153", "schemeAgencyID" => "6"]]]]
                                    ]]
                                ]]
                            ]],
                            "Item" => [[
                                "CommodityClassification" => [["ItemClassificationCode" => [["_" => "004", "listID" => "CLASS"]]]],
                                "Description" => [["_" => "Receipt 101 - 200"]],
                                "OriginCountry" => [["IdentificationCode" => [["_" => "MYS"]]]]
                            ]],
                            "Price" => [["PriceAmount" => [["_" => 20000, "currencyID" => "MYR"]]]],
                            "ItemPriceExtension" => [["Amount" => [["_" => 20000, "currencyID" => "MYR"]]]]
                        ]
                    ]
                ]
            ]
        ];
        /*
        $payload = [
            "_D" => "urn:oasis:names:specification:ubl:schema:xsd:Invoice-2",
            "_A" => "urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2",
            "_B" => "urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2",
            "Invoice" => [
                [
                    "ID" => [["_" => "JSON-INV12345"]],
                    "IssueDate" => [["_" => "2024-07-23"]],
                    "IssueTime" => [["_" => "00:30:00Z"]],
                    "InvoiceTypeCode" => [["_" => "01", "listVersionID" => "1.0"]],
                    "DocumentCurrencyCode" => [["_" => "MYR"]],
                    "TaxCurrencyCode" => [["_" => "MYR"]],
                    "AccountingSupplierParty" => [[
                        "Party" => [[
                            "IndustryClassificationCode" => [["_" => "46510", "name" => "Wholesale of computer hardware, software and peripherals"]],
                            "PartyIdentification" => [
                                ["ID" => [["_" => "Supplier's TIN", "schemeID" => "TIN"]]],
                                ["ID" => [["_" => "Supplier's BRN", "schemeID" => "BRN"]]],
                                ["ID" => [["_" => "NA", "schemeID" => "SST"]]],
                                ["ID" => [["_" => "NA", "schemeID" => "TTX"]]]
                            ],
                            "PostalAddress" => [[
                                "CityName" => [["_" => "Kuala Lumpur"]],
                                "PostalZone" => [["_" => "50480"]],
                                "CountrySubentityCode" => [["_" => "10"]],
                                "AddressLine" => [
                                    ["Line" => [["_" => "Lot 66"]]],
                                    ["Line" => [["_" => "Bangunan Merdeka"]]],
                                    ["Line" => [["_" => "Persiaran Jaya"]]]
                                ],
                                "Country" => [[
                                    "IdentificationCode" => [["_" => "MYS", "listID" => "ISO3166-1", "listAgencyID" => "6"]]
                                ]]
                            ]],
                            "PartyLegalEntity" => [["RegistrationName" => [["_" => "Supplier's Name"]]]],
                            "Contact" => [["Telephone" => [["_" => "+***********"]], "ElectronicMail" => [["_" => "<EMAIL>"]]]]
                        ]]
                    ]],
                    "AccountingCustomerParty" => [[
                        "Party" => [[
                            "PostalAddress" => [[
                                "CityName" => [["_" => ""]],
                                "PostalZone" => [["_" => ""]],
                                "CountrySubentityCode" => [["_" => ""]],
                                "AddressLine" => [
                                    ["Line" => [["_" => "NA"]]],
                                    ["Line" => [["_" => ""]]],
                                    ["Line" => [["_" => ""]]]
                                ],
                                "Country" => [[
                                    "IdentificationCode" => [["_" => "", "listID" => "ISO3166-1", "listAgencyID" => "6"]]
                                ]]
                            ]],
                            "PartyLegalEntity" => [["RegistrationName" => [["_" => "Consolidated Buyer's"]]]],
                            "PartyIdentification" => [
                                ["ID" => [["_" => "EI00000000010", "schemeID" => "TIN"]]],
                                ["ID" => [["_" => "NA", "schemeID" => "BRN"]]],
                                ["ID" => [["_" => "NA", "schemeID" => "SST"]]],
                                ["ID" => [["_" => "NA", "schemeID" => "TTX"]]]
                            ],
                            "Contact" => [["Telephone" => [["_" => "NA"]], "ElectronicMail" => [["_" => "NA"]]]]
                        ]]
                    ]],
                    "TaxTotal" => [[
                        "TaxAmount" => [["_" => 3000, "currencyID" => "MYR"]],
                        "TaxSubtotal" => [[
                            "TaxableAmount" => [["_" => 30000, "currencyID" => "MYR"]],
                            "TaxAmount" => [["_" => 3000, "currencyID" => "MYR"]],
                            "TaxCategory" => [[
                                "ID" => [["_" => "01"]],
                                "TaxScheme" => [["ID" => [["_" => "OTH", "schemeID" => "UN/ECE 5153", "schemeAgencyID" => "6"]]]]
                            ]]
                        ]]
                    ]],
                    "LegalMonetaryTotal" => [[
                        "LineExtensionAmount" => [["_" => 30000, "currencyID" => "MYR"]],
                        "TaxExclusiveAmount" => [["_" => 30000, "currencyID" => "MYR"]],
                        "TaxInclusiveAmount" => [["_" => 33000, "currencyID" => "MYR"]],
                        "PayableAmount" => [["_" => 33000, "currencyID" => "MYR"]]
                    ]],
                    "InvoiceLine" => [
                        [
                            "ID" => [["_" => "1"]],
                            "InvoicedQuantity" => [["_" => 1, "unitCode" => "C62"]],
                            "LineExtensionAmount" => [["_" => 10000, "currencyID" => "MYR"]],
                            "TaxTotal" => [[
                                "TaxAmount" => [["_" => 1000, "currencyID" => "MYR"]],
                                "TaxSubtotal" => [[
                                    "TaxableAmount" => [["_" => 10000, "currencyID" => "MYR"]],
                                    "TaxAmount" => [["_" => 1000, "currencyID" => "MYR"]],
                                    "Percent" => [["_" => 10]],
                                    "TaxCategory" => [[
                                        "ID" => [["_" => "01"]],
                                        "TaxScheme" => [["ID" => [["_" => "OTH", "schemeID" => "UN/ECE 5153", "schemeAgencyID" => "6"]]]]
                                    ]]
                                ]]
                            ]],
                            "Item" => [[
                                "CommodityClassification" => [["ItemClassificationCode" => [["_" => "004", "listID" => "CLASS"]]]],
                                "Description" => [["_" => "Receipt 001 - 100"]],
                                "OriginCountry" => [["IdentificationCode" => [["_" => "MYS"]]]]
                            ]],
                            "Price" => [["PriceAmount" => [["_" => 10000, "currencyID" => "MYR"]]]],
                            "ItemPriceExtension" => [["Amount" => [["_" => 10000, "currencyID" => "MYR"]]]]
                        ],
                        [
                            "ID" => [["_" => "2"]],
                            "InvoicedQuantity" => [["_" => 1, "unitCode" => "C62"]],
                            "LineExtensionAmount" => [["_" => 20000, "currencyID" => "MYR"]],
                            "TaxTotal" => [[
                                "TaxAmount" => [["_" => 2000, "currencyID" => "MYR"]],
                                "TaxSubtotal" => [[
                                    "TaxableAmount" => [["_" => 20000, "currencyID" => "MYR"]],
                                    "TaxAmount" => [["_" => 2000, "currencyID" => "MYR"]],
                                    "Percent" => [["_" => 10]],
                                    "TaxCategory" => [[
                                        "ID" => [["_" => "01"]],
                                        "TaxScheme" => [["ID" => [["_" => "OTH", "schemeID" => "UN/ECE 5153", "schemeAgencyID" => "6"]]]]
                                    ]]
                                ]]
                            ]],
                            "Item" => [[
                                "CommodityClassification" => [["ItemClassificationCode" => [["_" => "004", "listID" => "CLASS"]]]],
                                "Description" => [["_" => "Receipt 101 - 200"]],
                                "OriginCountry" => [["IdentificationCode" => [["_" => "MYS"]]]]
                            ]],
                            "Price" => [["PriceAmount" => [["_" => 20000, "currencyID" => "MYR"]]]],
                            "ItemPriceExtension" => [["Amount" => [["_" => 20000, "currencyID" => "MYR"]]]]
                        ]
                    ]
                ]
            ]
        ];
        */
    }

    public function invoiceLineJson(?OrderDetail $order = null, $index = 0, ?Order $od = null){
        // dd($order);
        $itemId = $index;
        $totalAmount = $order->product_price_decimal * $order->product_qty;
        $lineExtensionAmount = $totalAmount - $order->product_discount_amount_decimal;
        $discountPercent = $lineExtensionAmount/$totalAmount;
        $itemClassificationCode = "022";

        $sstPercent = $od->sst_value;
        // $sstAmount = $od->sst_amount;

        $sstAmountPerProduct = $totalAmount * ($sstPercent/100);
        $totalAmountWithSst = $totalAmount + $sstAmountPerProduct;

        // dd([
        //     'order->product_price' => $order->product_price,
        //     'product price' => $order->product->product_price,
        //     'order_details_id' => $order
        // ]);



        // condition untuk variant
        // $priceAmount = ($order->product->product_variant == "Y")
        // $priceAmount
        $invoiceLine = [
            // loop part ni untuk setiap jenis item
            "ID" => [
                [
                    "_" => "{$itemId}"
                ]
            ],
            "InvoicedQuantity" => [
                [
                    "_" => (int)$order->product_qty ?? 0,
                    "unitCode" => "XUN"
                ]
            ],
            "LineExtensionAmount" => [
                [
                    "_" => $lineExtensionAmount ?? '0',
                    "currencyID" => "MYR"
                ]
            ],
            "AllowanceCharge" => [
                [
                    "ChargeIndicator" => [
                        [
                            "_" => false
                        ]
                    ],
                    "AllowanceChargeReason" => [
                        [
                            "_" => "DISCOUNT"
                        ]
                    ],
                    "MultiplierFactorNumeric" => [
                        [
                            "_" => $discountPercent
                        ]
                    ],
                    "Amount" => [
                        [
                            "_" => (float)$order->product_discount_amount_decimal,
                            "currencyID" => "MYR"
                        ]
                    ]
                ],
                // [
                //     "ChargeIndicator" => [
                //         [
                //             "_" => true
                //         ]
                //     ],
                //     "AllowanceChargeReason" => [
                //         [
                //             "_" => "Sample Description"
                //         ]
                //     ],
                //     "MultiplierFactorNumeric" => [
                //         [
                //             "_" => 0.1
                //         ]
                //     ],
                //     "Amount" => [
                //         [
                //             "_" => 100,
                //             "currencyID" => "MYR"
                //         ]
                //     ]
                // ]
            ],
            "TaxTotal" => [
                [
                    "TaxAmount" => [
                        [
                            "_" => $sstAmountPerProduct,
                            "currencyID" => "MYR"
                        ]
                    ],
                    "TaxSubtotal" => [
                        [
                            "TaxableAmount" => [
                                [
                                    "_" => 0.0,
                                    "currencyID" => "MYR"
                                ]
                            ],
                            "TaxAmount" => [
                                [
                                    "_" => 0.0,
                                    "currencyID" => "MYR"
                                ]
                            ],
                            "Percent" => [
                                [
                                    "_" => (int)$sstPercent
                                ]
                            ],
                            "TaxCategory" => [
                                [
                                    "ID" => [
                                        [
                                            "_" => "01"
                                        ]
                                    ],
                                    // "TaxExemptionReason" => [
                                    //     [
                                    //         "_" => "Exempt New Means of Transport"
                                    //     ]
                                    // ],
                                    "TaxScheme" => [
                                        [
                                            "ID" => [
                                                [
                                                    "_" => "OTH",
                                                    "schemeID" => "UN/ECE 5153",
                                                    "schemeAgencyID" => "6"
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ],
            "Item" => [
                [
                    "CommodityClassification" => [
                        [
                            "ItemClassificationCode" => [
                                [
                                    "_" => $itemClassificationCode,
                                    "listID" => "PTC"
                                ]
                            ]
                        ],
                        [
                            "ItemClassificationCode" => [
                                [
                                    "_" => $itemClassificationCode,
                                    "listID" => "CLASS"
                                ]
                            ]
                        ]
                    ],
                    "Description" => [
                        [
                            "_" => $order->productName ?? "Bizappos Product"
                        ]
                    ],
                    "OriginCountry" => [
                        [
                            "IdentificationCode" => [
                                [
                                    "_" => "MYS"
                                ]
                            ]
                        ]
                    ]
                ]
            ],
            "Price" => [
                [
                    "PriceAmount" => [
                        [
                            "_" => (float)$order->product_price_decimal,
                            "currencyID" => "MYR"
                        ]
                    ]
                ]
            ],
            "ItemPriceExtension" => [
                [
                    "Amount" => [
                        [
                            "_" => $totalAmount,
                            "currencyID" => "MYR"
                        ]
                    ]
                ]
            ]
        ];


        // dd($invoiceLine);

        return $invoiceLine;
    }

    public function testJson()
    {

        // $this->loginTaxpayer();
        // $this->checkToken();
        // $this->validateTin('NRIC', '000704012131');
        // $this->searchTin('NRIC', '000704012131', '');
        // $payload = [
        //     "_D" => "urn:oasis:names:specification:ubl:schema:xsd:Invoice-2",
        //     "_A" => "urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2",
        //     "_B" => "urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2",
        //     "Invoice" => [
        //         "ID" => [
        //             "_" => "JSON-INV12345",
        //         ],
        //         "IssueDate" => [ // buat - 8 jam
        //             "_" => "2024-07-23"
        //         ],
        //         "IssueTime" => [
        //             "_" => "00:30:00Z"
        //         ],
        //         "InvoiceTypeCode" => [
        //             "_" => "01",
        //             "listVersionID" => "1.0"
        //         ]
        //     ]
        // ];

        $payload1 = [
            "_D" => "urn:oasis:names:specification:ubl:schema:xsd:Invoice-2",
            "_A" => "urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2",
            "_B" => "urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2",
            "Invoice" => [
                [
                    "ID" => [["_" => "JSON-INV12345"]],
                    "IssueDate" => [["_" => "2024-07-23"]],
                    "IssueTime" => [["_" => "00:30:00Z"]],
                    "InvoiceTypeCode" => [["_" => "01", "listVersionID" => "1.0"]],
                    "DocumentCurrencyCode" => [["_" => "MYR"]],
                    "TaxCurrencyCode" => [["_" => "MYR"]],
                    "AccountingSupplierParty" => [[
                        "Party" => [[
                            "IndustryClassificationCode" => [["_" => "46510", "name" => "Wholesale of computer hardware, software and peripherals"]],
                            "PartyIdentification" => [
                                ["ID" => [["_" => "Supplier's TIN", "schemeID" => "TIN"]]],
                                ["ID" => [["_" => "Supplier's BRN", "schemeID" => "BRN"]]],
                                ["ID" => [["_" => "NA", "schemeID" => "SST"]]],
                                ["ID" => [["_" => "NA", "schemeID" => "TTX"]]]
                            ],
                            "PostalAddress" => [[
                                "CityName" => [["_" => "Kuala Lumpur"]],
                                "PostalZone" => [["_" => "50480"]],
                                "CountrySubentityCode" => [["_" => "10"]],
                                "AddressLine" => [
                                    ["Line" => [["_" => "Lot 66"]]],
                                    ["Line" => [["_" => "Bangunan Merdeka"]]],
                                    ["Line" => [["_" => "Persiaran Jaya"]]]
                                ],
                                "Country" => [[
                                    "IdentificationCode" => [["_" => "MYS", "listID" => "ISO3166-1", "listAgencyID" => "6"]]
                                ]]
                            ]],
                            "PartyLegalEntity" => [["RegistrationName" => [["_" => "Supplier's Name"]]]],
                            "Contact" => [["Telephone" => [["_" => "+***********"]], "ElectronicMail" => [["_" => "<EMAIL>"]]]]
                        ]]
                    ]],
                    "AccountingCustomerParty" => [[
                        "Party" => [[
                            "PostalAddress" => [[
                                "CityName" => [["_" => ""]],
                                "PostalZone" => [["_" => ""]],
                                "CountrySubentityCode" => [["_" => ""]],
                                "AddressLine" => [
                                    ["Line" => [["_" => "NA"]]],
                                    ["Line" => [["_" => ""]]],
                                    ["Line" => [["_" => ""]]]
                                ],
                                "Country" => [[
                                    "IdentificationCode" => [["_" => "", "listID" => "ISO3166-1", "listAgencyID" => "6"]]
                                ]]
                            ]],
                            "PartyLegalEntity" => [["RegistrationName" => [["_" => "Consolidated Buyer's"]]]],
                            "PartyIdentification" => [
                                ["ID" => [["_" => "EI00000000010", "schemeID" => "TIN"]]],
                                ["ID" => [["_" => "NA", "schemeID" => "BRN"]]],
                                ["ID" => [["_" => "NA", "schemeID" => "SST"]]],
                                ["ID" => [["_" => "NA", "schemeID" => "TTX"]]]
                            ],
                            "Contact" => [["Telephone" => [["_" => "NA"]], "ElectronicMail" => [["_" => "NA"]]]]
                        ]]
                    ]],
                    "TaxTotal" => [[
                        "TaxAmount" => [["_" => 3000, "currencyID" => "MYR"]],
                        "TaxSubtotal" => [[
                            "TaxableAmount" => [["_" => 30000, "currencyID" => "MYR"]],
                            "TaxAmount" => [["_" => 3000, "currencyID" => "MYR"]],
                            "TaxCategory" => [[
                                "ID" => [["_" => "01"]],
                                "TaxScheme" => [["ID" => [["_" => "OTH", "schemeID" => "UN/ECE 5153", "schemeAgencyID" => "6"]]]]
                            ]]
                        ]]
                    ]],
                    "LegalMonetaryTotal" => [[
                        "LineExtensionAmount" => [["_" => 30000, "currencyID" => "MYR"]],
                        "TaxExclusiveAmount" => [["_" => 30000, "currencyID" => "MYR"]],
                        "TaxInclusiveAmount" => [["_" => 33000, "currencyID" => "MYR"]],
                        "PayableAmount" => [["_" => 33000, "currencyID" => "MYR"]]
                    ]],
                    "InvoiceLine" => [
                        [
                            "ID" => [["_" => "1"]],
                            "InvoicedQuantity" => [["_" => 1, "unitCode" => "C62"]],
                            "LineExtensionAmount" => [["_" => 10000, "currencyID" => "MYR"]],
                            "TaxTotal" => [[
                                "TaxAmount" => [["_" => 1000, "currencyID" => "MYR"]],
                                "TaxSubtotal" => [[
                                    "TaxableAmount" => [["_" => 10000, "currencyID" => "MYR"]],
                                    "TaxAmount" => [["_" => 1000, "currencyID" => "MYR"]],
                                    "Percent" => [["_" => 10]],
                                    "TaxCategory" => [[
                                        "ID" => [["_" => "01"]],
                                        "TaxScheme" => [["ID" => [["_" => "OTH", "schemeID" => "UN/ECE 5153", "schemeAgencyID" => "6"]]]]
                                    ]]
                                ]]
                            ]],
                            "Item" => [[
                                "CommodityClassification" => [["ItemClassificationCode" => [["_" => "004", "listID" => "CLASS"]]]],
                                "Description" => [["_" => "Receipt 001 - 100"]],
                                "OriginCountry" => [["IdentificationCode" => [["_" => "MYS"]]]]
                            ]],
                            "Price" => [["PriceAmount" => [["_" => 10000, "currencyID" => "MYR"]]]],
                            "ItemPriceExtension" => [["Amount" => [["_" => 10000, "currencyID" => "MYR"]]]]
                        ],
                        [
                            "ID" => [["_" => "2"]],
                            "InvoicedQuantity" => [["_" => 1, "unitCode" => "C62"]],
                            "LineExtensionAmount" => [["_" => 20000, "currencyID" => "MYR"]],
                            "TaxTotal" => [[
                                "TaxAmount" => [["_" => 2000, "currencyID" => "MYR"]],
                                "TaxSubtotal" => [[
                                    "TaxableAmount" => [["_" => 20000, "currencyID" => "MYR"]],
                                    "TaxAmount" => [["_" => 2000, "currencyID" => "MYR"]],
                                    "Percent" => [["_" => 10]],
                                    "TaxCategory" => [[
                                        "ID" => [["_" => "01"]],
                                        "TaxScheme" => [["ID" => [["_" => "OTH", "schemeID" => "UN/ECE 5153", "schemeAgencyID" => "6"]]]]
                                    ]]
                                ]]
                            ]],
                            "Item" => [[
                                "CommodityClassification" => [["ItemClassificationCode" => [["_" => "004", "listID" => "CLASS"]]]],
                                "Description" => [["_" => "Receipt 101 - 200"]],
                                "OriginCountry" => [["IdentificationCode" => [["_" => "MYS"]]]]
                            ]],
                            "Price" => [["PriceAmount" => [["_" => 20000, "currencyID" => "MYR"]]]],
                            "ItemPriceExtension" => [["Amount" => [["_" => 20000, "currencyID" => "MYR"]]]]
                        ]
                    ]
                ]
            ]
        ];


        $payload2 = [
            "_D" => "urn:oasis:names:specification:ubl:schema:xsd:Invoice-2",
            "_A" => "urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2",
            "_B" => "urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2",
            "Invoice" => [
                [
                    "ID" => [
                        [
                            "_" => "JSON-INV12345"
                        ]
                    ],
                    "IssueDate" => [
                        [
                            "_" => "2024-07-23"
                        ]
                    ],
                    "IssueTime" => [
                        [
                            "_" => "00:30:00Z"
                        ]
                    ],
                    "InvoiceTypeCode" => [
                        [
                            "_" => "01",
                            "listVersionID" => "1.0"
                        ]
                    ],
                    "DocumentCurrencyCode" => [
                        [
                            "_" => "MYR"
                        ]
                    ],
                    "TaxCurrencyCode" => [
                        [
                            "_" => "MYR"
                        ]
                    ],
                    "InvoicePeriod" => [
                        [
                            "StartDate" => [
                                [
                                    "_" => "2024-01-01"
                                ]
                            ],
                            "EndDate" => [
                                [
                                    "_" => "2024-07-31"
                                ]
                            ],
                            "Description" => [
                                [
                                    "_" => "Monthly"
                                ]
                            ]
                        ]
                    ],
                    "BillingReference" => [
                        [
                            "AdditionalDocumentReference" => [
                                [
                                    "ID" => [
                                        [
                                            "_" => "E12345678912"
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ],
                    "AdditionalDocumentReference" => [
                        [
                            "ID" => [
                                [
                                    "_" => "E12345678912"
                                ]
                            ],
                            "DocumentType" => [
                                [
                                    "_" => "CustomsImportForm"
                                ]
                            ]
                        ],
                        [
                            "ID" => [
                                [
                                    "_" => "sa313321312"
                                ]
                            ],
                            "DocumentType" => [
                                [
                                    "_" => "213312dddddd"
                                ]
                            ],
                            "DocumentDescription" => [
                                [
                                    "_" => "asddasdwqfd ddq"
                                ]
                            ]
                        ],
                        [
                            "ID" => [
                                [
                                    "_" => "E12345678912"
                                ]
                            ],
                            "DocumentType" => [
                                [
                                    "_" => "K2"
                                ]
                            ]
                        ],
                        [
                            "ID" => [
                                [
                                    "_" => "CIF"
                                ]
                            ]
                        ]
                    ],
                    "AccountingSupplierParty" => [
                        [
                            "AdditionalAccountID" => [
                                [
                                    "_" => "CPT-CCN-W-211111-KL-000002",
                                    "schemeAgencyName" => "CertEX"
                                ]
                            ],
                            "Party" => [
                                [
                                    "IndustryClassificationCode" => [
                                        [
                                            "_" => "46510",
                                            "name" => "Wholesale of computer hardware, software and peripherals"
                                        ]
                                    ],
                                    "PartyIdentification" => [
                                        [
                                            "ID" => [
                                                [
                                                    "_" => "Supplier's TIN",
                                                    "schemeID" => "TIN"
                                                ]
                                            ]
                                        ],
                                        [
                                            "ID" => [
                                                [
                                                    "_" => "Supplier's BRN",
                                                    "schemeID" => "BRN"
                                                ]
                                            ]
                                        ],
                                        [
                                            "ID" => [
                                                [
                                                    "_" => "NA",
                                                    "schemeID" => "SST"
                                                ]
                                            ]
                                        ],
                                        [
                                            "ID" => [
                                                [
                                                    "_" => "NA",
                                                    "schemeID" => "TTX"
                                                ]
                                            ]
                                        ]
                                    ],
                                    "PostalAddress" => [
                                        [
                                            "CityName" => [
                                                [
                                                    "_" => "Kuala Lumpur"
                                                ]
                                            ],
                                            "PostalZone" => [
                                                [
                                                    "_" => "50480"
                                                ]
                                            ],
                                            "CountrySubentityCode" => [
                                                [
                                                    "_" => "10"
                                                ]
                                            ],
                                            "AddressLine" => [
                                                [
                                                    "Line" => [
                                                        [
                                                            "_" => "Lot 66"
                                                        ]
                                                    ]
                                                ],
                                                [
                                                    "Line" => [
                                                        [
                                                            "_" => "Bangunan Merdeka"
                                                        ]
                                                    ]
                                                ],
                                                [
                                                    "Line" => [
                                                        [
                                                            "_" => "Persiaran Jaya"
                                                        ]
                                                    ]
                                                ]
                                            ],
                                            "Country" => [
                                                [
                                                    "IdentificationCode" => [
                                                        [
                                                            "_" => "MYS",
                                                            "listID" => "ISO3166-1",
                                                            "listAgencyID" => "6"
                                                        ]
                                                    ]
                                                ]
                                            ]
                                        ]
                                    ],
                                    "PartyLegalEntity" => [
                                        [
                                            "RegistrationName" => [
                                                [
                                                    "_" => "Supplier's Name"
                                                ]
                                            ]
                                        ]
                                    ],
                                    "Contact" => [
                                        [
                                            "Telephone" => [
                                                [
                                                    "_" => "+***********"
                                                ]
                                            ],
                                            "ElectronicMail" => [
                                                [
                                                    "_" => "<EMAIL>"
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ],
                    "AccountingCustomerParty" => [
                        [
                            "Party" => [
                                [
                                    "PostalAddress" => [
                                        [
                                            "CityName" => [
                                                [
                                                    "_" => "Kuala Lumpur"
                                                ]
                                            ],
                                            "PostalZone" => [
                                                [
                                                    "_" => "50480"
                                                ]
                                            ],
                                            "CountrySubentityCode" => [
                                                [
                                                    "_" => "10"
                                                ]
                                            ],
                                            "AddressLine" => [
                                                [
                                                    "Line" => [
                                                        [
                                                            "_" => "Lot 66"
                                                        ]
                                                    ]
                                                ],
                                                [
                                                    "Line" => [
                                                        [
                                                            "_" => "Bangunan Merdeka"
                                                        ]
                                                    ]
                                                ],
                                                [
                                                    "Line" => [
                                                        [
                                                            "_" => "Persiaran Jaya"
                                                        ]
                                                    ]
                                                ]
                                            ],
                                            "Country" => [
                                                [
                                                    "IdentificationCode" => [
                                                        [
                                                            "_" => "MYS",
                                                            "listID" => "ISO3166-1",
                                                            "listAgencyID" => "6"
                                                        ]
                                                    ]
                                                ]
                                            ]
                                        ]
                                    ],
                                    "PartyLegalEntity" => [
                                        [
                                            "RegistrationName" => [
                                                [
                                                    "_" => "Buyer's Name"
                                                ]
                                            ]
                                        ]
                                    ],
                                    "PartyIdentification" => [
                                        [
                                            "ID" => [
                                                [
                                                    "_" => "Buyer's TIN",
                                                    "schemeID" => "TIN"
                                                ]
                                            ]
                                        ],
                                        [
                                            "ID" => [
                                                [
                                                    "_" => "Buyer's BRN",
                                                    "schemeID" => "BRN"
                                                ]
                                            ]
                                        ],
                                        [
                                            "ID" => [
                                                [
                                                    "_" => "NA",
                                                    "schemeID" => "SST"
                                                ]
                                            ]
                                        ],
                                        [
                                            "ID" => [
                                                [
                                                    "_" => "NA",
                                                    "schemeID" => "TTX"
                                                ]
                                            ]
                                        ]
                                    ],
                                    "Contact" => [
                                        [
                                            "Telephone" => [
                                                [
                                                    "_" => "+***********"
                                                ]
                                            ],
                                            "ElectronicMail" => [
                                                [
                                                    "_" => "<EMAIL>"
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ],
                    "Delivery" => [
                        [
                            "DeliveryParty" => [
                                [
                                    "PartyLegalEntity" => [
                                        [
                                            "RegistrationName" => [
                                                [
                                                    "_" => "Recipient's Name"
                                                ]
                                            ]
                                        ]
                                    ],
                                    "PostalAddress" => [
                                        [
                                            "CityName" => [
                                                [
                                                    "_" => "Kuala Lumpur"
                                                ]
                                            ],
                                            "PostalZone" => [
                                                [
                                                    "_" => "50480"
                                                ]
                                            ],
                                            "CountrySubentityCode" => [
                                                [
                                                    "_" => "10"
                                                ]
                                            ],
                                            "AddressLine" => [
                                                [
                                                    "Line" => [
                                                        [
                                                            "_" => "Lot 66"
                                                        ]
                                                    ]
                                                ],
                                                [
                                                    "Line" => [
                                                        [
                                                            "_" => "Bangunan Merdeka"
                                                        ]
                                                    ]
                                                ],
                                                [
                                                    "Line" => [
                                                        [
                                                            "_" => "Persiaran Jaya"
                                                        ]
                                                    ]
                                                ]
                                            ],
                                            "Country" => [
                                                [
                                                    "IdentificationCode" => [
                                                        [
                                                            "_" => "MYS",
                                                            "listID" => "ISO3166-1",
                                                            "listAgencyID" => "6"
                                                        ]
                                                    ]
                                                ]
                                            ]
                                        ]
                                    ],
                                    "PartyIdentification" => [
                                        [
                                            "ID" => [
                                                [
                                                    "_" => "Recipients TIN",
                                                    "schemeID" => "TIN"
                                                ]
                                            ]
                                        ],
                                        [
                                            "ID" => [
                                                [
                                                    "_" => "Recipient's BRN",
                                                    "schemeID" => "BRN"
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ],
                            "Shipment" => [
                                [
                                    "ID" => [
                                        [
                                            "_" => "1234"
                                        ]
                                    ],
                                    "FreightAllowanceCharge" => [
                                        [
                                            "ChargeIndicator" => [
                                                [
                                                    "_" => true
                                                ]
                                            ],
                                            "AllowanceChargeReason" => [
                                                [
                                                    "_" => "Service charge"
                                                ]
                                            ],
                                            "Amount" => [
                                                [
                                                    "_" => 100,
                                                    "currencyID" => "MYR"
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ],
                    "PaymentMeans" => [
                        [
                            "PaymentMeansCode" => [
                                [
                                    "_" => "03"
                                ]
                            ],
                            "PayeeFinancialAccount" => [
                                [
                                    "ID" => [
                                        [
                                            "_" => "*************"
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ],
                    "PaymentTerms" => [
                        [
                            "Note" => [
                                [
                                    "_" => "Payment method is cash"
                                ]
                            ]
                        ]
                    ],
                    "PrepaidPayment" => [
                        [
                            "ID" => [
                                [
                                    "_" => "E12345678912"
                                ]
                            ],
                            "PaidAmount" => [
                                [
                                    "_" => 1,
                                    "currencyID" => "MYR"
                                ]
                            ],
                            "PaidDate" => [
                                [
                                    "_" => "2024-07-23"
                                ]
                            ],
                            "PaidTime" => [
                                [
                                    "_" => "00:30:00Z"
                                ]
                            ]
                        ]
                    ],
                    "AllowanceCharge" => [
                        [
                            "ChargeIndicator" => [
                                [
                                    "_" => false
                                ]
                            ],
                            "AllowanceChargeReason" => [
                                [
                                    "_" => "Sample Description"
                                ]
                            ],
                            "Amount" => [
                                [
                                    "_" => 100,
                                    "currencyID" => "MYR"
                                ]
                            ]
                        ],
                        [
                            "ChargeIndicator" => [
                                [
                                    "_" => true
                                ]
                            ],
                            "AllowanceChargeReason" => [
                                [
                                    "_" => "Service charge"
                                ]
                            ],
                            "Amount" => [
                                [
                                    "_" => 100,
                                    "currencyID" => "MYR"
                                ]
                            ]
                        ]
                    ],
                    "TaxTotal" => [
                        [
                            "TaxAmount" => [
                                [
                                    "_" => 87.63,
                                    "currencyID" => "MYR"
                                ]
                            ],
                            "TaxSubtotal" => [
                                [
                                    "TaxableAmount" => [
                                        [
                                            "_" => 87.63,
                                            "currencyID" => "MYR"
                                        ]
                                    ],
                                    "TaxAmount" => [
                                        [
                                            "_" => 87.63,
                                            "currencyID" => "MYR"
                                        ]
                                    ],
                                    "TaxCategory" => [
                                        [
                                            "ID" => [
                                                [
                                                    "_" => "01"
                                                ]
                                            ],
                                            "TaxScheme" => [
                                                [
                                                    "ID" => [
                                                        [
                                                            "_" => "OTH",
                                                            "schemeID" => "UN/ECE 5153",
                                                            "schemeAgencyID" => "6"
                                                        ]
                                                    ]
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ],
                    "LegalMonetaryTotal" => [
                        [
                            "LineExtensionAmount" => [
                                [
                                    "_" => 1436.5,
                                    "currencyID" => "MYR"
                                ]
                            ],
                            "TaxExclusiveAmount" => [
                                [
                                    "_" => 1436.5,
                                    "currencyID" => "MYR"
                                ]
                            ],
                            "TaxInclusiveAmount" => [
                                [
                                    "_" => 1436.5,
                                    "currencyID" => "MYR"
                                ]
                            ],
                            "AllowanceTotalAmount" => [
                                [
                                    "_" => 1436.5,
                                    "currencyID" => "MYR"
                                ]
                            ],
                            "ChargeTotalAmount" => [
                                [
                                    "_" => 1436.5,
                                    "currencyID" => "MYR"
                                ]
                            ],
                            "PayableRoundingAmount" => [
                                [
                                    "_" => 0.3,
                                    "currencyID" => "MYR"
                                ]
                            ],
                            "PayableAmount" => [
                                [
                                    "_" => 1436.5,
                                    "currencyID" => "MYR"
                                ]
                            ]
                        ]
                    ],
                    "InvoiceLine" => [
                        [
                            "ID" => [
                                [
                                    "_" => "1234"
                                ]
                            ],
                            "InvoicedQuantity" => [
                                [
                                    "_" => 1,
                                    "unitCode" => "C62"
                                ]
                            ],
                            "LineExtensionAmount" => [
                                [
                                    "_" => 1436.5,
                                    "currencyID" => "MYR"
                                ]
                            ],
                            "AllowanceCharge" => [
                                [
                                    "ChargeIndicator" => [
                                        [
                                            "_" => false
                                        ]
                                    ],
                                    "AllowanceChargeReason" => [
                                        [
                                            "_" => "Sample Description"
                                        ]
                                    ],
                                    "MultiplierFactorNumeric" => [
                                        [
                                            "_" => 0.15
                                        ]
                                    ],
                                    "Amount" => [
                                        [
                                            "_" => 100,
                                            "currencyID" => "MYR"
                                        ]
                                    ]
                                ],
                                [
                                    "ChargeIndicator" => [
                                        [
                                            "_" => true
                                        ]
                                    ],
                                    "AllowanceChargeReason" => [
                                        [
                                            "_" => "Sample Description"
                                        ]
                                    ],
                                    "MultiplierFactorNumeric" => [
                                        [
                                            "_" => 0.1
                                        ]
                                    ],
                                    "Amount" => [
                                        [
                                            "_" => 100,
                                            "currencyID" => "MYR"
                                        ]
                                    ]
                                ]
                            ],
                            "TaxTotal" => [
                                [
                                    "TaxAmount" => [
                                        [
                                            "_" => 1460.5,
                                            "currencyID" => "MYR"
                                        ]
                                    ],
                                    "TaxSubtotal" => [
                                        [
                                            "TaxableAmount" => [
                                                [
                                                    "_" => 1460.5,
                                                    "currencyID" => "MYR"
                                                ]
                                            ],
                                            "TaxAmount" => [
                                                [
                                                    "_" => 1460.5,
                                                    "currencyID" => "MYR"
                                                ]
                                            ],
                                            "Percent" => [
                                                [
                                                    "_" => 6
                                                ]
                                            ],
                                            "TaxCategory" => [
                                                [
                                                    "ID" => [
                                                        [
                                                            "_" => "E"
                                                        ]
                                                    ],
                                                    "TaxExemptionReason" => [
                                                        [
                                                            "_" => "Exempt New Means of Transport"
                                                        ]
                                                    ],
                                                    "TaxScheme" => [
                                                        [
                                                            "ID" => [
                                                                [
                                                                    "_" => "OTH",
                                                                    "schemeID" => "UN/ECE 5153",
                                                                    "schemeAgencyID" => "6"
                                                                ]
                                                            ]
                                                        ]
                                                    ]
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ],
                            "Item" => [
                                [
                                    "CommodityClassification" => [
                                        [
                                            "ItemClassificationCode" => [
                                                [
                                                    "_" => "9800.00.0010",
                                                    "listID" => "PTC"
                                                ]
                                            ]
                                        ],
                                        [
                                            "ItemClassificationCode" => [
                                                [
                                                    "_" => "003",
                                                    "listID" => "CLASS"
                                                ]
                                            ]
                                        ]
                                    ],
                                    "Description" => [
                                        [
                                            "_" => "Laptop Peripherals"
                                        ]
                                    ],
                                    "OriginCountry" => [
                                        [
                                            "IdentificationCode" => [
                                                [
                                                    "_" => "MYS"
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ],
                            "Price" => [
                                [
                                    "PriceAmount" => [
                                        [
                                            "_" => 17,
                                            "currencyID" => "MYR"
                                        ]
                                    ]
                                ]
                            ],
                            "ItemPriceExtension" => [
                                [
                                    "Amount" => [
                                        [
                                            "_" => 100,
                                            "currencyID" => "MYR"
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        // dd(json_encode($payload2, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES));
    }

    public function createLhdnLink($id){
        try {
            DB::beginTransaction();

            $order = Order::findOrFail($id);
            if (!$order) {
                throw new Exception('Order not found');
            }

            // Generate a unique hash for the URL
            $hash = Hash::make(uniqid() . $id);
            $hash = str_replace('/', '_', $hash); // Replace forward slashes for URL safety

            // Create the URL
            $baseUrl = config('app.url');
            $url = $baseUrl . '/lhdn/form/' . $hash;

            $lhdnLink = LhdnLink::create([
                'order_id' => $id,
                'url' => $url,
                'qr_id' => $hash,
                'lhdn_server' => 'staging'
            ]);

            DB::commit();
            return $lhdnLink;

        } catch (Exception $e) {
            DB::rollBack();
            Log::error('Error creating LHDN link: ' . $e->getMessage());
            throw $e;
        }
    }

    public function showForm($hash)
    {
        try {
            // Find the LHDN link record
            $lhdnLink = LhdnLink::where('qr_id', $hash)->firstOrFail();

            // Get the order details
            $order = Order::findOrFail($lhdnLink->order_id);

            // Get company details
            $company = $order->company;
            $lhdnDetails = $company->lhdn;

            // check if order already has lhdn submission
            $orderLhdn = OrderLhdn::where('order_id', $lhdnLink->order_id)->first();
            if($orderLhdn){
                // return redirect()->route('lhdn.success', ['hash' => $hash]);
                $document = $this->getDocumentStatus($orderLhdn, $hash);

                // dd($document);

                if($document['success'] ?? false){
                    // update status in orderLhdn. loop $document['data']['documentSummary'] and update status
                    $documentSummary = $document['data']['documentSummary'] ?? [];
                    foreach($documentSummary as $doc){
                        if(isset($doc['status'])){
                            $orderLhdn->status = $doc['status'];
                            $orderLhdn->save();
                        }
                    }

                    $invoiceUrl = isset($doc['uuid'], $doc['longId']) ?
                        "{$this->stagingUrlNonApis}/{$doc['uuid']}/share/{$doc['longId']}" : '';

                    return view('backend.integration.lhdn.lhdn_success', [
                        'seller_name' => $lhdnDetails->name ?? '',
                        'transaction_date' => $order->order_date ?? '',
                        'order_no' => $order->bizapp_temp_id ?? '',
                        'invoice_no' => $order->invoice_no ?? '',
                        'amount' => number_format($order->grandtotal_decimal ?? 0, 2),
                        'uuid' => $orderLhdn->uuid ?? '', // From LHDN response
                        'submitted_at' => now()->format('d/m/Y h:i A'),
                        'status' => $orderLhdn->status ?? '', // Or get from response
                        'myinvoice_url' => $invoiceUrl // URL for MyInvoice system
                    ]);
                }
            }

            // Prepare data for the view
            $data = [
                'seller_name' => $lhdnDetails->name ?? '',
                'transaction_date' => $order->order_date ?? '',
                'order_no' => $order->bizapp_temp_id ?? '',
                'invoice_no' => $order->invoice_no ?? '',
                'amount' => number_format($order->grandtotal_decimal, 2),
            ];

            return view('backend.integration.lhdn.lhdn_form', array_merge($data, ['hash' => $hash]));

        } catch (Exception $e) {
            Log::error('Error showing LHDN form: ' . $e->getMessage());
            abort(404);
        }
    }

    public function processForm(Request $request)
    {
        try {
            DB::beginTransaction();

            // dd($request->all());

            // Validate the request
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'email' => 'required|email|max:255',
                'classification' => 'required|in:INDIVIDUAL,BUSINESS',
                // 'identity' => 'required|string|max:255',
            ]);

            // Find the LHDN link record
            $lhdnLink = LhdnLink::where('qr_id', $request->hash)->firstOrFail();

            // Get the order
            $order = Order::findOrFail($lhdnLink->order_id); // findOrFail already handles null case
            $company = $order->company ?? null; // Handle null company relationship
            $lhdnDetails = $company?->lhdn ?? null; // Use null coalescing and optional chaining

            // Prepare customer data
            $customer = [
                'name' => $validated['name'] ?? '',
                'email' => $validated['email'] ?? '',
                'classification' => $validated['classification'] ?? '',
                'contact_no' => $request->contact_no ?? '',
                'ic_no' => $request->ic_no ?? '',
                'brn_no' => $request->brn_no ?? '',
                'sst_no' => $request->sst_no ?? '',
                'tin_no' => $request->tin_no ?? '',
                'address' => $request->address ?? '',
                'postcode' => $request->postcode ?? '',
                'city' => $request->city ?? '',
                'state' => $request->state ?? '',
                'country' => $request->country ?? '',
                'hash' => $request->hash,
            ];

            // Submit to LHDN
            $submitResponse = $this->submitLhdn($order->id, $customer);
            // dd($submitResponse);

            DB::commit();

            if(!$submitResponse['success']){
                throw new Exception($submitResponse['data'] ?? 'Failed to submit LHDN');
            }

            // Instead of redirect()->back()
            return view('backend.integration.lhdn.lhdn_success', [
                'seller_name' => $lhdnDetails->name ?? '',
                'transaction_date' => $order->order_date ?? '',
                'order_no' => $order->bizapp_temp_id ?? '',
                'invoice_no' => $order->invoice_no ?? '',
                'amount' => number_format($order->grandtotal_decimal, 2),
                'uuid' => $submitResponse['data']['uuid'], // From LHDN response
                'submitted_at' => now()->format('d/m/Y h:i A'),
                'status' => $submitResponse['data']['status'], // Or get from response
                // 'myinvoice_url' => $myinvoice_url // URL for MyInvoice system
            ]);

        } catch (ValidationException $e) {
            return redirect()->back()->withErrors($e->errors())->withInput();
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('Error processing LHDN form: ' . $e->getMessage());
            return redirect()->back()->with('error', $e->getMessage());
        }
    }

    // Add a new method for downloading invoice
    public function downloadInvoice($uuid)
    {
        // Implement invoice download logic here
        // Return file download response
    }

    public function getDocumentStatus(?OrderLhdn $orderLhdn = null, ?string $hash = null){
        try{
            $lhdnDetails = null;
            $lhdnToken = null;
            // if(!$lhdn){
            //     throw new Exception('LHDN not found.');
            // }

            $lhdnDetails = $orderLhdn?->companylhdn;
            $lhdnToken = $lhdnDetails?->companylhdntoken;

            // dd($orderLhdn);
            if (!$lhdnToken) {
                throw new Exception('LHDN details not found.');
            }

            $this->checkToken($hash, $lhdnDetails);

            $response = Http::withToken($lhdnToken->access_token)
                ->get("{$this->stagingUrl}/api/v1.0/documentsubmissions/{$orderLhdn->submission_uuid}", [
                    'pageNo' => 1,
                    'pageSize' => 20
                ])->throw();

            $data = $response->json();

            // dd($data);
            return [
                'success' => true,
                'data' => $data
            ];

        }catch(Exception $e){
            Log::error('Error getting document: ' . $e->getMessage());
            return [
                'success' => false,
                'data' => 'Error getting document'
            ];
            // throw $e;
        }
    }

}
