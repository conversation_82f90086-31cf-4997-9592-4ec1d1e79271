<?php

namespace App\Http\Controllers\backend;

use Carbon\Carbon;
use App\Models\Order;
use App\Models\Company;
use App\Models\Employee;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Http\Request;
use App\Models\CompanyBranch;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Validator;

class OrderHistoryController extends Controller
{
    public function index()
    {
        $order_status = 'all'; // Default to showing all orders
        return view('backend.order.index', compact('order_status'));
    }

    public function data(Request $request){
        $from_date = (empty($request->from_date)) ? Carbon::now()->startOfMonth()->format('Y-m-d') : $request->from_date;
        $to_date = (empty($request->to_date)) ? Carbon::now()->endOfMonth()->format('Y-m-d') : $request->to_date;
        $order_status = $request->order_status ?? 'all';

        $company_id = '';
        $company = Company::where('user_id',auth()->user()->id)->first();
        if($company){
            $company_id = $company->id;
        } else {
            $emp = Employee::where('user_id',auth()->user()->id)->first();
            $company_id = $emp->company_id;
        }

        $query = Order::query();

        // Handle order status filter
        if ($order_status === 'all') {
            // Show both active and cancelled orders
            $query->withTrashed();
        } else if ($order_status === 'cancelled') {
            // Show only cancelled orders
            $query->onlyTrashed();
        }
        // For 'active', we don't need to do anything as it's the default (no withTrashed)

        $query->select(
            'orders.id',
            'orders.user_id',
            'orders.order_date',
            'orders.customer_name',
            'orders.bizapp_order_id',
            'orders.bizapp_temp_id',
            'orders.grandtotal',
            'orders.created_at',
            'orders.order_label',
            'orders.grandtotal_decimal',
            'orders.bizapp_temp_id',
            'orders.order_date',
            'orders.deleted_at',
            'orders.order_notes'
        );
        $query->withCount('orderDetails as total_items');

        // Add left join with lhdn_links table
        $query->leftJoin('lhdn_links', 'orders.id', '=', 'lhdn_links.order_id')
              ->addSelect([
                  'lhdn_links.url as lhdn_url',
                  'lhdn_links.qr_id as lhdn_qr_id',
                  'lhdn_links.lhdn_server'
              ]);

        $query->where(function ($q) use ($company_id) {
            if (auth()->user()->companies) {
                // If the user is a company (not an employee), use company_id
                $q->where('company_id', $company_id);
            } else {
                // If the user is an employee, use user_id
                $q->where('user_id', auth()->user()->id);
            }
        });

        $query->when($request->search, function($q) use($request){
            $q->where('bizapp_order_id', 'like', '%' . $request->search . '%');
            $q->where('bizapp_temp_id', 'like', '%' . $request->search . '%');
        });
        $query->where('order_date', '>=', $from_date . ' 00:00:00');
        $query->where('order_date', '<=', $to_date . ' 23:59:59');

        $query->orderBy('order_date', 'desc');

        $list = $query->paginate($request->show ?? 10);

        // Check if user is HQ or staff
        $isHQ = auth()->user()->companies ? true : false;

        return view('backend.order.list', compact('list', 'order_status', 'isHQ'));
    }

    public function view(Request $request, $id){
        $order = Order::with('orderDetails','user')->where('id', $id)->first();
        if(!$order){
            abort(404);
        }
        $user = auth()->user();
        $currency = $user->userDetails->currency ?? 'RM';

        return view('backend.order.view', compact('order','user','currency'));
    }

    public function viewDetail($id)
    {
        $getOrder = Order::findOrFail($id);

        $urlPos = config('bizappos.bizappos_api_url');
        $getOrderAPI = Http::asForm()->post($urlPos . 'api_name=TRACK_RESITSECGST_THERMAL_NEW_X2&TX=122',[
            'DOMAIN' => auth()->user()->domain,
            'pid' => auth()->user()->pid,
            'orderid' => $getOrder->bizapp_user_id, //'120274941',
            'untuksiapa' => 'UNTUKPELANGGAN',
            'TOKEN' => 'aa'
        ])->throw()->json();

        // get total discounts from cart
        $totalDiscount = 0;
        foreach ($getOrderAPI[0] as $data) {
            for ($i = 1; $i <= 20; $i++) {
                $key = "pos_diskaunamount_rm_" . $i;

                if (isset($data[$key]) && is_numeric($data[$key])) {
                    $totalDiscount += $data[$key];
                }
            }
        }

        // store productname,price,and quantitiy in seperate array
        $productNameAndQuantity = [];
        foreach ($getOrderAPI as $data) {
            $filteredItem = [];

            foreach ($data as $key => $value) {
                // Check if the key starts with 'productname' or 'quantity' and the value is not empty
                if ((str_starts_with($key, 'productname') || str_starts_with($key, 'quantity') || str_starts_with($key, 'price')) && $value !== '') {
                    $filteredItem[$key] = $value;
                }
            }
            $productNameAndQuantity[] = $filteredItem;
        }
        // dd($productNameAndQuantity[0]);

        // get currenct
        $currency = auth()->user()->userDetails->currency ?? 'RM';
        // dd($currency);

        return view('backend.order-history-view')
        ->with('orderData',$getOrderAPI[0])
        ->with('productList',$productNameAndQuantity[0])
        ->with('currency',$currency)
        ->with('totalDiscount',$totalDiscount);
    }


    public function getAllOrderPaymentMethod($id)
    {

        // Convert the dates to the correct format
        $startDate = date('Y-m-d H:i:s', strtotime('2023-12-01 00:00:00'));
        $endDate = date('Y-m-d H:i:s', strtotime('2023-12-06 00:00:00'));
        $orders = Order::where('user_id', '1cc4fb57-4a45-4b36-a6f8-8892606b1702')->where(function($query) use ($startDate, $endDate){
            $query->whereBetween('created_at', [$startDate,$endDate]);
          })->get();

        $getHq = Company::where('user_id', $id)->first();
        $staffs = CompanyBranch::where('branch_of',$getHq->id)->get();
        // if($staffs){
        //     foreach ($staffs as $staff) {
        //         // $staff->employee->user_id
        //         $orders = $orders->concat(Order::where('user_id', '1cc4fb57-4a45-4b36-a6f8-8892606b1702')->where(function($query) use ($startDate, $endDate){
        //             $query->whereBetween('created_at', [$startDate,$endDate]);
        //           })->get());
        //     }
        // }


        // get list of payment method from $orders with its corresponding amount

        $paymentMethod = [];
        foreach ($orders as $order) {
            if (!in_array($order->pay_method, $paymentMethod)) {
                // $orderCreatedAt = \Carbon\Carbon::parse($order->order_date);
                // $startDate = \Carbon\Carbon::parse('2023-12-01 00:00:00');  // Replace with the actual start date
                // $endDate = \Carbon\Carbon::parse('2023-12-06 00:00:00');

                    $paymentMethod[] = $order;

            }

        }

        // put $orders into array with key as user_id and value as $paymentMethod
        $ordersByPaymentMethod = [];
        foreach ($orders as $order) {
            $ordersByPaymentMethod[$order->user_id][] = $paymentMethod;
        }

        // dd($ordersByPaymentMethod);

        $orders2 = Order::where('user_id', '1cc4fb57-4a45-4b36-a6f8-8892606b1702')
        ->where(function($query) use ($startDate, $endDate){
            $query->whereBetween('created_at', [$startDate, $endDate]);
        })
        ->select('pay_method', DB::raw('count(*) as total'), DB::raw('sum(CAST(grandtotal AS DECIMAL(10,2))) as grand_total_sum'))
        ->groupBy('pay_method')
        ->get();


        foreach ($orders2 as $order) {
            echo "Payment Method: " . $order->pay_method . ", Count: " . $order->total . ", Grand Total: " . $order->grand_total_sum . "\n";
        }
    }

    public function downloadPDF($id)
    {
        $order = Order::with('orderDetails')->findOrFail($id);
        // Add authorization check
        if (auth()->user()->companies) {
            // For HQ users, check company_id
            if ($order->company_id !== auth()->user()->companies->id) {
                abort(403);
            }
        } else {
            // For employees, check user_id
            if ($order->user_id !== auth()->user()->id) {
                abort(403);
            }

            // For staff users, modify the order's user to be the current user
            // This ensures the receipt shows the staff's name instead of HQ's name
            $order->user = auth()->user();
        }

        $user = auth()->user();
        $company = $user->companies ?? $user->employee->company;

        $currency = $user->userDetails->currency ?? 'RM';

        $pdf = Pdf::loadView('backend.order.receipt-pdf', compact('order', 'user', 'currency','company'))
            ->setOption('isPhpEnabled', true)
            ->setOption('isRemoteEnabled', true)
            ->setPaper('a4', 'portrait');

        // For quick browser preview instead of download:
        return $pdf->stream('receipt.pdf');

        // OR save to file for inspection:
        // $pdf->save(storage_path('temp/receipt.pdf'));
        // return response()->file(storage_path('temp/receipt.pdf'));
    }

    /**
     * Soft delete (cancel) an order
     */
    public function deleteOrderHistory(Request $request, $id)
    {
        // Validate the request
        $validator = \Illuminate\Support\Facades\Validator::make($request->all(), [
            'cancellation_reason' => 'required|string|min:3',
        ]);

        if ($validator->fails()) {
            if ($request->ajax()) {
                return response()->json([
                    'message' => 'Please provide a valid cancellation reason.',
                    'errors' => $validator->errors()
                ], 422);
            }
            return redirect()->back()->withErrors($validator)->withInput();
        }

        try {
            $order = Order::findOrFail($id);

            // Only HQ users can cancel orders
            if (!auth()->user()->companies) {
                if ($request->ajax()) {
                    return response()->json([
                        'message' => 'Only HQ users are authorized to cancel orders. Please contact your administrator.'
                    ], 403);
                }
                return redirect()->route('order.history')
                    ->with('error', 'Only HQ users are authorized to cancel orders. Please contact your administrator.');
            }

            // For HQ users, check company_id
            if ($order->company_id !== auth()->user()->companies->id) {
                if ($request->ajax()) {
                    return response()->json([
                        'message' => 'You are not authorized to cancel this order.'
                    ], 403);
                }
                abort(403, 'You are not authorized to cancel this order.');
            }

            // Format the cancellation reason
            $cancellationReason = 'CANCEL: ' . $request->cancellation_reason;

            // Check if user is a Bizapp user - if so, cancel in Bizapp first
            if (auth()->user()->isBizappUser == 'Y') {
                try {
                    $urlPos = config('bizappos.bizappos_api_url');

                    Log::info('Attempting to cancel order in Bizapp system', [
                        'order_id' => $order->id,
                        'bizapp_order_id' => $order->bizapp_order_id,
                        'user_pid' => auth()->user()->pid
                    ]);

                    // Make the API call to Bizapp
                    $cancelOrder = Http::asForm()
                        ->timeout(30) // Set a reasonable timeout
                        ->post($urlPos . 'api_name=TRACK_UPDATETRACKINGNO&TX=', [
                            'pid' => auth()->user()->pid,
                            'orderid' => $order->bizapp_order_id,
                            'DOMAIN' => auth()->user()->domain,
                            'scannedKey' => 'CBS',
                            'reason' => $request->cancellation_reason
                        ])
                        ->throw() // This will throw an exception for 4xx/5xx responses
                        ->json();

                    Log::info('Bizapp cancel order API successful', [
                        'order_id' => $order->id,
                        'response' => $cancelOrder
                    ]);

                } catch (\Illuminate\Http\Client\ConnectionException $e) {
                    // Handle connection errors (timeout, server unreachable, etc.)
                    Log::error('Bizapp API connection error during order cancellation', [
                        'order_id' => $order->id,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);

                    $errorMessage = 'Unable to cancel the order due to connectivity issues with the Bizapp system. Please try again later when the service is available.';

                    if ($request->ajax()) {
                        return response()->json([
                            'message' => $errorMessage,
                            'error_type' => 'connection'
                        ], 503); // Service Unavailable
                    }

                    return redirect()->route('order.history')
                        ->with('error', $errorMessage);

                } catch (\Illuminate\Http\Client\RequestException $e) {
                    // Handle HTTP errors (400, 500, etc.)
                    Log::error('Bizapp API request error during order cancellation', [
                        'order_id' => $order->id,
                        'status' => $e->getCode(),
                        'response' => $e->response?->body(),
                        'error' => $e->getMessage()
                    ]);

                    $errorMessage = 'The Bizapp system rejected the cancellation request. Please contact support for assistance.';

                    if ($request->ajax()) {
                        return response()->json([
                            'message' => $errorMessage,
                            'error_type' => 'api_error',
                            'details' => $e->getMessage()
                        ], 502); // Bad Gateway
                    }

                    return redirect()->route('order.history')
                        ->with('error', $errorMessage);

                } catch (\Exception $e) {
                    // Handle any other unexpected errors
                    Log::error('Unexpected error during Bizapp order cancellation', [
                        'order_id' => $order->id,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);

                    $errorMessage = 'An unexpected error occurred while communicating with the Bizapp system. Please try again later or contact support.';

                    if ($request->ajax()) {
                        return response()->json([
                            'message' => $errorMessage,
                            'error_type' => 'unexpected',
                            'details' => $e->getMessage()
                        ], 500);
                    }

                    return redirect()->route('order.history')
                        ->with('error', $errorMessage);
                }
            }

            // If we've reached this point, either:
            // 1. The user is not a Bizapp user, or
            // 2. The Bizapp API call was successful

            // Now proceed with the local cancellation

            // Update order notes with cancellation reason
            $order->order_notes = empty($order->order_notes)
                ? $cancellationReason
                : $cancellationReason . ' || ' . $order->order_notes;

            // Update status and save notes
            $order->status = 'CANCELLED';
            $order->save();

            // Soft delete the order
            $order->delete();

            if ($request->ajax()) {
                return response()->json([
                    'message' => 'Order has been cancelled successfully'
                ], 200);
            }

            return redirect()->route('order.history')->with('success', 'Order has been cancelled successfully');

        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Order cancellation error: ' . $e->getMessage());

            if ($request->ajax()) {
                return response()->json([
                    'message' => 'An error occurred while cancelling the order: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->route('order.history')
                ->with('error', 'An error occurred while cancelling the order: ' . $e->getMessage());
        }
    }
}
