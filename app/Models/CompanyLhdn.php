<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Traits\UUID;


class CompanyLhdn extends Model
{
    use HasFactory, UUID, SoftDeletes;
    protected $guarded = [];

    protected $casts = [
        'token_expire' => 'datetime',
    ];

    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    public function companylhdntoken(){
        return $this->hasOne(CompanyLhdnToken::class);
    }

    public function orderlhdn(){
        return $this->hasMany(OrderLhdn::class);
    }

    public function lhdnMsicCode(){
        return $this->belongsTo(LhdnMsicCode::class);
    }
}
