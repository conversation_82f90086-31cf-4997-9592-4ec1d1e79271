<?php

namespace App\Models;

use App\Traits\UUID;
use App\Enums\ProductEnums;
use App\Models\Uom;
use App\Models\ProductDetail;
use App\Models\ProductsCategory;
use App\Models\CollectionProduct;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\ProductVariantAttribute;
class Product extends Model
{
    use HasFactory, UUID, SoftDeletes;

    protected $fillable = [
        'parent_company',
        'branch_id',
        'product_id_bizapp',
        'product_name',
        'product_SKU',
        'product_brand',
        'product_description',
        'product_note',
        'uom_id',
        'product_stock',
        'min_quantity',
        'step_quantity',
        'product_stock_status',
        'product_price',
        'cost_price',
        'product_weight',
        'product_fav',
        'product_status',
        'isComposite',
        'material_ids',
        'category_id',
        'isVariant',
        'product_lhdn_code_id'
        // 'uom_id',
        // 'uom_price',
        // 'stock_quantity',
        // 'reorder_level',
        // 'active'
    ];

    protected $casts = [
        'material_ids' => 'array',
        // 'uom_price' => 'decimal:2',
        'min_quantity' => 'decimal:3',
        'step_quantity' => 'decimal:3',
        // 'stock_quantity' => 'decimal:3',
        // 'reorder_level' => 'decimal:3',
        // 'cost_price' => 'decimal:2',
        // 'active' => 'boolean'
    ];

    protected $appends = ['product_attachment', 'uom_name', 'uom_code', 'uom_type'];

    public function getProductAttachmentAttribute()
    {
        return $this->productDetail?->product_attachment ?? null;
        // return $this->productDetail?->where('product_id', $this->id)->where('product_variant_attribute_id', null)->value('product_attachment') ?? null;
    }

    public function getUomCodeAttribute()
    {
        return $this->uom->uom_code ?? null;
    }

    public function getUomNameAttribute()
    {
        return $this->uom->uom_name ?? null;
    }

    public function getUomTypeAttribute()
    {
        return $this->uom->uom_type ?? null;
    }

    public function companyOf()
    {
        return $this->belongsTo(Company::class,'parent_company');
    }

    public function productDetail()
    {
        return $this->hasOne(ProductDetail::class);
    }

    public function category(){
        return $this->belongsTo(ProductsCategory::class);
    }

    public function collectionProducts()
    {
        return $this->hasMany(CollectionProduct::class);
    }

    public function variants()
    {
        return $this->hasMany(ProductVariantAttribute::class);
    }

    public function productLhdnCode()
    {
        return $this->belongsTo(ProductLhdnCode::class);
    }

    public function uom()
    {
        return $this->belongsTo(Uom::class);
    }

    // public function scopeActive($query)
    // {
    //     return $query->where('active', true);
    // }

    // public function scopeLowStock($query)
    // {
    //     return $query->whereColumn('stock_quantity', '<=', 'reorder_level');
    // }

    // public function getEffectivePrice()
    // {
    //     return $this->uom_price ?? $this->product_price;
    // }

    // public function hasStock($requestedQuantity = 1)
    // {
    //     return $this->stock_quantity >= $requestedQuantity;
    // }
}
