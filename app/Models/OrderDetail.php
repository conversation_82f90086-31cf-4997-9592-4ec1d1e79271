<?php

namespace App\Models;

use App\Traits\UUID;
use App\Models\Uom;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class OrderDetail extends Model
{
    use HasFactory, UUID, SoftDeletes;

    protected $fillable = [
        'order_id',
        'product_SKU',
        'product_price',
        'product_price_decimal',
        'product_qty',
        'product_discount_amount',
        'product_discount_amount_decimal',
        'product_id',
        'productName',
        'bizapp_product_id',
        'uom_code',
        // New UOM and snapshot fields / Medan baharu UOM dan snapshot
        'uom_id',
        // 'product_name',
        // 'product_sku',
        'uom',
        // 'unit_price',
        // 'quantity',
        'product_total_price',
        // 'active'
        'product_variant_attribute_id',
        'product_parent_name'

    ];

    protected $casts = [
        // Cast decimal fields / Tukar medan decimal
        // 'unit_price' => 'decimal:2',
        // 'quantity' => 'decimal:3',
        // 'line_total' => 'decimal:2',
        // 'active' => 'boolean'
    ];

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function productVariant()
    {
        return $this->belongsTo(ProductVariantAttribute::class, 'product_variant_attribute_id', 'id');
    }

    // UOM relationship / Hubungan UOM
    public function uom()
    {
        return $this->belongsTo(Uom::class);
    }

    // Order relationship / Hubungan pesanan
    public function order()
    {
        return $this->belongsTo(Order::class);
    }


    // Scope for active order details / Skop untuk butiran pesanan aktif
    // public function scopeActive($query)
    // {
    //     return $query->where('active', true);
    // }

    // Helper method to calculate line total / Kaedah pembantu untuk mengira jumlah baris
    // public function calculateLineTotal()
    // {
    //     return $this->quantity * $this->unit_price;
    // }

    // Helper method to get effective quantity / Kaedah pembantu untuk mendapatkan kuantiti berkesan
    // public function getEffectiveQuantity()
    // {
    //     return $this->quantity ?? $this->product_qty;
    // }

    // Helper method to get effective price / Kaedah pembantu untuk mendapatkan harga berkesan
    // public function getEffectivePrice()
    // {
    //     return $this->unit_price ?? $this->product_price;
    // }
}
