<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class ProductVariantAttribute extends Model
{
    use HasFactory, HasUuids, SoftDeletes;

    protected $fillable = ['product_id', 'product_id_bizapp' ,'product_name', 'product_sku', 'product_stock', 'uom_id', 'min_quantity', 'step_quantity', 'product_price', 'product_status', 'product_stock_status', 'product_weight', 'product_attachment', 'product_lhdn_code_id'];


    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function productDetail()
    {
        return $this->hasOne(ProductDetail::class,  'product_variant_attribute_id');
    }

    public function compositeAttributeValues()
    {
        return $this->hasMany(CompositeAttributeValue::class);
    }

    public function productLhdnCode()
    {
        return $this->belongsTo(ProductLhdnCode::class);
    }

    public function uom()
    {
        return $this->belongsTo(Uom::class);
    }
}
